import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_theme.dart';
import '../presentation/widgets/animated_theme_wrapper.dart';

/// 主题服务 - 负责主题持久化和管理
///
/// 功能包括：
/// - 主题模式持久化（亮色/暗色/自动）
/// - 主题世界持久化
/// - 主题动画过渡
/// - 无障碍支持
/// 更新时间：2024-12-19 UTC
class ThemeService extends ChangeNotifier {
  static const String _themePrefsKey = 'app_theme_mode';
  static const String _themeWorldPrefsKey = 'app_theme_world';
  static const String _highContrastPrefsKey = 'app_high_contrast';
  static const String _animatedTransitionsPrefsKey = 'app_animated_transitions';
  static const String _animationTypePrefsKey = 'app_animation_type';
  static const String _animationDurationPrefsKey = 'app_animation_duration';

  final Logger _logger = Logger();
  SharedPreferences? _prefs;

  // 主题状态
  ThemeMode _themeMode = ThemeMode.system;
  String _themeWorld = 'default';
  bool _highContrastMode = false;
  bool _animatedTransitions = true;
  AnimationType _animationType = AnimationType.fade;
  Duration _animationDuration = const Duration(milliseconds: 300);

  // 主题缓存
  final Map<String, ThemeData> _lightThemeCache = {};
  final Map<String, ThemeData> _darkThemeCache = {};

  // Getters
  ThemeMode get themeMode => _themeMode;
  String get themeWorld => _themeWorld;
  bool get highContrastMode => _highContrastMode;
  bool get animatedTransitions => _animatedTransitions;
  AnimationType get animationType => _animationType;
  Duration get animationDuration => _animationDuration;

  /// 初始化主题服务
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadThemePreferences();
      _logger.i(
        'ThemeService initialized with mode: $_themeMode, world: $_themeWorld, animation: $_animationType',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to initialize ThemeService',
        error: e,
        stackTrace: stackTrace,
      );
      // 使用默认值
    }
  }

  /// 加载主题偏好设置
  Future<void> _loadThemePreferences() async {
    if (_prefs == null) return;

    // 加载主题模式
    final themeModeIndex = _prefs!.getInt(_themePrefsKey);
    if (themeModeIndex != null && themeModeIndex < ThemeMode.values.length) {
      _themeMode = ThemeMode.values[themeModeIndex];
    }

    // 加载主题世界
    _themeWorld = _prefs!.getString(_themeWorldPrefsKey) ?? 'default';

    // 加载无障碍设置
    _highContrastMode = _prefs!.getBool(_highContrastPrefsKey) ?? false;
    _animatedTransitions =
        _prefs!.getBool(_animatedTransitionsPrefsKey) ?? true;

    // 加载动画设置
    final animationTypeIndex = _prefs!.getInt(_animationTypePrefsKey);
    if (animationTypeIndex != null &&
        animationTypeIndex < AnimationType.values.length) {
      _animationType = AnimationType.values[animationTypeIndex];
    }

    final animationDurationMs = _prefs!.getInt(_animationDurationPrefsKey);
    if (animationDurationMs != null && animationDurationMs > 0) {
      _animationDuration = Duration(milliseconds: animationDurationMs);
    }

    _logger.d(
      'Loaded theme preferences: mode=$_themeMode, world=$_themeWorld, highContrast=$_highContrastMode, animation=$_animationType',
    );
  }

  /// 设置主题模式
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    await _saveThemeMode();
    notifyListeners();

    _logger.i('Theme mode changed to: $mode');
  }

  /// 设置高对比度模式
  Future<void> setHighContrastMode(bool enabled) async {
    if (_highContrastMode == enabled) return;

    _highContrastMode = enabled;
    await _saveHighContrastMode();
    _clearThemeCache();
    notifyListeners();

    _logger.i('High contrast mode changed to: $enabled');
  }

  /// 设置主题世界
  Future<void> setThemeWorld(String worldId) async {
    if (_themeWorld == worldId) return;

    _themeWorld = worldId;
    await _saveThemeWorld();

    // 清除缓存以使用新的主题世界
    _clearThemeCache();
    notifyListeners();

    _logger.i('Theme world changed to: $worldId');
  }

  /// 切换高对比度模式
  Future<void> toggleHighContrastMode() async {
    _highContrastMode = !_highContrastMode;
    await _saveHighContrastMode();

    // 清除缓存以应用高对比度设置
    _clearThemeCache();
    notifyListeners();

    _logger.i(
      'High contrast mode ${_highContrastMode ? 'enabled' : 'disabled'}',
    );
  }

  /// 切换动画过渡
  Future<void> toggleAnimatedTransitions() async {
    _animatedTransitions = !_animatedTransitions;
    await _saveAnimatedTransitions();
    notifyListeners();

    _logger.i(
      'Animated transitions ${_animatedTransitions ? 'enabled' : 'disabled'}',
    );
  }

  /// 设置动画类型
  Future<void> setAnimationType(AnimationType type) async {
    if (_animationType == type) return;

    _animationType = type;
    await _saveAnimationType();
    notifyListeners();

    _logger.i('Animation type changed to: $type');
  }

  /// 设置动画持续时间
  Future<void> setAnimationDuration(Duration duration) async {
    if (_animationDuration == duration) return;

    _animationDuration = duration;
    await _saveAnimationDuration();
    notifyListeners();

    _logger.i('Animation duration changed to: ${duration.inMilliseconds}ms');
  }

  /// 应用动画配置预设
  Future<void> applyAnimationConfig(ThemeAnimationConfig config) async {
    bool changed = false;

    if (_animationType != config.type) {
      _animationType = config.type;
      await _saveAnimationType();
      changed = true;
    }

    if (_animationDuration != config.duration) {
      _animationDuration = config.duration;
      await _saveAnimationDuration();
      changed = true;
    }

    if (_animatedTransitions != config.enabled) {
      _animatedTransitions = config.enabled;
      await _saveAnimatedTransitions();
      changed = true;
    }

    if (changed) {
      notifyListeners();
      _logger.i(
        'Applied animation config: ${config.type}, ${config.duration.inMilliseconds}ms, enabled: ${config.enabled}',
      );
    }
  }

  /// 获取亮色主题
  ThemeData getLightTheme() {
    final cacheKey = '${_themeWorld}_${_highContrastMode ? 'hc' : 'normal'}';

    if (_lightThemeCache.containsKey(cacheKey)) {
      return _lightThemeCache[cacheKey]!;
    }

    ThemeData theme;
    if (_themeWorld == 'default') {
      theme = AppTheme.lightTheme;
    } else {
      theme = AppTheme.getThemeWorldTheme(_themeWorld, isDark: false);
    }

    // 应用高对比度设置
    if (_highContrastMode) {
      theme = _applyHighContrastMode(theme, false);
    }

    _lightThemeCache[cacheKey] = theme;
    return theme;
  }

  /// 获取暗色主题
  ThemeData getDarkTheme() {
    final cacheKey = '${_themeWorld}_${_highContrastMode ? 'hc' : 'normal'}';

    if (_darkThemeCache.containsKey(cacheKey)) {
      return _darkThemeCache[cacheKey]!;
    }

    ThemeData theme;
    if (_themeWorld == 'default') {
      theme = AppTheme.darkTheme;
    } else {
      theme = AppTheme.getThemeWorldTheme(_themeWorld, isDark: true);
    }

    // 应用高对比度设置
    if (_highContrastMode) {
      theme = _applyHighContrastMode(theme, true);
    }

    _darkThemeCache[cacheKey] = theme;
    return theme;
  }

  /// 应用高对比度模式
  ThemeData _applyHighContrastMode(ThemeData theme, bool isDark) {
    if (!_highContrastMode) return theme;

    // 高对比度色彩
    final backgroundColor = isDark ? Colors.black : Colors.white;
    final foregroundColor = isDark ? Colors.white : Colors.black;
    final primaryColor = isDark ? Colors.white : Colors.black;

    return theme.copyWith(
      scaffoldBackgroundColor: backgroundColor,
      canvasColor: backgroundColor,
      cardColor: backgroundColor,

      // 高对比度颜色方案
      colorScheme: theme.colorScheme.copyWith(
        primary: primaryColor,
        onPrimary: backgroundColor,
        secondary: primaryColor,
        onSecondary: backgroundColor,
        surface: backgroundColor,
        onSurface: foregroundColor,
      ),

      // 高对比度文本主题
      textTheme: theme.textTheme.apply(
        bodyColor: foregroundColor,
        displayColor: foregroundColor,
      ),

      // 高对比度应用栏
      appBarTheme: theme.appBarTheme.copyWith(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        iconTheme: IconThemeData(color: foregroundColor),
      ),

      // 高对比度按钮
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: foregroundColor,
          foregroundColor: backgroundColor,
          side: BorderSide(color: foregroundColor, width: 2),
        ),
      ), dialogTheme: DialogThemeData(backgroundColor: backgroundColor),
    );
  }

  /// 保存主题模式
  Future<void> _saveThemeMode() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setInt(_themePrefsKey, _themeMode.index);
    } catch (e) {
      _logger.w('Failed to save theme mode: $e');
    }
  }

  /// 保存主题世界
  Future<void> _saveThemeWorld() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setString(_themeWorldPrefsKey, _themeWorld);
    } catch (e) {
      _logger.w('Failed to save theme world: $e');
    }
  }

  /// 保存高对比度设置
  Future<void> _saveHighContrastMode() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setBool(_highContrastPrefsKey, _highContrastMode);
    } catch (e) {
      _logger.w('Failed to save high contrast mode: $e');
    }
  }

  /// 保存动画过渡设置
  Future<void> _saveAnimatedTransitions() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setBool(_animatedTransitionsPrefsKey, _animatedTransitions);
    } catch (e) {
      _logger.w('Failed to save animated transitions: $e');
    }
  }

  /// 保存动画类型
  Future<void> _saveAnimationType() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setInt(_animationTypePrefsKey, _animationType.index);
    } catch (e) {
      _logger.w('Failed to save animation type: $e');
    }
  }

  /// 保存动画持续时间
  Future<void> _saveAnimationDuration() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setInt(
        _animationDurationPrefsKey,
        _animationDuration.inMilliseconds,
      );
    } catch (e) {
      _logger.w('Failed to save animation duration: $e');
    }
  }

  /// 清除主题缓存
  void _clearThemeCache() {
    _lightThemeCache.clear();
    _darkThemeCache.clear();
    _logger.d('Theme cache cleared');
  }

  /// 重置所有主题设置
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _themeWorld = 'default';
    _highContrastMode = false;
    _animatedTransitions = true;
    _animationType = AnimationType.fade;
    _animationDuration = const Duration(milliseconds: 300);

    if (_prefs != null) {
      await Future.wait([
        _prefs!.remove(_themePrefsKey),
        _prefs!.remove(_themeWorldPrefsKey),
        _prefs!.remove(_highContrastPrefsKey),
        _prefs!.remove(_animatedTransitionsPrefsKey),
        _prefs!.remove(_animationTypePrefsKey),
        _prefs!.remove(_animationDurationPrefsKey),
      ]);
    }

    _clearThemeCache();
    notifyListeners();

    _logger.i('Theme settings reset to defaults');
  }

  /// 获取主题统计信息
  Map<String, dynamic> getThemeStats() {
    return {
      'themeMode': _themeMode.toString(),
      'themeWorld': _themeWorld,
      'highContrastMode': _highContrastMode,
      'animatedTransitions': _animatedTransitions,
      'animationType': _animationType.toString(),
      'animationDuration': '${_animationDuration.inMilliseconds}ms',
      'lightCacheSize': _lightThemeCache.length,
      'darkCacheSize': _darkThemeCache.length,
    };
  }
}

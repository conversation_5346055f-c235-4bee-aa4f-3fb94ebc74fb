import 'package:logger/logger.dart';

/// 配置管理服务
/// 
/// 负责管理应用的各种配置参数，包括：
/// - 提示系统配置
/// - 游戏规则配置
/// - UI配置等
class ConfigService {
  static final ConfigService _instance = ConfigService._internal();
  factory ConfigService() => _instance;
  ConfigService._internal();

  final Logger _logger = Logger();

  // 提示系统配置
  static const int _defaultHintCooldownSeconds = 30;
  static const int _defaultMaxHintsPerLevel = 3;
  static const int _defaultHintCostPoints = 10;

  // 游戏配置
  static const int _defaultMaxGameTimeMinutes = 30;
  static const int _defaultPerfectScoreThreshold = 100;

  // 成就配置
  static const int _defaultSkillPointsPerLevel = 100;

  /// 获取提示冷却时间（秒）
  int get hintCooldownSeconds {
    // 这里可以从本地存储、远程配置或用户设置中获取
    // 暂时返回默认值
    return _defaultHintCooldownSeconds;
  }

  /// 获取每关最大提示次数
  int get maxHintsPerLevel {
    return _defaultMaxHintsPerLevel;
  }

  /// 获取使用提示的点数消耗
  int get hintCostPoints {
    return _defaultHintCostPoints;
  }

  /// 获取游戏最大时间限制（分钟）
  int get maxGameTimeMinutes {
    return _defaultMaxGameTimeMinutes;
  }

  /// 获取完美分数阈值
  int get perfectScoreThreshold {
    return _defaultPerfectScoreThreshold;
  }

  /// 获取技能升级所需点数
  int get skillPointsPerLevel {
    return _defaultSkillPointsPerLevel;
  }

  /// 根据用户等级获取提示冷却时间
  int getHintCooldownForUserLevel(int userLevel) {
    // 高等级用户可以享受更短的冷却时间
    if (userLevel >= 10) {
      return (_defaultHintCooldownSeconds * 0.5).round(); // 50%冷却时间
    } else if (userLevel >= 5) {
      return (_defaultHintCooldownSeconds * 0.75).round(); // 75%冷却时间
    }
    return _defaultHintCooldownSeconds;
  }

  /// 根据谜题难度获取最大提示次数
  int getMaxHintsForDifficulty(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return _defaultMaxHintsPerLevel + 1; // 简单难度多一次提示
      case 'hard':
        return _defaultMaxHintsPerLevel - 1; // 困难难度少一次提示
      case 'expert':
        return 1; // 专家难度只有一次提示
      default:
        return _defaultMaxHintsPerLevel;
    }
  }

  /// 获取成就解锁所需的统计数据阈值
  Map<String, int> get achievementThresholds {
    return {
      'puzzles_completed_beginner': 1,
      'puzzles_completed_intermediate': 10,
      'puzzles_completed_advanced': 50,
      'puzzles_completed_expert': 100,
      'speed_master_threshold': 30, // 秒
      'lightning_fast_threshold': 15, // 秒
      'perfect_scores_threshold': 10,
      'no_hints_threshold': 10,
      'consecutive_threshold': 5,
      'skill_level_threshold': 5,
    };
  }

  /// 更新配置（从远程或本地存储）
  Future<void> updateConfig() async {
    try {
      _logger.d('Updating configuration...');
      
      // 这里可以从远程服务器或本地存储加载配置
      // 例如：
      // final remoteConfig = await _remoteConfigService.getConfig();
      // final localConfig = await _localStorageService.getConfig();
      
      _logger.i('Configuration updated successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to update configuration', error: e, stackTrace: stackTrace);
    }
  }

  /// 重置为默认配置
  void resetToDefaults() {
    _logger.i('Configuration reset to defaults');
    // 这里可以清除本地存储的配置，恢复默认值
  }

  /// 获取调试信息
  Map<String, dynamic> getDebugInfo() {
    return {
      'hintCooldownSeconds': hintCooldownSeconds,
      'maxHintsPerLevel': maxHintsPerLevel,
      'hintCostPoints': hintCostPoints,
      'maxGameTimeMinutes': maxGameTimeMinutes,
      'perfectScoreThreshold': perfectScoreThreshold,
      'skillPointsPerLevel': skillPointsPerLevel,
    };
  }
}

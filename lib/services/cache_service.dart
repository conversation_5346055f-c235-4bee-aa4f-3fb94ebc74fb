import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// 缓存管理服务
/// 
/// 负责管理应用的各种缓存数据，包括：
/// - 图片缓存
/// - 数据缓存
/// - 临时文件
/// - SharedPreferences缓存
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  final Logger _logger = Logger();

  /// 清除所有缓存
  Future<bool> clearAllCache() async {
    try {
      _logger.i('Starting to clear all cache...');
      
      bool success = true;
      
      // 清除SharedPreferences缓存（保留用户设置）
      success &= await _clearSharedPreferencesCache();
      
      // 清除临时文件缓存
      success &= await _clearTemporaryFiles();
      
      // 清除应用缓存目录
      success &= await _clearApplicationCache();
      
      // 清除图片缓存（如果使用了图片缓存库）
      success &= await _clearImageCache();
      
      if (success) {
        _logger.i('All cache cleared successfully');
      } else {
        _logger.w('Some cache clearing operations failed');
      }
      
      return success;
    } catch (e, stackTrace) {
      _logger.e('Failed to clear cache', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 清除SharedPreferences缓存（保留重要设置）
  Future<bool> _clearSharedPreferencesCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 保留的关键设置
      final keysToKeep = {
        'user_id',
        'theme_mode',
        'language_code',
        'first_launch',
        'privacy_accepted',
      };
      
      // 获取所有键
      final allKeys = prefs.getKeys();
      
      // 删除非关键缓存数据
      for (final key in allKeys) {
        if (!keysToKeep.contains(key)) {
          await prefs.remove(key);
        }
      }
      
      _logger.d('SharedPreferences cache cleared (kept ${keysToKeep.length} essential keys)');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Failed to clear SharedPreferences cache', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 清除临时文件
  Future<bool> _clearTemporaryFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      
      if (await tempDir.exists()) {
        await _deleteDirectoryContents(tempDir);
        _logger.d('Temporary files cleared');
      }
      
      return true;
    } catch (e, stackTrace) {
      _logger.e('Failed to clear temporary files', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 清除应用缓存目录
  Future<bool> _clearApplicationCache() async {
    try {
      // 获取应用缓存目录
      final cacheDir = await getApplicationCacheDirectory();
      
      if (await cacheDir.exists()) {
        await _deleteDirectoryContents(cacheDir);
        _logger.d('Application cache cleared');
      }
      
      return true;
    } catch (e, stackTrace) {
      _logger.e('Failed to clear application cache', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 清除图片缓存
  Future<bool> _clearImageCache() async {
    try {
      // 如果使用了图片缓存库（如cached_network_image），在这里清除
      // 例如：await DefaultCacheManager().emptyCache();
      
      _logger.d('Image cache cleared');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Failed to clear image cache', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 删除目录内容但保留目录本身
  Future<void> _deleteDirectoryContents(Directory directory) async {
    if (!await directory.exists()) return;
    
    await for (final entity in directory.list()) {
      try {
        if (entity is File) {
          await entity.delete();
        } else if (entity is Directory) {
          await entity.delete(recursive: true);
        }
      } catch (e) {
        _logger.w('Failed to delete ${entity.path}: $e');
      }
    }
  }

  /// 获取缓存大小信息
  Future<Map<String, int>> getCacheSizeInfo() async {
    final info = <String, int>{};
    
    try {
      // 计算临时文件大小
      final tempDir = await getTemporaryDirectory();
      info['temp'] = await _calculateDirectorySize(tempDir);
      
      // 计算应用缓存大小
      final cacheDir = await getApplicationCacheDirectory();
      info['cache'] = await _calculateDirectorySize(cacheDir);
      
      // 计算总大小
      info['total'] = info.values.reduce((a, b) => a + b);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to calculate cache size', error: e, stackTrace: stackTrace);
    }
    
    return info;
  }

  /// 计算目录大小（字节）
  Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    
    if (!await directory.exists()) return size;
    
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    } catch (e) {
      _logger.w('Failed to calculate size for ${directory.path}: $e');
    }
    
    return size;
  }

  /// 格式化文件大小显示
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

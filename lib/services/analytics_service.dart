import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 分析统计服务
/// 
/// 负责收集和分析用户行为数据，包括：
/// - 页面访问统计
/// - 用户行为追踪
/// - 性能指标收集
/// - 错误日志统计
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final Logger _logger = Logger();
  
  // 统计数据缓存
  final Map<String, int> _pageViewCounts = {};
  final Map<String, DateTime> _lastPageViews = {};
  final List<Map<String, dynamic>> _eventQueue = [];

  /// 初始化分析服务
  Future<void> initialize() async {
    try {
      await _loadStoredAnalytics();
      _logger.i('Analytics service initialized');
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize analytics service', error: e, stackTrace: stackTrace);
    }
  }

  /// 记录页面访问
  void trackPageView(String pageName) {
    try {
      final now = DateTime.now();
      
      // 更新访问计数
      _pageViewCounts[pageName] = (_pageViewCounts[pageName] ?? 0) + 1;
      _lastPageViews[pageName] = now;
      
      // 记录事件
      final event = {
        'type': 'page_view',
        'page_name': pageName,
        'timestamp': now.toIso8601String(),
        'session_id': _getCurrentSessionId(),
      };
      
      _eventQueue.add(event);
      _logger.d('Page view tracked: $pageName');
      
      // 定期保存数据
      _saveAnalyticsData();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to track page view', error: e, stackTrace: stackTrace);
    }
  }

  /// 记录用户事件
  void trackEvent(String eventName, Map<String, dynamic>? parameters) {
    try {
      final event = {
        'type': 'user_event',
        'event_name': eventName,
        'parameters': parameters ?? {},
        'timestamp': DateTime.now().toIso8601String(),
        'session_id': _getCurrentSessionId(),
      };
      
      _eventQueue.add(event);
      _logger.d('Event tracked: $eventName');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to track event', error: e, stackTrace: stackTrace);
    }
  }

  /// 记录错误
  void trackError(String errorType, String errorMessage, {String? stackTrace}) {
    try {
      final event = {
        'type': 'error',
        'error_type': errorType,
        'error_message': errorMessage,
        'stack_trace': stackTrace,
        'timestamp': DateTime.now().toIso8601String(),
        'session_id': _getCurrentSessionId(),
      };
      
      _eventQueue.add(event);
      _logger.d('Error tracked: $errorType');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to track error', error: e, stackTrace: stackTrace);
    }
  }

  /// 记录性能指标
  void trackPerformance(String metricName, double value, {String? unit}) {
    try {
      final event = {
        'type': 'performance',
        'metric_name': metricName,
        'value': value,
        'unit': unit ?? 'ms',
        'timestamp': DateTime.now().toIso8601String(),
        'session_id': _getCurrentSessionId(),
      };
      
      _eventQueue.add(event);
      _logger.d('Performance metric tracked: $metricName = $value${unit ?? 'ms'}');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to track performance', error: e, stackTrace: stackTrace);
    }
  }

  /// 获取页面访问统计
  Map<String, int> getPageViewStats() {
    return Map.from(_pageViewCounts);
  }

  /// 获取最近访问的页面
  List<String> getRecentPages({int limit = 10}) {
    final sortedPages = _lastPageViews.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedPages
        .take(limit)
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取事件队列大小
  int get eventQueueSize => _eventQueue.length;

  /// 清除所有分析数据
  Future<void> clearAllData() async {
    try {
      _pageViewCounts.clear();
      _lastPageViews.clear();
      _eventQueue.clear();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('analytics_page_views');
      await prefs.remove('analytics_events');
      
      _logger.i('All analytics data cleared');
    } catch (e, stackTrace) {
      _logger.e('Failed to clear analytics data', error: e, stackTrace: stackTrace);
    }
  }

  /// 获取当前会话ID
  String _getCurrentSessionId() {
    // 简化版本：使用应用启动时间作为会话ID
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// 从本地存储加载分析数据
  Future<void> _loadStoredAnalytics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 加载页面访问数据
      final pageViewsJson = prefs.getString('analytics_page_views');
      if (pageViewsJson != null) {
        // 这里可以解析JSON数据
        _logger.d('Loaded stored page view data');
      }
      
    } catch (e, stackTrace) {
      _logger.e('Failed to load stored analytics', error: e, stackTrace: stackTrace);
    }
  }

  /// 保存分析数据到本地存储
  Future<void> _saveAnalyticsData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 保存页面访问统计（简化版本）
      final pageViewsData = _pageViewCounts.entries
          .map((entry) => '${entry.key}:${entry.value}')
          .join(',');
      
      await prefs.setString('analytics_page_views', pageViewsData);
      
      // 限制事件队列大小
      if (_eventQueue.length > 1000) {
        _eventQueue.removeRange(0, _eventQueue.length - 1000);
      }
      
    } catch (e, stackTrace) {
      _logger.e('Failed to save analytics data', error: e, stackTrace: stackTrace);
    }
  }

  /// 获取调试信息
  Map<String, dynamic> getDebugInfo() {
    return {
      'page_view_counts': _pageViewCounts,
      'event_queue_size': _eventQueue.length,
      'last_page_views': _lastPageViews.map(
        (key, value) => MapEntry(key, value.toIso8601String()),
      ),
    };
  }
}

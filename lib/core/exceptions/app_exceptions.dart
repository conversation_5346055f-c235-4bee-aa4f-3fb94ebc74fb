import '../constants/error_codes.dart';

/// LogicLab应用自定义异常类型

/// 错误严重程度
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// 错误分类
enum ErrorCategory {
  user,
  puzzle,
  progress,
  hint,
  achievement,
  data,
  system,
  validation,
  business,
}

/// 基础应用异常
abstract class AppException implements Exception {
  /// 错误码
  final String errorCode;
  
  /// 错误消息
  final String message;
  
  /// 原始异常
  final dynamic originalError;
  
  /// 堆栈跟踪
  final StackTrace? stackTrace;
  
  /// 额外的上下文信息
  final Map<String, dynamic>? context;

  AppException({
    required this.errorCode,
    required this.message,
    this.originalError,
    this.stackTrace,
    this.context,
  });

  /// 是否可重试
  bool get isRetryable => errorCode.contains('NETWORK') || errorCode.contains('TIMEOUT');

  /// 是否应该上报
  bool get isReportable => !errorCode.contains('VALIDATION') && !errorCode.contains('USER_INPUT');

  /// 是否用户友好
  bool get isUserFriendly => !errorCode.contains('SYSTEM') && !errorCode.contains('DATABASE');

  /// 错误严重程度
  ErrorSeverity get severity {
    if (errorCode.contains('CRITICAL') || errorCode.contains('CORRUPTED')) {
      return ErrorSeverity.critical;
    }
    if (errorCode.contains('FAILED') || errorCode.contains('ERROR')) {
      return ErrorSeverity.high;
    }
    if (errorCode.contains('INVALID') || errorCode.contains('NOT_FOUND')) {
      return ErrorSeverity.medium;
    }
    return ErrorSeverity.low;
  }

  /// 错误分类
  ErrorCategory get category {
    if (errorCode.startsWith('USER_') || errorCode.contains('1000')) {
      return ErrorCategory.user;
    }
    if (errorCode.startsWith('PUZZLE_') || errorCode.contains('2000')) {
      return ErrorCategory.puzzle;
    }
    if (errorCode.startsWith('PROGRESS_') || errorCode.contains('3000')) {
      return ErrorCategory.progress;
    }
    if (errorCode.startsWith('HINT_') || errorCode.contains('4000')) {
      return ErrorCategory.hint;
    }
    if (errorCode.startsWith('ACHIEVEMENT_') || errorCode.contains('5000')) {
      return ErrorCategory.achievement;
    }
    if (errorCode.startsWith('DATA_') || errorCode.contains('6000')) {
      return ErrorCategory.data;
    }
    if (errorCode.startsWith('NETWORK_') || errorCode.startsWith('SYSTEM_') || errorCode.contains('7000')) {
      return ErrorCategory.system;
    }
    if (errorCode.startsWith('PARAMETER_') || errorCode.contains('8000')) {
      return ErrorCategory.validation;
    }
    return ErrorCategory.business;
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('$runtimeType: [$errorCode] $message');
    
    if (context != null && context!.isNotEmpty) {
      buffer.write(' | Context: $context');
    }
    
    if (originalError != null) {
      buffer.write(' | Original: $originalError');
    }
    
    return buffer.toString();
  }

  /// 转换为Map，用于日志记录
  Map<String, dynamic> toMap() {
    return {
      'type': runtimeType.toString(),
      'errorCode': errorCode,
      'message': message,
      'severity': severity.name,
      'category': category.name,
      'retryable': isRetryable,
      'reportable': isReportable,
      'userFriendly': isUserFriendly,
      'context': context,
      'originalError': originalError?.toString(),
      'stackTrace': stackTrace?.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// 用户相关异常
class UserException extends AppException {
  UserException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 用户不存在异常
class UserNotFoundException extends UserException {
  UserNotFoundException({
    String? userId,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.userNotFound,
          message: '用户不存在${userId != null ? ': $userId' : ''}',
          context: userId != null ? {'userId': userId} : null,
        );
}

/// 用户已存在异常
class UserAlreadyExistsException extends UserException {
  UserAlreadyExistsException({
    String? nickname,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.userAlreadyExists,
          message: '用户已存在${nickname != null ? ': $nickname' : ''}',
          context: nickname != null ? {'nickname': nickname} : null,
        );
}

/// 用户数量超限异常
class UserLimitExceededException extends UserException {
  UserLimitExceededException({
    int? currentCount,
    int? maxCount,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.userLimitExceeded,
          message: '用户数量已达上限${currentCount != null && maxCount != null ? ' ($currentCount/$maxCount)' : ''}',
          context: currentCount != null && maxCount != null
              ? {'currentCount': currentCount, 'maxCount': maxCount}
              : null,
        );
}

/// 昵称无效异常
class InvalidNicknameException extends UserException {
  InvalidNicknameException({
    String? nickname,
    String? reason,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidNickname,
          message: '无效的昵称${nickname != null ? ': $nickname' : ''}${reason != null ? ' ($reason)' : ''}',
          context: {
            if (nickname != null) 'nickname': nickname,
            if (reason != null) 'reason': reason,
          },
        );
}

/// 昵称已被使用异常
class NicknameAlreadyTakenException extends UserException {
  NicknameAlreadyTakenException({
    required String nickname,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.nicknameAlreadyTaken,
          message: '昵称已被使用: $nickname',
          context: {'nickname': nickname},
        );
}

/// 头像ID无效异常
class InvalidAvatarIdException extends UserException {
  InvalidAvatarIdException({
    String? avatarId,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidAvatarId,
          message: '无效的头像ID${avatarId != null ? ': $avatarId' : ''}',
          context: avatarId != null ? {'avatarId': avatarId} : null,
        );
}

/// 用户数据损坏异常
class UserDataCorruptedException extends UserException {
  UserDataCorruptedException({
    String? userId,
    String? details,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.userDataCorrupted,
          message: '用户数据已损坏${userId != null ? ' for user: $userId' : ''}',
          context: {
            if (userId != null) 'userId': userId,
            if (details != null) 'details': details,
          },
        );
}

/// 谜题相关异常
class PuzzleException extends AppException {
  PuzzleException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 谜题不存在异常
class PuzzleNotFoundException extends PuzzleException {
  PuzzleNotFoundException({
    String? puzzleId,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.puzzleNotFound,
          message: '谜题不存在${puzzleId != null ? ': $puzzleId' : ''}',
          context: puzzleId != null ? {'puzzleId': puzzleId} : null,
        );
}

/// 不支持的谜题类型异常
class UnsupportedPuzzleTypeException extends PuzzleException {
  UnsupportedPuzzleTypeException({
    String? puzzleType,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.unsupportedPuzzleType,
          message: '不支持的谜题类型${puzzleType != null ? ': $puzzleType' : ''}',
          context: puzzleType != null ? {'puzzleType': puzzleType} : null,
        );
}

/// 谜题数据无效异常
class InvalidPuzzleDataException extends PuzzleException {
  InvalidPuzzleDataException({
    String? puzzleId,
    String? reason,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidPuzzleData,
          message: '无效的谜题数据${puzzleId != null ? ' for puzzle: $puzzleId' : ''}${reason != null ? ' ($reason)' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (reason != null) 'reason': reason,
          },
        );
}

/// 谜题答案错误异常
class IncorrectAnswerException extends PuzzleException {
  IncorrectAnswerException({
    String? puzzleId,
    dynamic userAnswer,
    dynamic correctAnswer,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.incorrectAnswer,
          message: '答案错误${puzzleId != null ? ' for puzzle: $puzzleId' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (userAnswer != null) 'userAnswer': userAnswer,
            if (correctAnswer != null) 'correctAnswer': correctAnswer,
          },
        );
}

/// 谜题锁定异常
class PuzzleLockedException extends PuzzleException {
  PuzzleLockedException({
    String? puzzleId,
    String? reason,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.puzzleLocked,
          message: '谜题已锁定${puzzleId != null ? ' for puzzle: $puzzleId' : ''}${reason != null ? ' ($reason)' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (reason != null) 'reason': reason,
          },
        );
}

/// 前置条件不满足异常
class PrerequisiteNotMetException extends PuzzleException {
  PrerequisiteNotMetException({
    String? puzzleId,
    List<String>? prerequisites,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.prerequisiteNotMet,
          message: '前置条件未满足${puzzleId != null ? ' for puzzle: $puzzleId' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (prerequisites != null) 'prerequisites': prerequisites,
          },
        );
}

/// 技能等级不足异常
class InsufficientSkillLevelException extends PuzzleException {
  InsufficientSkillLevelException({
    String? skillType,
    int? currentLevel,
    int? requiredLevel,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.insufficientSkillLevel,
          message: '技能等级不足${skillType != null ? ' for $skillType' : ''}${currentLevel != null && requiredLevel != null ? ' ($currentLevel/$requiredLevel)' : ''}',
          context: {
            if (skillType != null) 'skillType': skillType,
            if (currentLevel != null) 'currentLevel': currentLevel,
            if (requiredLevel != null) 'requiredLevel': requiredLevel,
          },
        );
}

/// 谜题加载失败异常
class PuzzleLoadFailedException extends PuzzleException {
  PuzzleLoadFailedException({
    String? puzzleId,
    String? source,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.puzzleLoadFailed,
          message: '谜题加载失败${puzzleId != null ? ' for puzzle: $puzzleId' : ''}${source != null ? ' from: $source' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (source != null) 'source': source,
          },
        );
}

/// 成就相关异常
class AchievementException extends AppException {
  AchievementException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 成就不存在异常
class AchievementNotFoundException extends AchievementException {
  AchievementNotFoundException({
    String? achievementId,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.achievementNotFound,
          message: '成就不存在${achievementId != null ? ': $achievementId' : ''}',
          context: achievementId != null ? {'achievementId': achievementId} : null,
        );
}

/// 成就已解锁异常
class AchievementAlreadyUnlockedException extends AchievementException {
  AchievementAlreadyUnlockedException({
    String? achievementId,
    DateTime? unlockedAt,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.achievementAlreadyUnlocked,
          message: '成就已解锁${achievementId != null ? ': $achievementId' : ''}',
          context: {
            if (achievementId != null) 'achievementId': achievementId,
            if (unlockedAt != null) 'unlockedAt': unlockedAt.toIso8601String(),
          },
        );
}

/// 成就条件不满足异常
class AchievementConditionNotMetException extends AchievementException {
  AchievementConditionNotMetException({
    String? achievementId,
    Map<String, dynamic>? conditions,
    Map<String, dynamic>? currentProgress,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.achievementConditionNotMet,
          message: '成就条件未满足${achievementId != null ? ' for: $achievementId' : ''}',
          context: {
            if (achievementId != null) 'achievementId': achievementId,
            if (conditions != null) 'conditions': conditions,
            if (currentProgress != null) 'currentProgress': currentProgress,
          },
        );
}

/// 提示相关异常
class HintException extends AppException {
  HintException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 提示不可用异常
class HintUnavailableException extends HintException {
  HintUnavailableException({
    String? puzzleId,
    String? reason,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.hintUnavailable,
          message: '提示不可用${puzzleId != null ? ' for puzzle: $puzzleId' : ''}${reason != null ? ' ($reason)' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (reason != null) 'reason': reason,
          },
        );
}

/// 提示次数超限异常
class HintLimitExceededException extends HintException {
  HintLimitExceededException({
    String? puzzleId,
    int? usedHints,
    int? maxHints,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.hintLimitExceeded,
          message: '提示次数已达上限${puzzleId != null ? ' for puzzle: $puzzleId' : ''}${usedHints != null && maxHints != null ? ' ($usedHints/$maxHints)' : ''}',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (usedHints != null) 'usedHints': usedHints,
            if (maxHints != null) 'maxHints': maxHints,
          },
        );
}

/// 提示冷却中异常
class HintOnCooldownException extends HintException {
  HintOnCooldownException({
    String? puzzleId,
    Duration? remainingTime,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.hintOnCooldown,
          message: '提示冷却中，剩余时间: ${remainingTime?.inSeconds}秒',
          context: {
            if (puzzleId != null) 'puzzleId': puzzleId,
            if (remainingTime != null) 'remainingSeconds': remainingTime.inSeconds,
          },
        );
}

/// 数据存储相关异常
class StorageException extends AppException {
  StorageException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 数据保存失败异常
class DataSaveException extends StorageException {
  DataSaveException({
    String? details,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.dataSaveError,
          message: '数据保存失败${details != null ? ' ($details)' : ''}',
          context: details != null ? {'details': details} : null,
        );
}

/// 数据加载失败异常
class DataLoadException extends StorageException {
  DataLoadException({
    String? details,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.dataLoadError,
          message: '数据加载失败${details != null ? ' ($details)' : ''}',
          context: details != null ? {'details': details} : null,
        );
}

/// 网络相关异常
class NetworkException extends AppException {
  NetworkException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 网络连接异常
class NetworkConnectionException extends NetworkException {
  NetworkConnectionException({
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.networkConnectionError,
          message: '网络连接失败，请检查网络设置',
          context: null,
        );
}

/// 验证相关异常
class ValidationException extends AppException {
  ValidationException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 参数验证异常
class InvalidParameterException extends ValidationException {
  InvalidParameterException({
    required String parameter,
    String? details,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidParameter,
          message: '参数验证失败: $parameter${details != null ? ' - $details' : ''}',
          context: {
            'parameterName': parameter,
            if (details != null) 'details': details,
          },
        );
}

/// 参数为空异常
class ParameterNullException extends ValidationException {
  ParameterNullException({
    String? parameterName,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.parameterNull,
          message: '参数不能为空${parameterName != null ? ': $parameterName' : ''}',
          context: parameterName != null ? {'parameterName': parameterName} : null,
        );
}

/// 参数格式错误异常
class InvalidParameterFormatException extends ValidationException {
  InvalidParameterFormatException({
    String? parameterName,
    String? expectedFormat,
    dynamic actualValue,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidParameterFormat,
          message: '参数格式错误${parameterName != null ? ' for: $parameterName' : ''}${expectedFormat != null ? ' (expected: $expectedFormat)' : ''}',
          context: {
            if (parameterName != null) 'parameterName': parameterName,
            if (expectedFormat != null) 'expectedFormat': expectedFormat,
            if (actualValue != null) 'actualValue': actualValue,
          },
        );
}

/// 参数值超出范围异常
class ParameterOutOfRangeException extends ValidationException {
  ParameterOutOfRangeException({
    String? parameterName,
    dynamic actualValue,
    dynamic minValue,
    dynamic maxValue,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.parameterOutOfRange,
          message: '参数超出范围${parameterName != null ? ' for: $parameterName' : ''}${actualValue != null ? ' (value: $actualValue)' : ''}${minValue != null && maxValue != null ? ' (range: $minValue-$maxValue)' : ''}',
          context: {
            if (parameterName != null) 'parameterName': parameterName,
            if (actualValue != null) 'actualValue': actualValue,
            if (minValue != null) 'minValue': minValue,
            if (maxValue != null) 'maxValue': maxValue,
          },
        );
}

/// 业务逻辑异常
class OperationNotAllowedException extends AppException {
  OperationNotAllowedException({
    String? operation,
    String? reason,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.operationNotAllowed,
          message: '操作不被允许${operation != null ? ' ($operation)' : ''}${reason != null ? ' ($reason)' : ''}',
          context: {
            if (operation != null) 'operation': operation,
            if (reason != null) 'reason': reason,
          },
        );
}

/// 状态冲突异常
class StateConflictException extends AppException {
  StateConflictException({
    String? currentState,
    String? expectedState,
    String? operation,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.stateConflict,
          message: '状态冲突${operation != null ? ' for operation: $operation' : ''}${currentState != null && expectedState != null ? ' (current: $currentState, expected: $expectedState)' : ''}',
          context: {
            if (currentState != null) 'currentState': currentState,
            if (expectedState != null) 'expectedState': expectedState,
            if (operation != null) 'operation': operation,
          },
        );
}

/// 业务规则违反异常
class BusinessRuleViolationException extends AppException {
  BusinessRuleViolationException({
    String? rule,
    String? details,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.businessRuleViolation,
          message: '业务规则违反${rule != null ? ' ($rule)' : ''}${details != null ? ' ($details)' : ''}',
          context: {
            if (rule != null) 'rule': rule,
            if (details != null) 'details': details,
          },
        );
}

/// 未知异常
class UnknownException extends AppException {
  UnknownException({
    String? details,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.unknown,
          message: '未知错误${details != null ? ' ($details)' : ''}',
          context: details != null ? {'details': details} : null,
        );
}

/// 数据库连接失败异常
class DatabaseConnectionFailedException extends StorageException {
  DatabaseConnectionFailedException({
    String? database,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.databaseConnectionFailed,
          message: '数据库连接失败${database != null ? ' to: $database' : ''}',
          context: database != null ? {'database': database} : null,
        );
}

/// 数据序列化失败异常
class DataSerializationFailedException extends StorageException {
  DataSerializationFailedException({
    String? dataType,
    dynamic data,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.dataSerializationFailed,
          message: '数据序列化失败${dataType != null ? ' for type: $dataType' : ''}',
          context: {
            if (dataType != null) 'dataType': dataType,
            if (data != null) 'data': data.toString(),
          },
        );
}

/// 数据反序列化失败异常
class DataDeserializationFailedException extends StorageException {
  DataDeserializationFailedException({
    String? dataType,
    String? source,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.dataDeserializationFailed,
          message: '数据反序列化失败${dataType != null ? ' for type: $dataType' : ''}',
          context: {
            if (dataType != null) 'dataType': dataType,
            if (source != null) 'source': source,
          },
        );
}

/// 操作超时异常
class OperationTimeoutException extends AppException {
  OperationTimeoutException({
    String? operation,
    Duration? timeout,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.operationTimeout,
          message: '操作超时${operation != null ? ' for: $operation' : ''}${timeout != null ? ' after ${timeout.inSeconds}s' : ''}',
          context: {
            if (operation != null) 'operation': operation,
            if (timeout != null) 'timeoutSeconds': timeout.inSeconds,
          },
        );
}

/// 权限不足异常
class InsufficientPermissionsException extends AppException {
  InsufficientPermissionsException({
    String? permission,
    String? operation,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.insufficientPermissions,
          message: '权限不足${permission != null ? ' for: $permission' : ''}${operation != null ? ' (operation: $operation)' : ''}',
          context: {
            if (permission != null) 'permission': permission,
            if (operation != null) 'operation': operation,
          },
        );
}

/// 进度保存失败异常
class ProgressSaveFailedException extends AppException {
  ProgressSaveFailedException({
    String? userId,
    String? puzzleId,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.progressSaveFailed,
          message: '进度保存失败${userId != null ? ' for user: $userId' : ''}${puzzleId != null ? ', puzzle: $puzzleId' : ''}',
          context: {
            if (userId != null) 'userId': userId,
            if (puzzleId != null) 'puzzleId': puzzleId,
          },
        );
}

/// 进度数据无效异常
class InvalidProgressDataException extends AppException {
  InvalidProgressDataException({
    String? reason,
    dynamic data,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidProgressData,
          message: '无效的进度数据${reason != null ? ' ($reason)' : ''}',
          context: {
            if (reason != null) 'reason': reason,
            if (data != null) 'data': data,
          },
        );
}

/// 游戏会话无效异常
class InvalidGameSessionException extends AppException {
  InvalidGameSessionException({
    String? sessionId,
    String? reason,
    super.originalError,
    super.stackTrace,
  }) : super(
          errorCode: ErrorCodes.invalidGameSession,
          message: '无效的游戏会话${sessionId != null ? ' (session: $sessionId)' : ''}${reason != null ? ' ($reason)' : ''}',
          context: {
            if (sessionId != null) 'sessionId': sessionId,
            if (reason != null) 'reason': reason,
          },
        );
} 

/// 数据生成异常
class DataGenerationException extends AppException {
  DataGenerationException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
}

/// 数据访问异常
class DataAccessException extends AppException {
  DataAccessException({
    required super.errorCode,
    required super.message,
    super.originalError,
    super.stackTrace,
    super.context,
  });
} 
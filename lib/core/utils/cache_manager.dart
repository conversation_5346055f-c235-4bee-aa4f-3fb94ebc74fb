/// LogicLab 统一缓存管理器
/// 为Repository层提供高性能的缓存策略支持
library;

import 'dart:async';
import 'dart:convert';

import 'result.dart';
import '../exceptions/app_exceptions.dart';

/// 缓存策略枚举
enum CacheStrategy {
  /// 无缓存
  none,
  
  /// 内存缓存
  memory,
  
  /// 持久化缓存
  persistent,
  
  /// 混合缓存（内存+持久化）
  hybrid,
}

/// 缓存过期策略
enum CacheExpirationStrategy {
  /// 基于时间的过期
  timeBased,
  
  /// 基于访问次数的过期
  accessBased,
  
  /// 基于大小的过期（LRU）
  sizeBased,
  
  /// 永不过期
  never,
}

/// 缓存项元数据
class CacheMetadata {
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后访问时间
  final DateTime lastAccessedAt;
  
  /// 访问次数
  final int accessCount;
  
  /// 过期时间
  final DateTime? expiresAt;
  
  /// 数据大小（字节）
  final int size;
  
  /// 优先级
  final int priority;

  const CacheMetadata({
    required this.createdAt,
    required this.lastAccessedAt,
    required this.accessCount,
    this.expiresAt,
    required this.size,
    this.priority = 0,
  });

  /// 是否已过期
  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }

  /// 创建副本
  CacheMetadata copyWith({
    DateTime? createdAt,
    DateTime? lastAccessedAt,
    int? accessCount,
    DateTime? expiresAt,
    int? size,
    int? priority,
  }) {
    return CacheMetadata(
      createdAt: createdAt ?? this.createdAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      accessCount: accessCount ?? this.accessCount,
      expiresAt: expiresAt ?? this.expiresAt,
      size: size ?? this.size,
      priority: priority ?? this.priority,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'createdAt': createdAt.toIso8601String(),
      'lastAccessedAt': lastAccessedAt.toIso8601String(),
      'accessCount': accessCount,
      'expiresAt': expiresAt?.toIso8601String(),
      'size': size,
      'priority': priority,
    };
  }

  /// 从Map创建
  factory CacheMetadata.fromMap(Map<String, dynamic> map) {
    return CacheMetadata(
      createdAt: DateTime.parse(map['createdAt']),
      lastAccessedAt: DateTime.parse(map['lastAccessedAt']),
      accessCount: map['accessCount'],
      expiresAt: map['expiresAt'] != null 
          ? DateTime.parse(map['expiresAt']) 
          : null,
      size: map['size'],
      priority: map['priority'] ?? 0,
    );
  }
}

/// 缓存项
class CacheItem<T> {
  /// 缓存的数据
  final T data;
  
  /// 缓存元数据
  final CacheMetadata metadata;

  const CacheItem({
    required this.data,
    required this.metadata,
  });

  /// 是否已过期
  bool get isExpired => metadata.isExpired;

  /// 更新访问信息
  CacheItem<T> updateAccess() {
    return CacheItem<T>(
      data: data,
      metadata: metadata.copyWith(
        lastAccessedAt: DateTime.now(),
        accessCount: metadata.accessCount + 1,
      ),
    );
  }
}

/// 缓存配置
class CacheConfig {
  /// 缓存策略
  final CacheStrategy strategy;
  
  /// 过期策略
  final CacheExpirationStrategy expirationStrategy;
  
  /// 默认过期时间（分钟）
  final int defaultTtlMinutes;
  
  /// 最大缓存大小（MB）
  final int maxSizeMB;
  
  /// 最大缓存项数量
  final int maxItems;
  
  /// 是否启用压缩
  final bool enableCompression;
  
  /// 是否启用加密
  final bool enableEncryption;

  const CacheConfig({
    this.strategy = CacheStrategy.hybrid,
    this.expirationStrategy = CacheExpirationStrategy.timeBased,
    this.defaultTtlMinutes = 30,
    this.maxSizeMB = 50,
    this.maxItems = 1000,
    this.enableCompression = true,
    this.enableEncryption = false,
  });
}

/// 缓存统计信息
class CacheStats {
  /// 命中次数
  final int hits;
  
  /// 未命中次数
  final int misses;
  
  /// 缓存项数量
  final int itemCount;
  
  /// 总大小（字节）
  final int totalSize;
  
  /// 命中率
  double get hitRate {
    final total = hits + misses;
    return total > 0 ? hits / total : 0.0;
  }

  const CacheStats({
    required this.hits,
    required this.misses,
    required this.itemCount,
    required this.totalSize,
  });

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'hits': hits,
      'misses': misses,
      'itemCount': itemCount,
      'totalSize': totalSize,
      'hitRate': hitRate,
    };
  }
}

/// 缓存管理器接口
abstract class CacheManager {
  /// 获取缓存项
  Future<Result<T?>> get<T>(String key);

  /// 设置缓存项
  Future<Result<void>> set<T>(
    String key,
    T value, {
    Duration? ttl,
    int? priority,
  });

  /// 删除缓存项
  Future<Result<void>> delete(String key);

  /// 检查缓存项是否存在
  Future<Result<bool>> exists(String key);

  /// 清空所有缓存
  Future<Result<void>> clear();

  /// 获取缓存统计信息
  Future<Result<CacheStats>> getStats();

  /// 清理过期缓存
  Future<Result<int>> cleanup();

  /// 获取所有缓存键
  Future<Result<List<String>>> getKeys();

  /// 批量获取
  Future<Result<Map<String, T?>>> getMultiple<T>(List<String> keys);

  /// 批量设置
  Future<Result<void>> setMultiple<T>(
    Map<String, T> items, {
    Duration? ttl,
    int? priority,
  });

  /// 批量删除
  Future<Result<void>> deleteMultiple(List<String> keys);
}

/// 内存缓存管理器
class MemoryCacheManager implements CacheManager {
  final Map<String, CacheItem<dynamic>> _cache = {};
  final CacheConfig _config;
  int _hits = 0;
  int _misses = 0;

  MemoryCacheManager(this._config);

  @override
  Future<Result<T?>> get<T>(String key) async {
    try {
      final item = _cache[key];
      
      if (item == null) {
        _misses++;
        return const Result.success(null);
      }

      if (item.isExpired) {
        _cache.remove(key);
        _misses++;
        return const Result.success(null);
      }

      // 更新访问信息
      _cache[key] = item.updateAccess();
      _hits++;
      
      return Result.success(item.data as T);
    } catch (e, stackTrace) {
      return Result.failure(
        DataDeserializationFailedException(
          dataType: T.toString(),
          source: 'memory cache',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<void>> set<T>(
    String key,
    T value, {
    Duration? ttl,
    int? priority,
  }) async {
    try {
      // 检查缓存大小限制
      if (_cache.length >= _config.maxItems) {
        await _evictLeastRecentlyUsed();
      }

      final now = DateTime.now();
      final metadata = CacheMetadata(
        createdAt: now,
        lastAccessedAt: now,
        accessCount: 0,
        expiresAt: ttl != null ? now.add(ttl) : null,
        size: _estimateSize(value),
        priority: priority ?? 0,
      );

      _cache[key] = CacheItem<T>(
        data: value,
        metadata: metadata,
      );

      return const Result.success(null);
    } catch (e, stackTrace) {
      return Result.failure(
        DataSerializationFailedException(
          dataType: T.toString(),
          data: value,
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<void>> delete(String key) async {
    _cache.remove(key);
    return const Result.success(null);
  }

  @override
  Future<Result<bool>> exists(String key) async {
    final item = _cache[key];
    if (item == null || item.isExpired) {
      return const Result.success(false);
    }
    return const Result.success(true);
  }

  @override
  Future<Result<void>> clear() async {
    _cache.clear();
    _hits = 0;
    _misses = 0;
    return const Result.success(null);
  }

  @override
  Future<Result<CacheStats>> getStats() async {
    int totalSize = 0;
    for (final item in _cache.values) {
      totalSize += item.metadata.size;
    }

    return Result.success(CacheStats(
      hits: _hits,
      misses: _misses,
      itemCount: _cache.length,
      totalSize: totalSize,
    ));
  }

  @override
  Future<Result<int>> cleanup() async {
    final keysToRemove = <String>[];
    
    for (final entry in _cache.entries) {
      if (entry.value.isExpired) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    return Result.success(keysToRemove.length);
  }

  @override
  Future<Result<List<String>>> getKeys() async {
    return Result.success(_cache.keys.toList());
  }

  @override
  Future<Result<Map<String, T?>>> getMultiple<T>(List<String> keys) async {
    final result = <String, T?>{};
    
    for (final key in keys) {
      final itemResult = await get<T>(key);
      if (itemResult.isSuccess) {
        result[key] = itemResult.data;
      } else {
        return Result.failure(itemResult.exception!);
      }
    }
    
    return Result.success(result);
  }

  @override
  Future<Result<void>> setMultiple<T>(
    Map<String, T> items, {
    Duration? ttl,
    int? priority,
  }) async {
    for (final entry in items.entries) {
      final result = await set(entry.key, entry.value, ttl: ttl, priority: priority);
      if (result.isFailure) {
        return result;
      }
    }
    
    return const Result.success(null);
  }

  @override
  Future<Result<void>> deleteMultiple(List<String> keys) async {
    for (final key in keys) {
      _cache.remove(key);
    }
    return const Result.success(null);
  }

  /// 驱逐最近最少使用的项
  Future<void> _evictLeastRecentlyUsed() async {
    if (_cache.isEmpty) return;

    String? lruKey;
    DateTime? oldestAccess;

    for (final entry in _cache.entries) {
      final lastAccessed = entry.value.metadata.lastAccessedAt;
      if (oldestAccess == null || lastAccessed.isBefore(oldestAccess)) {
        oldestAccess = lastAccessed;
        lruKey = entry.key;
      }
    }

    if (lruKey != null) {
      _cache.remove(lruKey);
    }
  }

  /// 估算数据大小
  int _estimateSize(dynamic value) {
    try {
      final json = jsonEncode(value);
      return json.length * 2; // 粗略估算，UTF-16编码
    } catch (e) {
      return 100; // 默认大小
    }
  }
}

/// 缓存装饰器
/// 
/// 为Repository方法提供透明的缓存支持
class CacheDecorator<T> {
  final CacheManager _cacheManager;
  final String _keyPrefix;
  final Duration _defaultTtl;

  CacheDecorator(
    this._cacheManager,
    this._keyPrefix, {
    Duration? defaultTtl,
  }) : _defaultTtl = defaultTtl ?? const Duration(minutes: 30);

  /// 缓存方法调用
  Future<Result<T>> cached<P>(
    String methodName,
    P params,
    Future<Result<T>> Function() operation, {
    Duration? ttl,
    bool forceRefresh = false,
    String Function(P)? keyGenerator,
  }) async {
    // 生成缓存键
    final key = _generateKey(methodName, params, keyGenerator);

    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      final cachedResult = await _cacheManager.get<T>(key);
      if (cachedResult.isSuccess && cachedResult.data != null) {
        return Result.success(cachedResult.data as T);
      }
    }

    // 执行实际操作
    final result = await operation();
    
    // 如果成功，缓存结果
    if (result.isSuccess) {
      await _cacheManager.set(
        key,
        result.data as T,
        ttl: ttl ?? _defaultTtl,
      );
    }

    return result;
  }

  /// 缓存列表方法调用
  Future<Result<List<T>>> cachedList<P>(
    String methodName,
    P params,
    Future<Result<List<T>>> Function() operation, {
    Duration? ttl,
    bool forceRefresh = false,
    String Function(P)? keyGenerator,
  }) async {
    final key = _generateKey(methodName, params, keyGenerator);

    if (!forceRefresh) {
      final cachedResult = await _cacheManager.get<List<T>>(key);
      if (cachedResult.isSuccess && cachedResult.data != null) {
        return Result.success(cachedResult.data!);
      }
    }

    final result = await operation();
    
    if (result.isSuccess) {
      await _cacheManager.set(
        key,
        result.data!,
        ttl: ttl ?? _defaultTtl,
      );
    }

    return result;
  }

  /// 失效缓存
  Future<Result<void>> invalidate<P>(
    String methodName,
    P params, {
    String Function(P)? keyGenerator,
  }) async {
    final key = _generateKey(methodName, params, keyGenerator);
    return _cacheManager.delete(key);
  }

  /// 失效模式匹配的缓存
  Future<Result<void>> invalidatePattern(String pattern) async {
    final keysResult = await _cacheManager.getKeys();
    if (keysResult.isFailure) {
      return Result.failure(keysResult.exception!);
    }

    final matchingKeys = keysResult.data!
        .where((key) => key.contains(pattern))
        .toList();

    return _cacheManager.deleteMultiple(matchingKeys);
  }

  /// 生成缓存键
  String _generateKey<P>(
    String methodName,
    P params,
    String Function(P)? keyGenerator,
  ) {
    final paramKey = keyGenerator?.call(params) ?? _defaultKeyGenerator(params);
    return '$_keyPrefix:$methodName:$paramKey';
  }

  /// 默认键生成器
  String _defaultKeyGenerator<P>(P params) {
    try {
      if (params == null) return 'null';
      if (params is String) return params;
      if (params is num) return params.toString();
      if (params is bool) return params.toString();
      
      // 对于复杂对象，尝试序列化
      final json = jsonEncode(params);
      return json.hashCode.toString();
    } catch (e) {
      return params.toString().hashCode.toString();
    }
  }
}

/// 缓存管理器工厂
class CacheManagerFactory {
  CacheManagerFactory._();

  /// 创建内存缓存管理器
  static CacheManager createMemory({CacheConfig? config}) {
    return MemoryCacheManager(config ?? const CacheConfig());
  }

  /// 创建用户数据缓存装饰器
  static CacheDecorator<T> createUserDecorator<T>(CacheManager cacheManager) {
    return CacheDecorator<T>(
      cacheManager,
      'user',
      defaultTtl: const Duration(minutes: 15),
    );
  }

  /// 创建谜题数据缓存装饰器
  static CacheDecorator<T> createPuzzleDecorator<T>(CacheManager cacheManager) {
    return CacheDecorator<T>(
      cacheManager,
      'puzzle',
      defaultTtl: const Duration(hours: 1),
    );
  }

  /// 创建游戏进度缓存装饰器
  static CacheDecorator<T> createProgressDecorator<T>(CacheManager cacheManager) {
    return CacheDecorator<T>(
      cacheManager,
      'progress',
      defaultTtl: const Duration(minutes: 5),
    );
  }

  /// 创建成就数据缓存装饰器
  static CacheDecorator<T> createAchievementDecorator<T>(CacheManager cacheManager) {
    return CacheDecorator<T>(
      cacheManager,
      'achievement',
      defaultTtl: const Duration(minutes: 30),
    );
  }
} 
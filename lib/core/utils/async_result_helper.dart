/// 异步结果辅助工具
/// 提供统一的异步操作Result封装，减少重复的try-catch代码
library;

import 'package:logger/logger.dart';
import 'result.dart';
import '../exceptions/app_exceptions.dart';

/// 异步结果辅助器
class AsyncResultHelper {
  static final Logger _logger = Logger();

  /// 执行异步操作并返回Result
  /// 
  /// [operation] 要执行的异步操作
  /// [errorCode] 错误码
  /// [errorMessage] 错误消息
  /// [logContext] 日志上下文信息
  static Future<Result<T>> execute<T>(
    Future<T> Function() operation, {
    required String errorCode,
    required String errorMessage,
    String? logContext,
  }) async {
    return Result.fromAsync(() async {
      try {
        final result = await operation();
        if (logContext != null) {
          _logger.d('$logContext: Success');
        }
        return result;
      } catch (e, stackTrace) {
        final context = logContext ?? 'Operation';
        _logger.e('$context failed', error: e, stackTrace: stackTrace);
        
        // 重新抛出异常，让Result.fromAsync处理
        if (e is AppException) {
          rethrow;
        } else {
          throw UserException(
            errorCode: errorCode,
            message: '$errorMessage: ${e.toString()}',
            originalError: e,
            stackTrace: stackTrace,
          );
        }
      }
    });
  }

  /// 执行用户相关操作
  static Future<Result<T>> executeUserOperation<T>(
    Future<T> Function() operation, {
    required String errorCode,
    required String errorMessage,
    String? logContext,
  }) async {
    return execute<T>(
      operation,
      errorCode: errorCode,
      errorMessage: errorMessage,
      logContext: logContext ?? 'User operation',
    );
  }

  /// 执行谜题相关操作
  static Future<Result<T>> executePuzzleOperation<T>(
    Future<T> Function() operation, {
    required String errorCode,
    required String errorMessage,
    String? logContext,
  }) async {
    return execute<T>(
      operation,
      errorCode: errorCode,
      errorMessage: errorMessage,
      logContext: logContext ?? 'Puzzle operation',
    );
  }

  /// 执行数据操作
  static Future<Result<T>> executeDataOperation<T>(
    Future<T> Function() operation, {
    required String errorCode,
    required String errorMessage,
    String? logContext,
  }) async {
    return execute<T>(
      operation,
      errorCode: errorCode,
      errorMessage: errorMessage,
      logContext: logContext ?? 'Data operation',
    );
  }

  /// 同步操作的Result封装
  static Result<T> executeSync<T>(
    T Function() operation, {
    required String errorCode,
    required String errorMessage,
    String? logContext,
  }) {
    try {
      final result = operation();
      if (logContext != null) {
        _logger.d('$logContext: Success');
      }
      return Result.success(result);
    } catch (e, stackTrace) {
      final context = logContext ?? 'Sync operation';
      _logger.e('$context failed', error: e, stackTrace: stackTrace);
      
      if (e is AppException) {
        return Result.failure(e);
      } else {
        return Result.failure(UserException(
          errorCode: errorCode,
          message: '$errorMessage: ${e.toString()}',
          originalError: e,
          stackTrace: stackTrace,
        ));
      }
    }
  }
} 
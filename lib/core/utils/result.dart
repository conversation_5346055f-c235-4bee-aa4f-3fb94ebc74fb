/// LogicLab 统一结果类型
/// 用于封装操作结果和错误处理的函数式编程模式
library;

import '../exceptions/app_exceptions.dart';

/// 操作结果的基类
/// 
/// 使用函数式编程的Result模式来处理可能失败的操作
/// 避免使用异常进行正常的业务流程控制
sealed class Result<T> {
  const Result();

  /// 创建成功结果
  const factory Result.success(T data) = Success<T>;

  /// 创建失败结果
  const factory Result.failure(AppException exception) = Failure<T>;

  /// 从异步操作创建Result
  static Future<Result<T>> fromAsync<T>(Future<T> Function() operation) async {
    try {
      final data = await operation();
      return Result.success(data);
    } on AppException catch (e) {
      return Result.failure(e);
    } catch (e, stackTrace) {
      return Result.failure(
        UnknownException(
          details: e.toString(),
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 从同步操作创建Result
  static Result<T> fromSync<T>(T Function() operation) {
    try {
      final data = operation();
      return Result.success(data);
    } on AppException catch (e) {
      return Result.failure(e);
    } catch (e, stackTrace) {
      return Result.failure(
        UnknownException(
          details: e.toString(),
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 是否为成功结果
  bool get isSuccess => this is Success<T>;

  /// 是否为失败结果
  bool get isFailure => this is Failure<T>;

  /// 获取数据（仅在成功时可用）
  T? get data => switch (this) {
        Success<T>(data: final data) => data,
        Failure<T>() => null,
      };

  /// 获取异常（仅在失败时可用）
  AppException? get exception => switch (this) {
        Success<T>() => null,
        Failure<T>(exception: final exception) => exception,
      };

  /// 获取数据或抛出异常
  T getOrThrow() => switch (this) {
        Success<T>(data: final data) => data,
        Failure<T>(exception: final exception) => throw exception,
      };

  /// 获取数据或返回默认值
  T getOrDefault(T defaultValue) => switch (this) {
        Success<T>(data: final data) => data,
        Failure<T>() => defaultValue,
      };

  /// 获取数据或通过函数计算默认值
  T getOrElse(T Function() defaultValueProvider) => switch (this) {
        Success<T>(data: final data) => data,
        Failure<T>() => defaultValueProvider(),
      };

  /// 映射成功值
  Result<R> map<R>(R Function(T data) mapper) => switch (this) {
        Success<T>(data: final data) => Result.success(mapper(data)),
        Failure<T>(exception: final exception) => Result.failure(exception),
      };

  /// 异步映射成功值
  Future<Result<R>> mapAsync<R>(Future<R> Function(T data) mapper) async =>
      switch (this) {
        Success<T>(data: final data) => Result.success(await mapper(data)),
        Failure<T>(exception: final exception) => Result.failure(exception),
      };

  /// 平铺映射（flatMap）
  Result<R> flatMap<R>(Result<R> Function(T data) mapper) => switch (this) {
        Success<T>(data: final data) => mapper(data),
        Failure<T>(exception: final exception) => Result.failure(exception),
      };

  /// 异步平铺映射
  Future<Result<R>> flatMapAsync<R>(
    Future<Result<R>> Function(T data) mapper,
  ) async =>
      switch (this) {
        Success<T>(data: final data) => await mapper(data),
        Failure<T>(exception: final exception) => Result.failure(exception),
      };

  /// 映射异常
  Result<T> mapException(AppException Function(AppException exception) mapper) =>
      switch (this) {
        Success<T>() => this,
        Failure<T>(exception: final exception) =>
            Result.failure(mapper(exception)),
      };

  /// 恢复错误（提供默认值）
  Result<T> recover(T Function(AppException exception) recovery) =>
      switch (this) {
        Success<T>() => this,
        Failure<T>(exception: final exception) =>
            Result.success(recovery(exception)),
      };

  /// 异步恢复错误
  Future<Result<T>> recoverAsync(
    Future<T> Function(AppException exception) recovery,
  ) async =>
      switch (this) {
        Success<T>() => this,
        Failure<T>(exception: final exception) =>
            Result.success(await recovery(exception)),
      };

  /// 恢复错误（返回另一个Result）
  Result<T> recoverWith(
    Result<T> Function(AppException exception) recovery,
  ) =>
      switch (this) {
        Success<T>() => this,
        Failure<T>(exception: final exception) => recovery(exception),
      };

  /// 异步恢复错误（返回另一个Result）
  Future<Result<T>> recoverWithAsync(
    Future<Result<T>> Function(AppException exception) recovery,
  ) async =>
      switch (this) {
        Success<T>() => this,
        Failure<T>(exception: final exception) => await recovery(exception),
      };

  /// 过滤结果
  Result<T> filter(
    bool Function(T data) predicate,
    AppException Function() onFilterFailed,
  ) =>
      switch (this) {
        Success<T>(data: final data) when predicate(data) => this,
        Success<T>() => Result.failure(onFilterFailed()),
        Failure<T>() => this,
      };

  /// 执行副作用（成功时）
  Result<T> onSuccess(void Function(T data) action) {
    if (this case Success<T>(data: final data)) {
      action(data);
    }
    return this;
  }

  /// 执行副作用（失败时）
  Result<T> onFailure(void Function(AppException exception) action) {
    if (this case Failure<T>(exception: final exception)) {
      action(exception);
    }
    return this;
  }

  /// 执行副作用（无论成功失败）
  Result<T> onComplete(void Function(Result<T> result) action) {
    action(this);
    return this;
  }

  /// 折叠结果（将成功和失败都映射到同一类型）
  R fold<R>(
    R Function(T data) onSuccess,
    R Function(AppException exception) onFailure,
  ) =>
      switch (this) {
        Success<T>(data: final data) => onSuccess(data),
        Failure<T>(exception: final exception) => onFailure(exception),
      };

  /// 异步折叠结果
  Future<R> foldAsync<R>(
    Future<R> Function(T data) onSuccess,
    Future<R> Function(AppException exception) onFailure,
  ) async =>
      switch (this) {
        Success<T>(data: final data) => await onSuccess(data),
        Failure<T>(exception: final exception) => await onFailure(exception),
      };

  /// 转换为Map（用于序列化）
  Map<String, dynamic> toMap() => switch (this) {
        Success<T>(data: final data) => {
            'success': true,
            'data': data,
          },
        Failure<T>(exception: final exception) => {
            'success': false,
            'error': exception.toMap(),
          },
      };

  @override
  String toString() => switch (this) {
        Success<T>(data: final data) => 'Success($data)',
        Failure<T>(exception: final exception) => 'Failure($exception)',
      };

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return switch ((this, other)) {
      (Success<T>(data: final data1), Success<T>(data: final data2)) =>
        data1 == data2,
      (
        Failure<T>(exception: final exception1),
        Failure<T>(exception: final exception2)
      ) =>
        exception1.errorCode == exception2.errorCode,
      _ => false,
    };
  }

  @override
  int get hashCode => switch (this) {
        Success<T>(data: final data) => data.hashCode,
        Failure<T>(exception: final exception) => exception.errorCode.hashCode,
      };
}

/// 成功结果
final class Success<T> extends Result<T> {
  /// 成功的数据
  @override
  final T data;

  const Success(this.data);
}

/// 失败结果
final class Failure<T> extends Result<T> {
  /// 失败的异常
  @override
  final AppException exception;

  const Failure(this.exception);
}

/// Result的扩展方法
extension ResultExtensions<T> on Result<T> {
  /// 链式调用多个操作
  Result<R> chain<R>(Result<R> Function(T data) operation) => flatMap(operation);

  /// 异步链式调用
  Future<Result<R>> chainAsync<R>(
    Future<Result<R>> Function(T data) operation,
  ) =>
      flatMapAsync(operation);

  /// 验证数据
  Result<T> validate(
    bool Function(T data) validator,
    AppException Function() onValidationFailed,
  ) =>
      filter(validator, onValidationFailed);

  /// 转换为可空类型
  T? toNullable() => data;

  /// 转换为Future
  Future<Result<T>> toFuture() => Future.value(this);
}

/// 多个Result的组合操作
extension ResultCombinators on Result {
  /// 组合两个Result
  static Result<(T1, T2)> combine2<T1, T2>(
    Result<T1> result1,
    Result<T2> result2,
  ) {
    if (result1 case Failure(exception: final exception)) {
      return Result.failure(exception);
    }
    if (result2 case Failure(exception: final exception)) {
      return Result.failure(exception);
    }
    if (result1 case Success(data: final data1)) {
      if (result2 case Success(data: final data2)) {
        return Result.success((data1, data2));
      }
    }
    // This should never happen but added for completeness
    throw StateError('Unexpected Result state in combine2');
  }

  /// 组合三个Result
  static Result<(T1, T2, T3)> combine3<T1, T2, T3>(
    Result<T1> result1,
    Result<T2> result2,
    Result<T3> result3,
  ) {
    if (result1 case Failure(exception: final exception)) {
      return Result.failure(exception);
    }
    if (result2 case Failure(exception: final exception)) {
      return Result.failure(exception);
    }
    if (result3 case Failure(exception: final exception)) {
      return Result.failure(exception);
    }
    if (result1 case Success(data: final data1)) {
      if (result2 case Success(data: final data2)) {
        if (result3 case Success(data: final data3)) {
          return Result.success((data1, data2, data3));
        }
      }
    }
    // This should never happen but added for completeness
    throw StateError('Unexpected Result state in combine3');
  }

  /// 组合多个Result为列表
  static Result<List<T>> combineList<T>(List<Result<T>> results) {
    final List<T> data = [];
    for (final result in results) {
      switch (result) {
        case Success(data: final value):
          data.add(value);
        case Failure(exception: final exception):
          return Result.failure(exception);
      }
    }
    return Result.success(data);
  }

  /// 异步组合多个Result
  static Future<Result<List<T>>> combineListAsync<T>(
    List<Future<Result<T>>> futures,
  ) async {
    final results = await Future.wait(futures);
    return combineList(results);
  }

  /// 序列化执行多个异步操作
  static Future<Result<List<T>>> sequence<T>(
    List<Future<Result<T>>> futures,
  ) async {
    final List<T> data = [];
    for (final future in futures) {
      final result = await future;
      switch (result) {
        case Success(data: final value):
          data.add(value);
        case Failure(exception: final exception):
          return Result.failure(exception);
      }
    }
    return Result.success(data);
  }

  /// 并行执行多个异步操作，收集所有成功的结果
  static Future<Result<List<T>>> traverse<T>(
    List<Future<Result<T>>> futures,
  ) async {
    final results = await Future.wait(futures);
    final List<T> successData = [];
    
    for (final result in results) {
      if (result case Success(data: final value)) {
        successData.add(value);
      }
    }
    
    // 如果所有操作都成功，返回成功结果
    if (successData.length == results.length) {
      return Result.success(successData);
    }
    
    // 否则返回第一个失败的结果
    for (final result in results) {
      if (result case Failure(exception: final exception)) {
        return Result.failure(exception);
      }
    }
    
    // 这种情况理论上不会发生
    return Result.failure(
      UnknownException(details: 'Unexpected error in traverse'),
    );
  }
}

/// 用于验证的Result构建器
class ResultBuilder<T> {
  final List<String Function(T)> _validators = [];
  final List<AppException Function()> _errorProviders = [];

  /// 添加验证规则
  ResultBuilder<T> addValidator(
    String Function(T) validator,
    AppException Function() errorProvider,
  ) {
    _validators.add(validator);
    _errorProviders.add(errorProvider);
    return this;
  }

  /// 验证数据
  Result<T> validate(T data) {
    for (int i = 0; i < _validators.length; i++) {
      final error = _validators[i](data);
      if (error.isNotEmpty) {
        return Result.failure(_errorProviders[i]());
      }
    }
    return Result.success(data);
  }
}

/// 便捷的验证方法
extension ValidationHelpers<T> on T {
  /// 快速验证
  Result<T> validateWith(ResultBuilder<T> builder) => builder.validate(this);

  /// 简单验证
  Result<T> validateThat(
    bool Function(T) predicate,
    AppException Function() errorProvider,
  ) {
    return predicate(this) ? Result.success(this) : Result.failure(errorProvider());
  }
}

/// 字符串验证扩展
extension StringValidation on String {
  /// 验证非空
  Result<String> validateNotEmpty() {
    return validateThat(
      (str) => str.isNotEmpty,
      () => ParameterNullException(parameterName: 'string'),
    );
  }

  /// 验证长度
  Result<String> validateLength({int? min, int? max}) {
    return validateThat(
      (str) => (min == null || str.length >= min) && (max == null || str.length <= max),
      () => ParameterOutOfRangeException(
        parameterName: 'string length',
        actualValue: length,
        minValue: min,
        maxValue: max,
      ),
    );
  }

  /// 验证格式（正则表达式）
  Result<String> validateFormat(RegExp pattern, {String? formatName}) {
    return validateThat(
      (str) => pattern.hasMatch(str),
      () => InvalidParameterFormatException(
        parameterName: 'string',
        expectedFormat: formatName ?? pattern.pattern,
        actualValue: this,
      ),
    );
  }
}

/// 数字验证扩展
extension NumberValidation on num {
  /// 验证范围
  Result<num> validateRange({num? min, num? max}) {
    return validateThat(
      (value) => (min == null || value >= min) && (max == null || value <= max),
      () => ParameterOutOfRangeException(
        parameterName: 'number',
        actualValue: this,
        minValue: min,
        maxValue: max,
      ),
    );
  }

  /// 验证为正数
  Result<num> validatePositive() {
    return validateThat(
      (value) => value > 0,
      () => ParameterOutOfRangeException(
        parameterName: 'number',
        actualValue: this,
        minValue: 0,
      ),
    );
  }

  /// 验证为非负数
  Result<num> validateNonNegative() {
    return validateThat(
      (value) => value >= 0,
      () => ParameterOutOfRangeException(
        parameterName: 'number',
        actualValue: this,
        minValue: 0,
      ),
    );
  }
}

/// 列表验证扩展
extension ListValidation<T> on List<T> {
  /// 验证非空
  Result<List<T>> validateNotEmpty() {
    return validateThat(
      (list) => list.isNotEmpty,
      () => ParameterNullException(parameterName: 'list'),
    );
  }

  /// 验证长度
  Result<List<T>> validateLength({int? min, int? max}) {
    return validateThat(
      (list) => (min == null || list.length >= min) && (max == null || list.length <= max),
      () => ParameterOutOfRangeException(
        parameterName: 'list length',
        actualValue: length,
        minValue: min,
        maxValue: max,
      ),
    );
  }
} 
/// 技能类型映射工具类
/// 提供统一的谜题类型到技能类型的映射逻辑
library;

import '../constants/app_constants.dart';

/// 技能类型映射器
class SkillTypeMapper {
  // 私有构造函数，防止实例化
  SkillTypeMapper._();

  // 技能类型常量
  static const String skillTypeLogic = 'logic';
  static const String skillTypeSpatial = 'spatial';
  static const String skillTypeCoding = 'coding';

  /// 根据关卡ID获取技能类型
  /// 
  /// [levelId] 关卡ID，格式如: logic_1, spatial_2, coding_3
  /// 返回对应的技能类型字符串
  static String getSkillTypeFromLevelId(String levelId) {
    if (levelId.startsWith('logic_')) {
      return skillTypeLogic;
    } else if (levelId.startsWith('spatial_')) {
      return skillTypeSpatial;
    } else if (levelId.startsWith('coding_')) {
      return skillTypeCoding;
    } else if (levelId.startsWith('mirror_')) {
      return skillTypeSpatial; // 镜像对称属于空间技能
    } else if (levelId.startsWith('numeric_')) {
      return skillTypeLogic; // 数字逻辑属于逻辑技能
    } else {
      return skillTypeLogic; // 默认为逻辑技能
    }
  }

  /// 根据谜题类型获取技能类型
  /// 
  /// [puzzleType] 谜题类型枚举
  /// 返回对应的技能类型字符串
  static String getSkillTypeFromPuzzleType(PuzzleType puzzleType) {
    switch (puzzleType) {
      case PuzzleType.numericLogic:
        return skillTypeLogic;
        
      case PuzzleType.spatialVisualization:
      case PuzzleType.mirrorSymmetry:
      case PuzzleType.graphicPattern3x3:
        return skillTypeSpatial;
        
      case PuzzleType.introToCoding:
        return skillTypeCoding;
    }
  }

  /// 获取所有支持的技能类型
  static List<String> getAllSkillTypes() {
    return [
      skillTypeLogic,
      skillTypeSpatial,
      skillTypeCoding,
    ];
  }

  /// 检查技能类型是否有效
  static bool isValidSkillType(String skillType) {
    return getAllSkillTypes().contains(skillType);
  }

  /// 获取技能类型的显示名称
  static String getSkillTypeDisplayName(String skillType) {
    switch (skillType) {
      case skillTypeLogic:
        return '逻辑推理';
      case skillTypeSpatial:
        return '空间想象';
      case skillTypeCoding:
        return '编程思维';
      default:
        return '未知技能';
    }
  }
} 
import 'package:flutter/material.dart';

/// 游戏动画工具类
/// 
/// 提供简化的动画系统，包含常用的动画效果和时长常量
class GameAnimations {
  // =============================================================================
  // 动画时长常量
  // =============================================================================
  
  /// 快速动画时长 - 150ms
  static const Duration fast = Duration(milliseconds: 150);
  
  /// 正常动画时长 - 300ms
  static const Duration normal = Duration(milliseconds: 300);
  
  /// 慢速动画时长 - 500ms
  static const Duration slow = Duration(milliseconds: 500);
  
  /// 庆祝动画时长 - 1200ms
  static const Duration celebration = Duration(milliseconds: 1200);
  
  /// 页面转场动画时长 - 350ms
  static const Duration pageTransition = Duration(milliseconds: 350);
  
  /// 微交互动画时长 - 100ms
  static const Duration micro = Duration(milliseconds: 100);
  
  // =============================================================================
  // 基础动画工厂方法
  // =============================================================================
  
  /// 淡入动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeIn
  static Animation<double> fadeIn(
    AnimationController controller, {
    Curve curve = Curves.easeIn,
  }) {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: controller, curve: curve),
    );
  }
  
  /// 淡出动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeOut
  static Animation<double> fadeOut(
    AnimationController controller, {
    Curve curve = Curves.easeOut,
  }) {
    return Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: controller, curve: curve),
    );
  }
  
  /// 从右侧滑入动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeOut
  static Animation<Offset> slideInFromRight(
    AnimationController controller, {
    Curve curve = Curves.easeOut,
  }) {
    return Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: controller, curve: curve));
  }
  
  /// 从左侧滑入动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeOut
  static Animation<Offset> slideInFromLeft(
    AnimationController controller, {
    Curve curve = Curves.easeOut,
  }) {
    return Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: controller, curve: curve));
  }
  
  /// 从上方滑入动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeOut
  static Animation<Offset> slideInFromTop(
    AnimationController controller, {
    Curve curve = Curves.easeOut,
  }) {
    return Tween<Offset>(
      begin: const Offset(0.0, -1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: controller, curve: curve));
  }
  
  /// 从下方滑入动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeOut
  static Animation<Offset> slideInFromBottom(
    AnimationController controller, {
    Curve curve = Curves.easeOut,
  }) {
    return Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: controller, curve: curve));
  }
  
  /// 缩放进入动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 elasticOut
  static Animation<double> scaleIn(
    AnimationController controller, {
    Curve curve = Curves.elasticOut,
  }) {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: controller, curve: curve),
    );
  }
  
  /// 缩放退出动画
  /// 
  /// [controller] 动画控制器
  /// [curve] 动画曲线，默认为 easeIn
  static Animation<double> scaleOut(
    AnimationController controller, {
    Curve curve = Curves.easeIn,
  }) {
    return Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: controller, curve: curve),
    );
  }
  
  /// 脉冲动画 (用于强调效果)
  /// 
  /// [controller] 动画控制器
  /// [minScale] 最小缩放值，默认为 1.0
  /// [maxScale] 最大缩放值，默认为 1.2
  /// [curve] 动画曲线，默认为 easeInOut
  static Animation<double> pulse(
    AnimationController controller, {
    double minScale = 1.0,
    double maxScale = 1.2,
    Curve curve = Curves.easeInOut,
  }) {
    return Tween<double>(begin: minScale, end: maxScale).animate(
      CurvedAnimation(parent: controller, curve: curve),
    );
  }
  
  /// 旋转动画
  /// 
  /// [controller] 动画控制器
  /// [turns] 旋转圈数，默认为 1.0 (360度)
  /// [curve] 动画曲线，默认为 linear
  static Animation<double> rotate(
    AnimationController controller, {
    double turns = 1.0,
    Curve curve = Curves.linear,
  }) {
    return Tween<double>(begin: 0.0, end: turns).animate(
      CurvedAnimation(parent: controller, curve: curve),
    );
  }
  
  /// 摇摆动画 (用于错误提示)
  /// 
  /// [controller] 动画控制器
  /// [distance] 摇摆距离，默认为 10.0
  static Animation<double> shake(
    AnimationController controller, {
    double distance = 10.0,
  }) {
    return Tween<double>(begin: -distance, end: distance).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.0, 1.0, curve: Curves.elasticIn),
      ),
    );
  }
  
  // =============================================================================
  // 组合动画方法
  // =============================================================================
  
  /// 播放动画序列
  /// 
  /// [animations] 动画回调函数列表
  /// [interval] 动画间隔时间，默认为 200ms
  static void playSequence(
    List<VoidCallback> animations, {
    Duration interval = const Duration(milliseconds: 200),
  }) {
    for (int i = 0; i < animations.length; i++) {
      Future.delayed(interval * i, animations[i]);
    }
  }
  
  /// 错开显示动画 (用于列表项依次显示)
  /// 
  /// [controllers] 动画控制器列表
  /// [staggerDelay] 错开延迟时间，默认为 100ms
  static void staggeredAnimation(
    List<AnimationController> controllers, {
    Duration staggerDelay = const Duration(milliseconds: 100),
  }) {
    for (int i = 0; i < controllers.length; i++) {
      Future.delayed(staggerDelay * i, () {
        controllers[i].forward();
      });
    }
  }
  
  // =============================================================================
  // 游戏特定动画
  // =============================================================================
  
  /// 成功反馈动画组合
  /// 
  /// 包含缩放 + 淡入 + 轻微旋转
  static AnimationController createSuccessAnimation(
    TickerProvider vsync, {
    Duration duration = const Duration(milliseconds: 800),
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }
  
  /// 失败反馈动画组合
  /// 
  /// 包含摇摆 + 淡出效果
  static AnimationController createFailureAnimation(
    TickerProvider vsync, {
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }
  
  /// 页面进入动画组合
  /// 
  /// 包含淡入 + 从下方滑入
  static AnimationController createPageEnterAnimation(
    TickerProvider vsync, {
    Duration duration = const Duration(milliseconds: 350),
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }
  
  // =============================================================================
  // 辅助方法
  // =============================================================================
  
  /// 创建带延迟的动画
  /// 
  /// [controller] 动画控制器
  /// [delay] 延迟时间
  static void delayedStart(AnimationController controller, Duration delay) {
    Future.delayed(delay, () {
      controller.forward();
    });
  }
  
  /// 创建循环动画
  /// 
  /// [controller] 动画控制器
  /// [reverse] 是否反向循环，默认为 true
  static void startRepeating(AnimationController controller, {bool reverse = true}) {
    if (reverse) {
      controller.repeat(reverse: true);
    } else {
      controller.repeat();
    }
  }
  
  /// 停止循环动画
  /// 
  /// [controller] 动画控制器
  /// [reset] 是否重置到初始状态，默认为 false
  static void stopRepeating(AnimationController controller, {bool reset = false}) {
    controller.stop();
    if (reset) {
      controller.reset();
    }
  }
} 
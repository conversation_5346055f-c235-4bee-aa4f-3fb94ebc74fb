import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../exceptions/app_exceptions.dart';
import 'result.dart';

/// 性能优化器
/// 
/// 提供缓存、批量处理、防抖动等性能优化功能
class PerformanceOptimizer {
  static final PerformanceOptimizer _instance = PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  final Logger _logger = Logger();
  final Map<String, Timer> _debounceTimers = {};
  final Map<String, List<dynamic>> _batchQueues = {};
  final Map<String, Timer> _batchTimers = {};

  /// 防抖动执行
  /// 
  /// [key] 防抖动键
  /// [delay] 延迟时间
  /// [action] 要执行的操作
  void debounce(
    String key,
    Duration delay,
    VoidCallback action,
  ) {
    // 取消之前的定时器
    _debounceTimers[key]?.cancel();
    
    // 创建新的定时器
    _debounceTimers[key] = Timer(delay, () {
      action();
      _debounceTimers.remove(key);
    });
  }

  /// 节流执行
  /// 
  /// [key] 节流键
  /// [interval] 间隔时间
  /// [action] 要执行的操作
  void throttle(
    String key,
    Duration interval,
    VoidCallback action,
  ) {
    // 如果已有定时器在运行，忽略本次调用
    if (_debounceTimers.containsKey(key)) {
      return;
    }
    
    // 立即执行操作
    action();
    
    // 设置冷却时间
    _debounceTimers[key] = Timer(interval, () {
      _debounceTimers.remove(key);
    });
  }

  /// 批量处理队列
  /// 
  /// [key] 批处理键
  /// [item] 要添加的项目
  /// [batchSize] 批处理大小
  /// [maxWaitTime] 最大等待时间
  /// [processor] 批处理器
  void addToBatch<T>(
    String key,
    T item, {
    int batchSize = 10,
    Duration maxWaitTime = const Duration(milliseconds: 100),
    required Future<void> Function(List<T>) processor,
  }) {
    // 初始化队列
    _batchQueues.putIfAbsent(key, () => <T>[]);
    final queue = _batchQueues[key] as List<T>;
    
    // 添加项目到队列
    queue.add(item);
    
    // 检查是否达到批处理大小
    if (queue.length >= batchSize) {
      _processBatch(key, processor);
    } else {
      // 设置最大等待时间定时器
      _batchTimers[key]?.cancel();
      _batchTimers[key] = Timer(maxWaitTime, () {
        _processBatch(key, processor);
      });
    }
  }

  /// 处理批次
  Future<void> _processBatch<T>(
    String key,
    Future<void> Function(List<T>) processor,
  ) async {
    final queue = _batchQueues[key] as List<T>?;
    if (queue == null || queue.isEmpty) return;

    // 取出所有项目
    final items = List<T>.from(queue);
    queue.clear();
    
    // 取消定时器
    _batchTimers[key]?.cancel();
    _batchTimers.remove(key);
    
    try {
      await processor(items);
      _logger.d('Processed batch of ${items.length} items for key: $key');
    } catch (e, stackTrace) {
      _logger.e('Failed to process batch for key: $key', error: e, stackTrace: stackTrace);
    }
  }

  /// 清理资源
  void dispose() {
    // 取消所有定时器
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    for (final timer in _batchTimers.values) {
      timer.cancel();
    }
    
    _debounceTimers.clear();
    _batchTimers.clear();
    _batchQueues.clear();
  }
}

/// 缓存结果装饰器
/// 
/// 为方法调用提供缓存功能
class CachedResultDecorator<T> {
  final Map<String, ({T result, DateTime timestamp})> _cache = {};
  final Duration _ttl;
  final int _maxSize;
  final Logger _logger = Logger();

  CachedResultDecorator({
    Duration ttl = const Duration(minutes: 5),
    int maxSize = 100,
  })  : _ttl = ttl,
        _maxSize = maxSize;

  /// 执行带缓存的操作
  Future<Result<T>> execute(
    String cacheKey,
    Future<Result<T>> Function() operation,
  ) async {
    // 检查缓存
    final cached = _cache[cacheKey];
    if (cached != null) {
      final isExpired = DateTime.now().difference(cached.timestamp) > _ttl;
      if (!isExpired) {
        _logger.d('Cache hit for key: $cacheKey');
        return Result.success(cached.result);
      } else {
        _cache.remove(cacheKey);
      }
    }

    // 执行操作
    final result = await operation();
    
    // 缓存成功结果
    if (result.isSuccess) {
      final data = result.data;
      if (data != null) {
        _cacheResult(cacheKey, data);
      }
    }
    
    return result;
  }

  /// 缓存结果
  void _cacheResult(String key, T result) {
    // 检查缓存大小限制
    if (_cache.length >= _maxSize) {
      _evictOldest();
    }
    
    _cache[key] = (
      result: result,
      timestamp: DateTime.now(),
    );
    
    _logger.d('Cached result for key: $key');
  }

  /// 驱逐最旧的缓存项
  void _evictOldest() {
    if (_cache.isEmpty) return;
    
    String? oldestKey;
    DateTime? oldestTime;
    
    for (final entry in _cache.entries) {
      if (oldestTime == null || entry.value.timestamp.isBefore(oldestTime)) {
        oldestKey = entry.key;
        oldestTime = entry.value.timestamp;
      }
    }
    
    if (oldestKey != null) {
      _cache.remove(oldestKey);
      _logger.d('Evicted oldest cache entry: $oldestKey');
    }
  }

  /// 清除缓存
  void clear() {
    _cache.clear();
    _logger.d('Cleared all cache entries');
  }

  /// 移除特定缓存
  void remove(String key) {
    _cache.remove(key);
    _logger.d('Removed cache entry: $key');
  }

  /// 获取缓存统计
  Map<String, dynamic> getStats() {
    final now = DateTime.now();
    int expiredCount = 0;
    
    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > _ttl) {
        expiredCount++;
      }
    }
    
    return {
      'totalEntries': _cache.length,
      'expiredEntries': expiredCount,
      'maxSize': _maxSize,
      'ttlSeconds': _ttl.inSeconds,
    };
  }
}

/// 批量操作优化器
/// 
/// 专门用于优化高频批量操作
class BatchOperationOptimizer<T, R> {
  final String _operationName;
  final Future<Result<List<R>>> Function(List<T>) _batchProcessor;
  final Duration _batchInterval;
  final int _batchSize;
  final Logger _logger = Logger();
  
  final Queue<({T item, Completer<Result<R>> completer})> _queue = Queue();
  Timer? _batchTimer;
  bool _processing = false;

  BatchOperationOptimizer({
    required String operationName,
    required Future<Result<List<R>>> Function(List<T>) batchProcessor,
    Duration batchInterval = const Duration(milliseconds: 50),
    int batchSize = 20,
  })  : _operationName = operationName,
        _batchProcessor = batchProcessor,
        _batchInterval = batchInterval,
        _batchSize = batchSize;

  /// 添加操作到批次
  Future<Result<R>> add(T item) {
    final completer = Completer<Result<R>>();
    
    _queue.add((item: item, completer: completer));
    
    // 检查是否需要立即处理
    if (_queue.length >= _batchSize) {
      _processBatch();
    } else {
      _scheduleBatch();
    }
    
    return completer.future;
  }

  /// 调度批处理
  void _scheduleBatch() {
    _batchTimer?.cancel();
    _batchTimer = Timer(_batchInterval, _processBatch);
  }

  /// 处理批次
  Future<void> _processBatch() async {
    if (_processing || _queue.isEmpty) return;
    
    _processing = true;
    _batchTimer?.cancel();
    
    try {
      // 取出当前队列中的所有项目
      final batch = <({T item, Completer<Result<R>> completer})>[];
      while (_queue.isNotEmpty && batch.length < _batchSize) {
        batch.add(_queue.removeFirst());
      }
      
      if (batch.isEmpty) return;
      
      final items = batch.map((entry) => entry.item).toList();
      
      _logger.d('Processing batch of ${batch.length} items for $_operationName');
      
      // 执行批处理
      final result = await _batchProcessor(items);
      
      if (result.isSuccess) {
        final results = result.data!;
        
        // 将结果分发给对应的Completer
        for (int i = 0; i < batch.length; i++) {
          if (i < results.length) {
            batch[i].completer.complete(Result.success(results[i]));
          } else {
            batch[i].completer.complete(Result.failure(
              UnknownException(
                details: 'Batch result index out of range',
                originalError: Exception('Batch result index out of range'),
              ),
            ));
          }
        }
      } else {
        // 批处理失败，所有项目都返回失败
        for (final entry in batch) {
          entry.completer.complete(Result.failure(result.exception!));
        }
      }
      
    } catch (e, stackTrace) {
      _logger.e('Batch processing failed for $_operationName', error: e, stackTrace: stackTrace);
      
      // 处理异常，所有等待的项目都返回失败
      while (_queue.isNotEmpty) {
        final entry = _queue.removeFirst();
        entry.completer.complete(Result.failure(
          UnknownException(
            details: 'Batch processing failed for $_operationName',
            originalError: e,
            stackTrace: stackTrace,
          ),
        ));
      }
    } finally {
      _processing = false;
      
      // 如果还有待处理的项目，继续调度
      if (_queue.isNotEmpty) {
        _scheduleBatch();
      }
    }
  }

  /// 获取统计信息
  Map<String, dynamic> getStats() {
    return {
      'operationName': _operationName,
      'queueLength': _queue.length,
      'batchSize': _batchSize,
      'batchIntervalMs': _batchInterval.inMilliseconds,
      'processing': _processing,
    };
  }

  /// 清理资源
  void dispose() {
    _batchTimer?.cancel();
    
    // 取消所有等待的操作
    while (_queue.isNotEmpty) {
      final entry = _queue.removeFirst();
      entry.completer.complete(Result.failure(
        UnknownException(
          details: 'BatchOperationOptimizer disposed',
          originalError: Exception('BatchOperationOptimizer disposed'),
        ),
      ));
    }
  }
}

/// 内存使用优化器
/// 
/// 监控和优化内存使用
class MemoryOptimizer {
  static final MemoryOptimizer _instance = MemoryOptimizer._internal();
  factory MemoryOptimizer() => _instance;
  MemoryOptimizer._internal();

  final Logger _logger = Logger();
  final List<WeakReference<Object>> _trackedObjects = [];
  Timer? _cleanupTimer;

  /// 开始内存监控
  void startMonitoring({
    Duration interval = const Duration(minutes: 5),
  }) {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(interval, (_) => _performCleanup());
    
    _logger.i('Memory monitoring started with interval: ${interval.inMinutes} minutes');
  }

  /// 停止内存监控
  void stopMonitoring() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    
    _logger.i('Memory monitoring stopped');
  }

  /// 跟踪对象
  void trackObject(Object object) {
    _trackedObjects.add(WeakReference(object));
  }

  /// 执行清理
  void _performCleanup() {
    // 清理已被回收的弱引用
    _trackedObjects.removeWhere((ref) => ref.target == null);
    
    // 强制垃圾回收（仅在调试模式下）
    assert(() {
      // 在调试模式下可以手动触发GC
      _logger.d('Performing memory cleanup. Tracked objects: ${_trackedObjects.length}');
      return true;
    }());
  }

  /// 获取内存统计
  Map<String, dynamic> getMemoryStats() {
    final aliveObjects = _trackedObjects.where((ref) => ref.target != null).length;
    final deadReferences = _trackedObjects.length - aliveObjects;
    
    return {
      'trackedObjects': _trackedObjects.length,
      'aliveObjects': aliveObjects,
      'deadReferences': deadReferences,
      'monitoringActive': _cleanupTimer != null,
    };
  }

  /// 立即清理
  void forceCleanup() {
    _performCleanup();
    _logger.d('Forced memory cleanup completed');
  }
}

/// 性能监控器
/// 
/// 监控操作性能并提供统计信息
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Logger _logger = Logger();
  final Map<String, List<Duration>> _operationTimes = {};
  final Map<String, int> _operationCounts = {};

  /// 监控操作性能
  Future<T> monitor<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      
      stopwatch.stop();
      _recordOperation(operationName, stopwatch.elapsed);
      
      return result;
    } catch (e) {
      stopwatch.stop();
      _recordOperation(operationName, stopwatch.elapsed, failed: true);
      rethrow;
    }
  }

  /// 记录操作
  void _recordOperation(String operationName, Duration duration, {bool failed = false}) {
    _operationTimes.putIfAbsent(operationName, () => []);
    _operationTimes[operationName]!.add(duration);
    
    _operationCounts[operationName] = (_operationCounts[operationName] ?? 0) + 1;
    
    // 保持最近的100次记录
    if (_operationTimes[operationName]!.length > 100) {
      _operationTimes[operationName]!.removeAt(0);
    }
    
    if (failed) {
      _logger.w('Operation $operationName failed after ${duration.inMilliseconds}ms');
    } else if (duration.inMilliseconds > 1000) {
      _logger.w('Slow operation detected: $operationName took ${duration.inMilliseconds}ms');
    }
  }

  /// 获取操作统计
  Map<String, dynamic> getOperationStats(String operationName) {
    final times = _operationTimes[operationName] ?? [];
    final count = _operationCounts[operationName] ?? 0;
    
    if (times.isEmpty) {
      return {
        'operationName': operationName,
        'count': count,
        'avgMs': 0,
        'minMs': 0,
        'maxMs': 0,
        'totalMs': 0,
      };
    }
    
    final totalMs = times.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds);
    final avgMs = totalMs / times.length;
    final minMs = times.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b);
    final maxMs = times.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b);
    
    return {
      'operationName': operationName,
      'count': count,
      'avgMs': avgMs.round(),
      'minMs': minMs,
      'maxMs': maxMs,
      'totalMs': totalMs,
    };
  }

  /// 获取所有操作统计
  Map<String, dynamic> getAllStats() {
    final stats = <String, dynamic>{};
    
    for (final operationName in _operationTimes.keys) {
      stats[operationName] = getOperationStats(operationName);
    }
    
    return stats;
  }

  /// 重置统计
  void reset() {
    _operationTimes.clear();
    _operationCounts.clear();
    _logger.d('Performance statistics reset');
  }
} 
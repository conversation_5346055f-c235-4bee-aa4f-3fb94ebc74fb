/// LogicLab 统一验证框架
/// 为Use Cases层提供完整的输入验证能力
library;

import '../exceptions/app_exceptions.dart';
import 'result.dart';

/// 验证结果
typedef ValidationResult = Result<void>;

/// 验证器接口
abstract class Validator<T> {
  /// 验证数据
  ValidationResult validate(T data);

  /// 验证器描述
  String get description;
}

/// 基础验证器实现
class BaseValidator<T> implements Validator<T> {
  final bool Function(T) _predicate;
  final AppException Function() _exceptionProvider;
  final String _description;

  const BaseValidator(
    this._predicate,
    this._exceptionProvider,
    this._description,
  );

  @override
  ValidationResult validate(T data) {
    return _predicate(data)
        ? const Result.success(null)
        : Result.failure(_exceptionProvider());
  }

  @override
  String get description => _description;
}

/// 复合验证器
class CompositeValidator<T> implements Validator<T> {
  final List<Validator<T>> _validators;
  final String _description;

  const CompositeValidator(this._validators, this._description);

  @override
  ValidationResult validate(T data) {
    for (final validator in _validators) {
      final result = validator.validate(data);
      if (result.isFailure) {
        return result;
      }
    }
    return const Result.success(null);
  }

  @override
  String get description => _description;
}

/// 条件验证器
class ConditionalValidator<T> implements Validator<T> {
  final bool Function(T) _condition;
  final Validator<T> _validator;
  final String _description;

  const ConditionalValidator(
    this._condition,
    this._validator,
    this._description,
  );

  @override
  ValidationResult validate(T data) {
    if (_condition(data)) {
      return _validator.validate(data);
    }
    return const Result.success(null);
  }

  @override
  String get description => _description;
}

/// 验证器构建器
class ValidatorBuilder<T> {
  final List<Validator<T>> _validators = [];

  /// 添加验证器
  ValidatorBuilder<T> add(Validator<T> validator) {
    _validators.add(validator);
    return this;
  }

  /// 添加条件验证
  ValidatorBuilder<T> when(bool Function(T) condition, Validator<T> validator) {
    _validators.add(
      ConditionalValidator(
        condition,
        validator,
        'Conditional: ${validator.description}',
      ),
    );
    return this;
  }

  /// 添加自定义验证
  ValidatorBuilder<T> custom(
    bool Function(T) predicate,
    AppException Function() exceptionProvider,
    String description,
  ) {
    _validators.add(BaseValidator(predicate, exceptionProvider, description));
    return this;
  }

  /// 构建复合验证器
  Validator<T> build(String description) {
    return CompositeValidator(_validators, description);
  }
}

/// 内置验证器集合
class Validators {
  Validators._();

  // ============ 通用验证器 ============

  /// 非空验证器
  static Validator<T?> notNull<T>(String parameterName) {
    return BaseValidator<T?>(
      (value) => value != null,
      () => ParameterNullException(parameterName: parameterName),
      'Not null: $parameterName',
    );
  }

  /// 相等验证器
  static Validator<T> equals<T>(T expectedValue, String parameterName) {
    return BaseValidator<T>(
      (value) => value == expectedValue,
      () => InvalidParameterFormatException(
        parameterName: parameterName,
        expectedFormat: expectedValue.toString(),
        actualValue: null,
      ),
      'Equals $expectedValue: $parameterName',
    );
  }

  /// 不相等验证器
  static Validator<T> notEquals<T>(T forbiddenValue, String parameterName) {
    return BaseValidator<T>(
      (value) => value != forbiddenValue,
      () => InvalidParameterFormatException(
        parameterName: parameterName,
        expectedFormat: 'not $forbiddenValue',
        actualValue: null,
      ),
      'Not equals $forbiddenValue: $parameterName',
    );
  }

  /// 包含验证器
  static Validator<T> oneOf<T>(List<T> allowedValues, String parameterName) {
    return BaseValidator<T>(
      (value) => allowedValues.contains(value),
      () => InvalidParameterFormatException(
        parameterName: parameterName,
        expectedFormat: 'one of $allowedValues',
        actualValue: null,
      ),
      'One of $allowedValues: $parameterName',
    );
  }

  // ============ 字符串验证器 ============

  /// 字符串非空验证器
  static Validator<String> stringNotEmpty(String parameterName) {
    return BaseValidator<String>(
      (value) => value.isNotEmpty,
      () => ParameterNullException(parameterName: parameterName),
      'String not empty: $parameterName',
    );
  }

  /// 字符串长度验证器
  static Validator<String> stringLength({
    required String parameterName,
    int? min,
    int? max,
  }) {
    return BaseValidator<String>(
      (value) =>
          (min == null || value.length >= min) &&
          (max == null || value.length <= max),
      () => ParameterOutOfRangeException(
        parameterName: '$parameterName length',
        actualValue: null,
        minValue: min,
        maxValue: max,
      ),
      'String length ${min != null ? 'min: $min' : ''}${max != null ? ' max: $max' : ''}: $parameterName',
    );
  }

  /// 字符串格式验证器
  static Validator<String> stringFormat({
    required RegExp pattern,
    required String parameterName,
    String? formatDescription,
  }) {
    return BaseValidator<String>(
      (value) => pattern.hasMatch(value),
      () => InvalidParameterFormatException(
        parameterName: parameterName,
        expectedFormat: formatDescription ?? pattern.pattern,
        actualValue: null,
      ),
      'String format ${formatDescription ?? pattern.pattern}: $parameterName',
    );
  }

  /// 昵称验证器
  static Validator<String> nickname() {
    return ValidatorBuilder<String>()
        .add(stringNotEmpty('nickname'))
        .add(stringLength(parameterName: 'nickname', min: 1, max: 20))
        .add(
          stringFormat(
            pattern: RegExp(r'^[a-zA-Z0-9\u4e00-\u9fa5_-]+$'),
            parameterName: 'nickname',
            formatDescription: '字母、数字、中文、下划线或短横线',
          ),
        )
        .build('Nickname validation');
  }

  /// 用户ID验证器
  static Validator<String> userId() {
    return ValidatorBuilder<String>()
        .add(stringNotEmpty('userId'))
        .add(
          stringFormat(
            pattern: RegExp(r'^[a-zA-Z0-9-]+$'),
            parameterName: 'userId',
            formatDescription: 'UUID format',
          ),
        )
        .build('User ID validation');
  }

  /// 谜题ID验证器
  static Validator<String> puzzleId() {
    return ValidatorBuilder<String>()
        .add(stringNotEmpty('puzzleId'))
        .add(stringLength(parameterName: 'puzzleId', min: 1, max: 100))
        .build('Puzzle ID validation');
  }

  // ============ 数字验证器 ============

  /// 数字范围验证器
  static Validator<num> numberRange({
    required String parameterName,
    num? min,
    num? max,
  }) {
    return BaseValidator<num>(
      (value) => (min == null || value >= min) && (max == null || value <= max),
      () => ParameterOutOfRangeException(
        parameterName: parameterName,
        actualValue: null,
        minValue: min,
        maxValue: max,
      ),
      'Number range ${min != null ? 'min: $min' : ''}${max != null ? ' max: $max' : ''}: $parameterName',
    );
  }

  /// 整数范围验证器
  static Validator<int> intRange({
    required String parameterName,
    int? min,
    int? max,
  }) {
    return BaseValidator<int>(
      (value) => (min == null || value >= min) && (max == null || value <= max),
      () => ParameterOutOfRangeException(
        parameterName: parameterName,
        actualValue: null,
        minValue: min,
        maxValue: max,
      ),
      'Int range ${min != null ? 'min: $min' : ''}${max != null ? ' max: $max' : ''}: $parameterName',
    );
  }

  /// 正数验证器
  static Validator<num> positive(String parameterName) {
    return BaseValidator<num>(
      (value) => value > 0,
      () => ParameterOutOfRangeException(
        parameterName: parameterName,
        actualValue: null,
        minValue: 0,
      ),
      'Positive number: $parameterName',
    );
  }

  /// 正整数验证器
  static Validator<int> positiveInt(String parameterName) {
    return BaseValidator<int>(
      (value) => value > 0,
      () => ParameterOutOfRangeException(
        parameterName: parameterName,
        actualValue: null,
        minValue: 0,
      ),
      'Positive integer: $parameterName',
    );
  }

  /// 非负数验证器
  static Validator<num> nonNegative(String parameterName) {
    return BaseValidator<num>(
      (value) => value >= 0,
      () => ParameterOutOfRangeException(
        parameterName: parameterName,
        actualValue: null,
        minValue: 0,
      ),
      'Non-negative number: $parameterName',
    );
  }

  /// 非负整数验证器
  static Validator<int> nonNegativeInt(String parameterName) {
    return BaseValidator<int>(
      (value) => value >= 0,
      () => ParameterOutOfRangeException(
        parameterName: parameterName,
        actualValue: null,
        minValue: 0,
      ),
      'Non-negative integer: $parameterName',
    );
  }

  /// 星级评分验证器
  static Validator<int> starRating() {
    return ValidatorBuilder<int>()
        .add(intRange(parameterName: 'starRating', min: 0, max: 3))
        .build('Star rating validation');
  }

  /// 技能等级验证器
  static Validator<int> skillLevel() {
    return ValidatorBuilder<int>()
        .add(intRange(parameterName: 'skillLevel', min: 1, max: 100))
        .build('Skill level validation');
  }

  /// 游戏时间验证器（秒）
  static Validator<int> gameTimeSeconds() {
    return ValidatorBuilder<int>()
        .add(nonNegativeInt('gameTimeSeconds'))
        .add(intRange(parameterName: 'gameTimeSeconds', max: 7200)) // 最多2小时
        .build('Game time validation');
  }

  // ============ 集合验证器 ============

  /// 列表非空验证器
  static Validator<List<T>> listNotEmpty<T>(String parameterName) {
    return BaseValidator<List<T>>(
      (value) => value.isNotEmpty,
      () => ParameterNullException(parameterName: parameterName),
      'List not empty: $parameterName',
    );
  }

  /// 列表长度验证器
  static Validator<List<T>> listLength<T>({
    required String parameterName,
    int? min,
    int? max,
  }) {
    return BaseValidator<List<T>>(
      (value) =>
          (min == null || value.length >= min) &&
          (max == null || value.length <= max),
      () => ParameterOutOfRangeException(
        parameterName: '$parameterName length',
        actualValue: null,
        minValue: min,
        maxValue: max,
      ),
      'List length ${min != null ? 'min: $min' : ''}${max != null ? ' max: $max' : ''}: $parameterName',
    );
  }

  /// 列表元素验证器
  static Validator<List<T>> listElements<T>(
    Validator<T> elementValidator,
    String parameterName,
  ) {
    return BaseValidator<List<T>>(
      (value) {
        for (final element in value) {
          if (elementValidator.validate(element).isFailure) {
            return false;
          }
        }
        return true;
      },
      () => InvalidParameterFormatException(
        parameterName: parameterName,
        expectedFormat: 'valid elements',
      ),
      'List elements validation: $parameterName',
    );
  }

  // ============ 枚举验证器 ============

  /// 谜题类型验证器
  static Validator<String> puzzleType() {
    return oneOf([
      'GRAPHIC_PATTERN_3X3',
      'SPATIAL_VISUALIZATION',
      'NUMERIC_LOGIC',
      'INTRO_TO_CODING',
      'MIRROR_SYMMETRY',
    ], 'puzzleType');
  }

  /// 头像ID验证器
  static Validator<String> avatarId() {
    return ValidatorBuilder<String>()
        .add(stringNotEmpty('avatarId'))
        .add(
          stringFormat(
            pattern: RegExp(r'^avatar_[0-9]+$'),
            parameterName: 'avatarId',
            formatDescription: 'avatar_数字格式',
          ),
        )
        .build('Avatar ID validation');
  }

  // ============ 业务逻辑验证器 ============

  /// 用户数量限制验证器
  static Validator<int> userCountLimit() {
    return ValidatorBuilder<int>()
        .add(intRange(parameterName: 'userCount', min: 0, max: 4))
        .build('User count limit validation');
  }

  /// 提示次数验证器
  static Validator<int> hintCount() {
    return ValidatorBuilder<int>()
        .add(nonNegativeInt('hintCount'))
        .add(intRange(parameterName: 'hintCount', max: 3)) // 最多3次提示
        .build('Hint count validation');
  }

  /// 游戏难度验证器
  static Validator<int> gameDifficulty() {
    return ValidatorBuilder<int>()
        .add(intRange(parameterName: 'difficulty', min: 1, max: 20))
        .build('Game difficulty validation');
  }

  // ============ 复合对象验证器 ============

  /// 用户创建参数验证器
  static Validator<Map<String, dynamic>> createUserParams() {
    return BaseValidator<Map<String, dynamic>>(
      (params) {
        // 验证必需字段存在
        if (!params.containsKey('nickname') ||
            !params.containsKey('avatarId')) {
          return false;
        }

        // 验证昵称
        final nicknameResult = nickname().validate(
          params['nickname'] as String,
        );
        if (nicknameResult.isFailure) return false;

        // 验证头像ID
        final avatarResult = avatarId().validate(params['avatarId'] as String);
        if (avatarResult.isFailure) return false;

        return true;
      },
      () => InvalidParameterFormatException(
        parameterName: 'createUserParams',
        expectedFormat: 'valid user creation parameters',
      ),
      'Create user parameters validation',
    );
  }

  /// 游戏进度参数验证器
  static Validator<Map<String, dynamic>> gameProgressParams() {
    return BaseValidator<Map<String, dynamic>>(
      (params) {
        // 验证必需字段
        final requiredFields = ['userId', 'puzzleId', 'completed'];
        for (final field in requiredFields) {
          if (!params.containsKey(field)) return false;
        }

        // 验证用户ID
        final userIdResult = userId().validate(params['userId'] as String);
        if (userIdResult.isFailure) return false;

        // 验证谜题ID
        final puzzleIdResult = puzzleId().validate(
          params['puzzleId'] as String,
        );
        if (puzzleIdResult.isFailure) return false;

        // 验证完成状态
        if (params['completed'] is! bool) return false;

        // 如果有游戏时间，验证游戏时间
        if (params.containsKey('gameTimeSeconds')) {
          final timeResult = gameTimeSeconds().validate(
            params['gameTimeSeconds'] as int,
          );
          if (timeResult.isFailure) return false;
        }

        // 如果有星级评分，验证星级评分
        if (params.containsKey('starRating')) {
          final starResult = starRating().validate(params['starRating'] as int);
          if (starResult.isFailure) return false;
        }

        return true;
      },
      () => InvalidParameterFormatException(
        parameterName: 'gameProgressParams',
        expectedFormat: 'valid game progress parameters',
      ),
      'Game progress parameters validation',
    );
  }

  /// 提示请求参数验证器
  static Validator<Map<String, dynamic>> hintRequestParams() {
    return BaseValidator<Map<String, dynamic>>(
      (params) {
        // 验证必需字段
        if (!params.containsKey('puzzleId') ||
            !params.containsKey('hintLevel')) {
          return false;
        }

        // 验证谜题ID
        final puzzleIdResult = puzzleId().validate(
          params['puzzleId'] as String,
        );
        if (puzzleIdResult.isFailure) return false;

        // 验证提示级别
        final hintLevelResult = numberRange(
          parameterName: 'hintLevel',
          min: 1,
          max: 3,
        ).validate(params['hintLevel'] as int);
        if (hintLevelResult.isFailure) return false;

        return true;
      },
      () => InvalidParameterFormatException(
        parameterName: 'hintRequestParams',
        expectedFormat: 'valid hint request parameters',
      ),
      'Hint request parameters validation',
    );
  }
}

/// 验证扩展方法
extension ValidationExtensions<T> on T {
  /// 使用验证器验证
  ValidationResult validateWith(Validator<T> validator) {
    return validator.validate(this);
  }

  /// 使用多个验证器验证
  ValidationResult validateWithAll(List<Validator<T>> validators) {
    for (final validator in validators) {
      final result = validator.validate(this);
      if (result.isFailure) {
        return result;
      }
    }
    return const Result.success(null);
  }
}

/// 验证结果扩展
extension ValidationResultExtensions on ValidationResult {
  /// 转换为Result`<T>`
  Result<T> toResult<T>(T value) {
    return fold(
      (_) => Result.success(value),
      (exception) => Result.failure(exception),
    );
  }

  /// 链式验证
  ValidationResult then(ValidationResult Function() nextValidation) {
    return fold(
      (_) => nextValidation(),
      (exception) => Result.failure(exception),
    );
  }
}

/// 批量验证工具
class BatchValidator {
  final List<(String name, ValidationResult result)> _results = [];

  /// 添加验证结果
  BatchValidator add(String name, ValidationResult result) {
    _results.add((name, result));
    return this;
  }

  /// 验证并添加
  BatchValidator validate<T>(String name, T value, Validator<T> validator) {
    _results.add((name, validator.validate(value)));
    return this;
  }

  /// 获取所有验证结果
  List<(String name, ValidationResult result)> get results =>
      List.unmodifiable(_results);

  /// 是否全部通过
  bool get isValid => _results.every((result) => result.$2.isSuccess);

  /// 获取第一个错误
  AppException? get firstError {
    for (final (_, result) in _results) {
      if (result.isFailure) {
        return result.exception;
      }
    }
    return null;
  }

  /// 获取所有错误
  List<AppException> get allErrors {
    return _results
        .where((result) => result.$2.isFailure)
        .map((result) => result.$2.exception!)
        .toList();
  }

  /// 转换为ValidationResult
  ValidationResult toResult() {
    final error = firstError;
    return error != null ? Result.failure(error) : const Result.success(null);
  }

  /// 清空结果
  void clear() {
    _results.clear();
  }
}

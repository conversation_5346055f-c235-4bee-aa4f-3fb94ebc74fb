import 'package:get_it/get_it.dart';

// Services
import '../services/user_service.dart';
import '../services/puzzle_engine.dart';
import '../services/achievement_service.dart';
import '../services/theme_service.dart';
import '../services/config_service.dart';
import '../services/cache_service.dart';
import '../services/data_export_service.dart';
import '../services/analytics_service.dart';

// Core Managers and Generators
import '../core/managers/asset_manager.dart';
import '../core/generators/puzzle_generator.dart';

// Repositories
import '../domain/repositories/user_repository.dart';
import '../domain/repositories/puzzle_repository.dart';
import '../data/repositories/user_repository_impl.dart';
import '../data/repositories/puzzle_repository_impl.dart';

// Use Cases
import '../domain/usecases/create_user_usecase.dart';
import '../domain/usecases/play_puzzle_usecase.dart';
import '../domain/usecases/save_progress_usecase.dart';
import '../domain/usecases/generate_hint_usecase.dart';
import '../domain/usecases/unlock_achievement_usecase.dart';

// BLoCs
import '../presentation/bloc/user/user_bloc.dart';
import '../presentation/bloc/puzzle/puzzle_bloc.dart';
import '../presentation/bloc/hint/hint_bloc.dart';
import '../presentation/bloc/achievement/achievement_bloc.dart';
import '../presentation/bloc/settings/settings_bloc.dart';

/// 全局服务定位器实例
final sl = GetIt.instance;

/// 全局服务定位器别名，用于在UI层获取依赖
final getIt = sl;

/// 设置依赖注入
/// 在应用启动时调用，注册所有服务和仓库
Future<void> setupServiceLocator() async {
  // ================================
  // Services (单例服务)
  // ================================

  // 用户服务 - 处理本地用户数据
  sl.registerLazySingleton<UserService>(() => UserService());
  
  // 初始化用户服务
  await sl<UserService>().initialize();

  // 素材管理器 - 处理素材分类和随机组合生成
  sl.registerLazySingleton<AssetManager>(() => AssetManager.instance);
  
  // 题目生成器 - 处理基于分类系统的题目生成
  sl.registerLazySingleton<PuzzleGenerator>(() => PuzzleGenerator.instance);

  // 谜题引擎 - 处理谜题逻辑和验证
  sl.registerLazySingleton<PuzzleEngine>(() => PuzzleEngine());
  
  // 成就服务 - 处理成就数据和业务逻辑
  sl.registerLazySingleton<AchievementService>(() => AchievementService());

  // 主题服务 - 处理主题持久化和管理
  sl.registerLazySingleton<ThemeService>(() => ThemeService());
  
  // 初始化需要异步初始化的服务
  await sl<PuzzleEngine>().initialize();
  await sl<ThemeService>().initialize();

  // 配置服务 - 处理应用配置管理
  sl.registerLazySingleton<ConfigService>(() => ConfigService());

  // 缓存服务 - 处理缓存管理
  sl.registerLazySingleton<CacheService>(() => CacheService());

  // 数据导出服务 - 处理数据导出
  sl.registerLazySingleton<DataExportService>(() => DataExportService());

  // 分析统计服务 - 处理用户行为统计
  sl.registerLazySingleton<AnalyticsService>(() => AnalyticsService());

  // ================================
  // Repositories (仓库层)
  // ================================

  // 用户仓库 - Domain层接口，Data层实现
  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(sl<UserService>()),
  );

  // 谜题仓库 - Domain层接口，Data层实现
  sl.registerLazySingleton<PuzzleRepository>(
    () => PuzzleRepositoryImpl(sl<PuzzleEngine>()),
  );

  // ================================
  // Use Cases (用例层)
  // ================================

  // 创建用户用例
  sl.registerLazySingleton<CreateUserUseCase>(
    () => CreateUserUseCase(sl<UserRepository>()),
  );

  // 游戏谜题用例
  sl.registerLazySingleton<PlayPuzzleUseCase>(
    () => PlayPuzzleUseCase(sl<PuzzleRepository>(), sl<UserRepository>()),
  );

  // 保存进度用例
  sl.registerLazySingleton<SaveProgressUseCase>(
    () => SaveProgressUseCase(sl<UserRepository>()),
  );

  // 生成提示用例
  sl.registerLazySingleton<GenerateHintUseCase>(
    () => GenerateHintUseCase(sl<PuzzleRepository>(), sl<UserRepository>()),
  );

  // 解锁成就用例
  sl.registerLazySingleton<UnlockAchievementUseCase>(
    () => UnlockAchievementUseCase(userRepository: sl<UserRepository>()),
  );

  // ================================
  // BLoCs (状态管理)
  // ================================

  // 用户状态管理BLoC
  sl.registerFactory<UserBloc>(
    () => UserBloc(
      createUserUseCase: sl<CreateUserUseCase>(),
      userRepository: sl<UserRepository>(),
    ),
  );

  // 谜题游戏状态管理BLoC
  sl.registerFactory<PuzzleBloc>(
    () => PuzzleBloc(
      playPuzzleUseCase: sl<PlayPuzzleUseCase>(),
      saveProgressUseCase: sl<SaveProgressUseCase>(),
      puzzleRepository: sl<PuzzleRepository>(),
      userRepository: sl<UserRepository>(),
    ),
  );

  // 提示系统状态管理BLoC
  sl.registerFactory<HintBloc>(
    () => HintBloc(
      generateHintUseCase: sl<GenerateHintUseCase>(),
      userRepository: sl<UserRepository>(),
      configService: sl<ConfigService>(),
    ),
  );

  // 成就系统状态管理BLoC
  sl.registerFactory<AchievementBloc>(
    () => AchievementBloc(
      unlockAchievementUseCase: sl<UnlockAchievementUseCase>(),
      userRepository: sl<UserRepository>(),
      achievementService: sl<AchievementService>(),
    ),
  );

  // 设置页面状态管理BLoC
  sl.registerFactory<SettingsBloc>(
    () => SettingsBloc(userRepository: sl<UserRepository>()),
  );
}

/// 重置服务定位器
/// 主要用于测试环境
void resetServiceLocator() {
  sl.reset();
}

/// 注册测试用的模拟依赖
/// 在测试中替换真实的服务实现
void setupTestServiceLocator() {
  // 在测试文件中实现
  // 例如：sl.registerLazySingleton<UserRepository>(() => MockUserRepository());
}

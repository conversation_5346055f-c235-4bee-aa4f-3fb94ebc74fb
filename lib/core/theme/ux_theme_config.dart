import 'package:flutter/material.dart';

/// UX主题配置类
///
/// 提供应用的核心设计token，包括颜色、间距、圆角等
/// 这些配置遵循Material Design 3规范，同时适配儿童应用的特殊需求
class UXThemeConfig {
  UXThemeConfig._();

  // ============ 主色调 ============
  /// 主品牌色 - 明亮的蓝色，代表智慧和探索
  static const Color primaryBlue = Color(0xFF2196F3);

  /// 次要品牌色 - 温暖的橙色，代表活力和创造力
  static const Color secondaryOrange = Color(0xFFFF9800);

  // ============ 强调色 ============
  /// 强调蓝色 - 用于重要按钮和链接
  static const Color accentBlue = Color(0xFF1976D2);

  /// 强调青色 - 用于成功状态和正面反馈
  static const Color accentTeal = Color(0xFF009688);

  /// 强调黄色 - 用于警告和注意事项
  static const Color accentYellow = Color(0xFFFFC107);

  // ============ 功能色 ============
  /// 成功色 - 绿色系，表示正确、完成、成功
  static const Color successGreen = Color(0xFF4CAF50);

  /// 错误色 - 红色系，表示错误、失败、危险
  static const Color errorRed = Color(0xFFF44336);

  /// 警告色 - 橙色系，表示警告、注意
  static const Color warningOrange = Color(0xFFFF9800);

  /// 警告色别名 - 为了兼容性
  static const Color warning = Color(0xFFFF9800);

  /// 信息色 - 蓝色系，表示提示、信息
  static const Color infoBlue = Color(0xFF2196F3);

  // ============ 中性色 ============
  /// 深色文本 - 主要文本颜色
  static const Color textDark = Color(0xFF212121);

  /// 中等文本 - 次要文本颜色
  static const Color textMedium = Color(0xFF757575);

  /// 浅色文本 - 辅助文本颜色
  static const Color textLight = Color(0xFF9E9E9E);

  /// 次要文本 - 用于不重要的信息
  static const Color textSecondary = Color(0xFF616161);

  // ============ 背景色 ============
  /// 主背景色 - 应用的主要背景
  static const Color backgroundPrimary = Color(0xFFFAFAFA);

  /// 次要背景色 - 卡片和容器背景
  static const Color backgroundSecondary = Color(0xFFFFFFFF);

  /// 表面背景色 - 浮动元素背景
  static const Color backgroundSurface = Color(0xFFFFFFFF);

  // ============ 边框和分割线 ============
  /// 主边框色
  static const Color borderPrimary = Color(0xFFE0E0E0);

  /// 次要边框色
  static const Color borderSecondary = Color(0xFFEEEEEE);

  /// 分割线颜色
  static const Color divider = Color(0xFFBDBDBD);

  // ============ 阴影色 ============
  /// 轻阴影
  static const Color shadowLight = Color(0x1A000000);

  /// 中等阴影
  static const Color shadowMedium = Color(0x33000000);

  /// 深阴影
  static const Color shadowDark = Color(0x4D000000);

  // ============ 间距 ============
  /// 超小间距 4dp
  static const double paddingXS = 4.0;

  /// 小间距 8dp
  static const double paddingS = 8.0;

  /// 中等间距 16dp
  static const double paddingM = 16.0;

  /// 大间距 24dp
  static const double paddingL = 24.0;

  /// 超大间距 32dp
  static const double paddingXL = 32.0;

  // ============ 圆角 ============
  /// 小圆角
  static const double radiusS = 4.0;

  /// 中等圆角
  static const double radiusM = 8.0;

  /// 大圆角
  static const double radiusL = 12.0;

  /// 超大圆角
  static const double radiusXL = 16.0;

  // ============ 字体大小 ============
  /// 超大标题
  static const double fontSizeXL = 32.0;

  /// 大标题
  static const double fontSizeL = 24.0;

  /// 中等标题
  static const double fontSizeM = 20.0;

  /// 正文
  static const double fontSizeBody = 16.0;

  /// 小文本
  static const double fontSizeS = 14.0;

  /// 超小文本
  static const double fontSizeXS = 12.0;

  // ============ 动画时长 ============
  /// 快速动画 150ms
  static const Duration animationFast = Duration(milliseconds: 150);

  /// 正常动画 300ms
  static const Duration animationNormal = Duration(milliseconds: 300);

  /// 慢速动画 500ms
  static const Duration animationSlow = Duration(milliseconds: 500);

  // ============ 主题相关的扩展方法 ============

  /// 获取当前主题的颜色方案
  static ColorScheme get lightColorScheme => const ColorScheme.light(
    primary: primaryBlue,
    secondary: secondaryOrange,
    surface: backgroundSurface,
    error: errorRed,
    onPrimary: Colors.white,
    onSecondary: Colors.white,
    onSurface: textDark,
    onError: Colors.white,
  );

  /// 获取暗色主题的颜色方案
  static ColorScheme get darkColorScheme => const ColorScheme.dark(
    primary: primaryBlue,
    secondary: secondaryOrange,
    surface: Color(0xFF121212),
    error: errorRed,
    onPrimary: Colors.white,
    onSecondary: Colors.white,
    onSurface: Colors.white,
    onError: Colors.white,
  );
}

/// UXThemeConfig的扩展方法
extension UXThemeConfigExtension on BuildContext {
  /// 获取当前颜色方案
  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  /// 获取当前文本主题
  TextTheme get textTheme => Theme.of(this).textTheme;
}

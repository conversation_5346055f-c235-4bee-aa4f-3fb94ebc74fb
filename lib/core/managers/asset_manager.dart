import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../constants/game_asset_categories.dart';
import '../utils/result.dart';
import '../exceptions/app_exceptions.dart';

/// 智能素材管理器
///
/// 支持分类素材的加载、缓存、随机组合生成和路径管理
class AssetManager {
  static AssetManager? _instance;
  static AssetManager get instance => _instance ??= AssetManager._();

  AssetManager._();

  // 素材基础路径
  static const String _basePath = 'assets/images';

  // 调试模式
  static bool _debugMode = false;

  // 素材缓存
  final Map<String, String> _assetCache = {};

  // 素材组合缓存
  final Map<String, List<AssetCombination>> _combinationCache = {};

  // 随机数生成器
  final Random _random = Random();

  /// 启用调试模式
  static void enableDebugMode() {
    _debugMode = true;
    if (kDebugMode) {
      debugPrint('AssetManager: Debug mode enabled');
    }
  }

  /// 禁用调试模式
  static void disableDebugMode() {
    _debugMode = false;
  }

  // =============================================================================
  // 基础素材路径管理
  // =============================================================================

  /// 获取素材路径
  String _getAssetPath(String relativePath) {
    final fullPath = '$_basePath/$relativePath.png';

    if (_debugMode) {
      debugPrint('AssetManager: Loading asset: $fullPath');
    }

    return fullPath;
  }

  /// 检查素材是否存在
  Future<bool> _assetExists(String assetPath) async {
    try {
      await rootBundle.load(assetPath);
      return true;
    } catch (e) {
      if (_debugMode) {
        debugPrint('AssetManager: Asset not found: $assetPath');
      }
      return false;
    }
  }

  /// 获取备用素材路径
  String _getFallbackAssetPath(String gameType, String category) {
    return _getAssetPath('$gameType/fallback/${category}_placeholder');
  }

  // =============================================================================
  // 镜像对称游戏素材管理
  // =============================================================================

  /// 获取镜像对称游戏组合素材
  Future<Result<String>> getMirrorSymmetryAsset(
    MirrorSymmetryAssetCombination combination,
  ) async {
    try {
      final assetPath = _getAssetPath(combination.generateAssetPath());

      // 检查素材是否存在
      if (await _assetExists(assetPath)) {
        return Result.success(assetPath);
      }

      // 尝试分层组合生成
      final layeredPath = await _generateLayeredMirrorAsset(combination);
      if (layeredPath != null) {
        return Result.success(layeredPath);
      }

      // 返回备用素材
      final fallbackPath = _getFallbackAssetPath('mirror_symmetry', 'clothing');
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
        return Result.failure(
          DataAccessException(
            errorCode: ErrorCodes.dataAccessError,
            message: '镜像对称素材加载失败: ${combination.name}',
            originalError: e,
            stackTrace: stackTrace,
          ),
      );
    }
  }

  /// 生成分层镜像对称素材
  Future<String?> _generateLayeredMirrorAsset(
    MirrorSymmetryAssetCombination combination,
  ) async {
    try {
      // 尝试加载基础衣服素材
      final clothingPath = _getAssetPath(
        'mirror_symmetry/clothes/${combination.clothingType.assetKey}',
      );

      if (await _assetExists(clothingPath)) {
        // 如果基础素材存在，可以在运行时叠加花纹和颜色
        return clothingPath;
      }

      return null;
    } catch (e) {
      if (_debugMode) {
        debugPrint('AssetManager: Failed to generate layered mirror asset: $e');
      }
      return null;
    }
  }

  /// 获取镜像对称游戏的花纹素材
  Future<Result<String>> getMirrorPatternAsset(PatternType patternType) async {
    try {
      final assetPath = _getAssetPath(
        'mirror_symmetry/patterns/${patternType.assetKey}',
      );

      if (await _assetExists(assetPath)) {
        return Result.success(assetPath);
      }

      final fallbackPath = _getFallbackAssetPath('mirror_symmetry', 'pattern');
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
        return Result.failure(
          DataAccessException(
            errorCode: ErrorCodes.dataAccessError,
            message: '花纹素材加载失败: ${patternType.displayName}',
            originalError: e,
            stackTrace: stackTrace,
          ),
      );
    }
  }

  // =============================================================================
  // 图形推理游戏素材管理
  // =============================================================================

  /// 获取图形推理游戏组合素材
  Future<Result<String>> getGraphicPatternAsset(
    GraphicPatternAssetCombination combination,
  ) async {
    try {
      final assetPath = _getAssetPath(combination.generateAssetPath());

      if (await _assetExists(assetPath)) {
        return Result.success(assetPath);
      }

      // 尝试分层组合生成
      final layeredPath = await _generateLayeredGraphicAsset(combination);
      if (layeredPath != null) {
        return Result.success(layeredPath);
      }

      final fallbackPath = _getFallbackAssetPath('graphic_pattern', 'shape');
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
        return Result.failure(
          DataAccessException(
            errorCode: ErrorCodes.dataAccessError,
            message: '图形推理素材加载失败: ${combination.name}',
            originalError: e,
            stackTrace: stackTrace,
          ),
      );
    }
  }

  /// 生成分层图形推理素材
  Future<String?> _generateLayeredGraphicAsset(
    GraphicPatternAssetCombination combination,
  ) async {
    try {
      // 尝试加载基础图形素材
      final shapePath = _getAssetPath(
        'graphic_pattern/shapes/${combination.shapeType.assetKey}',
      );

      if (await _assetExists(shapePath)) {
        return shapePath;
      }

      return null;
    } catch (e) {
      if (_debugMode) {
        debugPrint(
          'AssetManager: Failed to generate layered graphic asset: $e',
        );
      }
      return null;
    }
  }

  // =============================================================================
  // 空间想象游戏素材管理
  // =============================================================================

  /// 获取空间想象游戏组合素材
  Future<Result<String>> getSpatialVisualizationAsset(
    SpatialVisualizationAssetCombination combination,
  ) async {
    try {
      final assetPath = _getAssetPath(combination.generateAssetPath());

      if (await _assetExists(assetPath)) {
        return Result.success(assetPath);
      }

      // 尝试分层组合生成
      final layeredPath = await _generateLayeredSpatialAsset(combination);
      if (layeredPath != null) {
        return Result.success(layeredPath);
      }

      final fallbackPath = _getFallbackAssetPath(
        'spatial_visualization',
        'shape',
      );
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
      return Result.failure(
        DataAccessException(
          errorCode: 'SPATIAL_ASSET_LOAD_FAILED',
          message: '空间想象素材加载失败: ${combination.name}',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 生成分层空间想象素材
  Future<String?> _generateLayeredSpatialAsset(
    SpatialVisualizationAssetCombination combination,
  ) async {
    try {
      // 尝试加载基础3D形状素材
      final shapePath = _getAssetPath(
        'spatial_visualization/shapes/${combination.shapeType.assetKey}_${combination.viewType.assetKey}',
      );

      if (await _assetExists(shapePath)) {
        return shapePath;
      }

      return null;
    } catch (e) {
      if (_debugMode) {
        debugPrint(
          'AssetManager: Failed to generate layered spatial asset: $e',
        );
      }
      return null;
    }
  }

  // =============================================================================
  // 编程启蒙游戏素材管理
  // =============================================================================

  /// 获取编程启蒙游戏角色素材
  Future<Result<String>> getCodingCharacterAsset(
    CodingAssetCombination combination,
  ) async {
    try {
      final assetPath = _getAssetPath(combination.generateCharacterAssetPath());

      if (await _assetExists(assetPath)) {
        return Result.success(assetPath);
      }

      // 尝试基础角色素材
      final basicPath = _getAssetPath(
        'intro_coding/characters/${combination.characterType.assetKey}_${combination.characterState.assetKey}',
      );

      if (await _assetExists(basicPath)) {
        return Result.success(basicPath);
      }

      final fallbackPath = _getFallbackAssetPath('intro_coding', 'character');
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
        return Result.failure(
          DataAccessException(
            errorCode: ErrorCodes.dataAccessError,
            message: '编程角色素材加载失败: ${combination.name}',
            originalError: e,
            stackTrace: stackTrace,
          ),
      );
    }
  }

  /// 获取编程启蒙游戏环境素材
  Future<Result<String>> getCodingEnvironmentAsset(
    CodingAssetCombination combination,
  ) async {
    try {
      final assetPath = _getAssetPath(
        combination.generateEnvironmentAssetPath(),
      );

      if (await _assetExists(assetPath)) {
        return Result.success(assetPath);
      }

      final fallbackPath = _getFallbackAssetPath('intro_coding', 'environment');
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
      return Result.failure(
        DataAccessException(
          errorCode: 'CODING_ENVIRONMENT_ASSET_LOAD_FAILED',
          message: '编程环境素材加载失败: ${combination.name}',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // =============================================================================
  // 随机组合生成
  // =============================================================================

  /// 生成随机镜像对称素材组合
  Future<Result<MirrorSymmetryAssetCombination>>
  generateRandomMirrorSymmetryAsset({
    int? difficulty,
    List<String>? requiredTags,
    List<ClothingType>? allowedClothingTypes,
    List<PatternType>? allowedPatternTypes,
    List<ColorType>? allowedColorTypes,
  }) async {
    try {
      final clothingTypes = allowedClothingTypes ?? ClothingType.values;
      final patternTypes = allowedPatternTypes ?? PatternType.values;
      final colorTypes = allowedColorTypes ?? ColorType.values;

      final clothingType = clothingTypes[_random.nextInt(clothingTypes.length)];
      final patternType = patternTypes[_random.nextInt(patternTypes.length)];
        return Result.failure(
          DataAccessException(
            errorCode: ErrorCodes.dataAccessError,
            message: '空间想象素材加载失败: ${combination.name}',
            originalError: e,
            stackTrace: stackTrace,
          ),
            '${clothingType.displayName}_${patternType.displayName}_${colorType.displayName}',
        description:
            '${colorType.displayName}的${clothingType.displayName}，带有${patternType.displayName}图案',
        difficulty: difficulty ?? _random.nextInt(5) + 1,
        tags: requiredTags ?? [],
      );

      return Result.success(combination);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'MIRROR_ASSET_GENERATION_FAILED',
          message: '镜像对称素材组合生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 生成随机图形推理素材组合
  Future<Result<GraphicPatternAssetCombination>>
  generateRandomGraphicPatternAsset({
    int? difficulty,
    List<String>? requiredTags,
    List<ShapeType>? allowedShapeTypes,
    List<FillType>? allowedFillTypes,
    List<ColorType>? allowedColorTypes,
    List<SizeType>? allowedSizeTypes,
  }) async {
    try {
      final shapeTypes = allowedShapeTypes ?? ShapeType.values;
      final fillTypes = allowedFillTypes ?? FillType.values;
      final colorTypes = allowedColorTypes ?? ColorType.values;
      final sizeTypes = allowedSizeTypes ?? SizeType.values;

      final shapeType = shapeTypes[_random.nextInt(shapeTypes.length)];
      final fillType = fillTypes[_random.nextInt(fillTypes.length)];
      final colorType = colorTypes[_random.nextInt(colorTypes.length)];
      final sizeType = sizeTypes[_random.nextInt(sizeTypes.length)];

      final combination = GraphicPatternAssetCombination(
        shapeType: shapeType,
        fillType: fillType,
        colorType: colorType,
        sizeType: sizeType,
        name:
            '${sizeType.displayName}_${colorType.displayName}_${fillType.displayName}_${shapeType.displayName}',
        description:
            '${sizeType.displayName}的${colorType.displayName}${fillType.displayName}${shapeType.displayName}',
        difficulty: difficulty ?? _random.nextInt(5) + 1,
        tags: requiredTags ?? [],
      );

      return Result.success(combination);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'GRAPHIC_ASSET_GENERATION_FAILED',
          message: '图形推理素材组合生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 生成随机编程启蒙素材组合
  Future<Result<CodingAssetCombination>> generateRandomCodingAsset({
    int? difficulty,
    List<String>? requiredTags,
    List<CharacterType>? allowedCharacterTypes,
    List<CharacterState>? allowedCharacterStates,
    List<EnvironmentType>? allowedEnvironmentTypes,
  }) async {
    try {
      final characterTypes = allowedCharacterTypes ?? CharacterType.values;
      final characterStates = allowedCharacterStates ?? CharacterState.values;
      final environmentTypes =
          allowedEnvironmentTypes ?? EnvironmentType.values;

      final characterType =
          characterTypes[_random.nextInt(characterTypes.length)];
      final characterState =
          characterStates[_random.nextInt(characterStates.length)];
      final environmentType =
          environmentTypes[_random.nextInt(environmentTypes.length)];

      final combination = CodingAssetCombination(
        characterType: characterType,
        characterState: characterState,
        environmentType: environmentType,
        name:
            '${environmentType.displayName}_${characterState.displayName}_${characterType.displayName}',
        description:
            '在${environmentType.displayName}环境中${characterState.displayName}的${characterType.displayName}',
        difficulty: difficulty ?? _random.nextInt(5) + 1,
        tags: requiredTags ?? [],
      );

      return Result.success(combination);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'CODING_ASSET_GENERATION_FAILED',
          message: '编程启蒙素材组合生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // =============================================================================
  // 批量生成
  // =============================================================================

  /// 批量生成素材组合
  Future<Result<List<T>>> generateMultipleAssets<T extends AssetCombination>(
    Future<Result<T>> Function() generator,
    int count, {
    bool allowDuplicates = false,
    int maxAttempts = 100,
  }) async {
    try {
      final List<T> results = [];
      final Set<String> usedNames = {};

      int attempts = 0;
      while (results.length < count && attempts < maxAttempts) {
        final assetResult = await generator();

        if (assetResult.isSuccess) {
          final asset = assetResult.data!;

          if (allowDuplicates || !usedNames.contains(asset.name)) {
            results.add(asset);
            usedNames.add(asset.name);
          }
        }

        attempts++;
      }

      if (results.length < count) {
        if (_debugMode) {
          debugPrint(
            'AssetManager: Warning - Only generated ${results.length} assets out of $count requested',
          );
        }
      }

      return Result.success(results);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'BATCH_ASSET_GENERATION_FAILED',
          message: '批量素材组合生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // =============================================================================
  // 缓存管理
  // =============================================================================

  /// 清除素材缓存
  void clearCache() {
    _assetCache.clear();
    _combinationCache.clear();

    if (_debugMode) {
      debugPrint('AssetManager: Cache cleared');
    }
  }

  /// 获取缓存统计信息
  Map<String, int> getCacheStats() {
    return {
      'assetCacheSize': _assetCache.length,
      'combinationCacheSize': _combinationCache.length,
    };
  }

  // =============================================================================
  // 兼容性方法（向后兼容现有的GameAssets）
  // =============================================================================

  /// 获取镜像对称衣服素材（兼容性方法）
  String mirrorClothing(ClothingType type, String id) {
    return _getAssetPath('mirror_symmetry/clothes/${type.assetKey}_$id');
  }

  /// 获取图形推理图形素材（兼容性方法）
  String graphicPattern(String id) {
    return _getAssetPath('graphic_pattern/shapes/$id');
  }

  /// 获取空间想象形状素材（兼容性方法）
  String spatialShape(String id) {
    return _getAssetPath('spatial_visualization/3d_models/$id');
  }

  /// 获取数字逻辑水果素材（兼容性方法）
  String numericFruit(String fruit) {
    return _getAssetPath('numeric_logic/fruits/$fruit');
  }

  /// 获取编程启蒙角色素材（兼容性方法）
  String codingCharacter(String state) {
    return _getAssetPath('intro_coding/characters/robot_$state');
  }

  // =============================================================================
  // GameAssets类需要的兼容性方法
  // =============================================================================

  /// 获取镜像对称游戏素材路径
  String getMirrorSymmetryAssetPath(
    ClothingType clothingType,
    PatternType? patternType,
    ColorType? colorType,
    String? id,
  ) {
    if (patternType != null && colorType != null) {
      // 组合素材路径
      return _getAssetPath(
        'mirror_symmetry/combinations/${clothingType.assetKey}_${patternType.assetKey}_${colorType.assetKey}',
      );
    } else if (id != null) {
      // 单个衣服素材路径
      return _getAssetPath(
        'mirror_symmetry/clothes/${clothingType.assetKey}_$id',
      );
    } else {
      // 基础衣服素材路径
      return _getAssetPath('mirror_symmetry/clothes/${clothingType.assetKey}');
    }
  }

  /// 获取图形推理游戏素材路径
  String getGraphicPatternAssetPath(
    ShapeType shapeType,
    FillType fillType,
    ColorType colorType,
    SizeType sizeType,
        return Result.failure(
          DataAccessException(
            errorCode: ErrorCodes.dataAccessError,
            message: '编程环境素材加载失败: ${combination.name}',
            originalError: e,
            stackTrace: stackTrace,
          ),
  String getSpatialVisualizationAssetPath(
    Shape3DType shapeType,
    ViewType viewType,
    AssetMaterialType materialType,
    ColorType colorType,
  ) {
    return _getAssetPath(
      'spatial_visualization/combinations/${shapeType.assetKey}_${viewType.assetKey}_${materialType.assetKey}_${colorType.assetKey}',
    );
  }

  /// 获取编程启蒙游戏素材路径
  String getIntroCodingAssetPath(
    CharacterType characterType,
    CharacterState characterState,
    EnvironmentType environmentType,
  ) {
    return _getAssetPath(
      'intro_coding/combinations/${characterType.assetKey}_${characterState.assetKey}_${environmentType.assetKey}',
    );
  }

  // =============================================================================
  // 随机素材生成方法（PuzzleGenerator需要）
  // =============================================================================

  /// 生成随机空间想象素材组合
  Future<Result<SpatialVisualizationAssetCombination>>
  generateRandomSpatialVisualizationAsset({
    int difficulty = 1,
    List<String>? requiredTags,
    List<Shape3DType>? allowedShapeTypes,
    List<ViewType>? allowedViewTypes,
    List<AssetMaterialType>? allowedMaterialTypes,
    List<ColorType>? allowedColorTypes,
  }) async {
    try {
      final shapeTypes = allowedShapeTypes ?? Shape3DType.values;
      final viewTypes = allowedViewTypes ?? ViewType.values;
      final materialTypes = allowedMaterialTypes ?? AssetMaterialType.values;
      final colorTypes = allowedColorTypes ?? ColorType.values;

      final selectedShape = shapeTypes[_random.nextInt(shapeTypes.length)];
      final selectedView = viewTypes[_random.nextInt(viewTypes.length)];
      final selectedMaterial =
          materialTypes[_random.nextInt(materialTypes.length)];
      final selectedColor = colorTypes[_random.nextInt(colorTypes.length)];

      final combination = SpatialVisualizationAssetCombination(
        shapeType: selectedShape,
        viewType: selectedView,
        materialType: selectedMaterial,
        colorType: selectedColor,
        name: 'spatial_${selectedShape.assetKey}_${selectedView.assetKey}',
        description:
            '${selectedShape.displayName}的${selectedView.displayName}视角',
        difficulty: difficulty,
        tags: requiredTags ?? [],
      );

      return Result.success(combination);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'SPATIAL_ASSET_GENERATION_FAILED',
          message: '空间想象素材生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 生成随机编程启蒙素材组合
  Future<Result<CodingAssetCombination>> generateRandomIntroCodingAsset({
    int difficulty = 1,
    List<String>? requiredTags,
    List<CharacterType>? allowedCharacterTypes,
    List<CharacterState>? allowedCharacterStates,
    List<EnvironmentType>? allowedEnvironmentTypes,
  }) async {
    try {
      final characterTypes = allowedCharacterTypes ?? CharacterType.values;
      final characterStates = allowedCharacterStates ?? CharacterState.values;
      final environmentTypes =
          allowedEnvironmentTypes ?? EnvironmentType.values;

      final selectedCharacter =
          characterTypes[_random.nextInt(characterTypes.length)];
      final selectedState =
          characterStates[_random.nextInt(characterStates.length)];
      final selectedEnvironment =
          environmentTypes[_random.nextInt(environmentTypes.length)];

      final combination = CodingAssetCombination(
        characterType: selectedCharacter,
        characterState: selectedState,
        environmentType: selectedEnvironment,
        name: 'coding_${selectedCharacter.assetKey}_${selectedState.assetKey}',
        description:
            '${selectedCharacter.displayName}在${selectedEnvironment.displayName}中${selectedState.displayName}',
        difficulty: difficulty,
        tags: requiredTags ?? [],
      );

      return Result.success(combination);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'CODING_ASSET_GENERATION_FAILED',
          message: '编程启蒙素材生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 获取编程启蒙素材
  Future<Result<String>> getIntroCodingAsset(
    CodingAssetCombination combination,
  ) async {
    try {
      final characterAssetPath = _getAssetPath(
        combination.generateCharacterAssetPath(),
      );

      // 检查素材是否存在
      if (await _assetExists(characterAssetPath)) {
        return Result.success(characterAssetPath);
      }

      // 返回备用素材
      final fallbackPath = _getFallbackAssetPath('intro_coding', 'character');
      return Result.success(fallbackPath);
    } catch (e, stackTrace) {
      return Result.failure(
        DataAccessException(
          errorCode: 'CODING_ASSET_LOAD_FAILED',
          message: '编程启蒙素材加载失败: ${combination.name}',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}

import 'package:equatable/equatable.dart';

/// 通用失败基类
abstract class Failure extends Equatable {
  final String message;
  final String? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

/// 服务端失败
class ServerFailure extends Failure {
  const ServerFailure({required super.message, super.code});
}

/// 缓存失败
class CacheFailure extends Failure {
  const CacheFailure({required super.message, super.code});
}

/// 无效输入失败
class InvalidInputFailure extends Failure {
  const InvalidInputFailure({required super.message, super.code});
}

/// 权限失败
class PermissionFailure extends Failure {
  const PermissionFailure({required super.message, super.code});
}

/// LogicLab应用错误码定义
/// 提供标准化的错误代码和错误信息
library;

import '../exceptions/app_exceptions.dart';

/// 错误码类别定义
class ErrorCodes {
  // 私有构造函数，防止实例化
  ErrorCodes._();

  // ============ 用户相关错误 (1000-1999) ============
  
  /// 用户不存在
  static const String userNotFound = 'USER_NOT_FOUND_1001';
  
  /// 用户已存在
  static const String userAlreadyExists = 'USER_ALREADY_EXISTS_1002';
  
  /// 用户数量超限
  static const String userLimitExceeded = 'USER_LIMIT_EXCEEDED_1003';
  
  /// 昵称无效
  static const String invalidNickname = 'INVALID_NICKNAME_1004';
  
  /// 昵称已被使用
  static const String nicknameAlreadyTaken = 'NICKNAME_ALREADY_TAKEN_1005';
  
  /// 头像ID无效
  static const String invalidAvatarId = 'INVALID_AVATAR_ID_1006';
  
  /// 用户数据损坏
  static const String userDataCorrupted = 'USER_DATA_CORRUPTED_1007';

  // ============ 谜题相关错误 (2000-2999) ============
  
  /// 谜题不存在
  static const String puzzleNotFound = 'PUZZLE_NOT_FOUND_2001';
  
  /// 谜题类型不支持
  static const String unsupportedPuzzleType = 'UNSUPPORTED_PUZZLE_TYPE_2002';
  
  /// 谜题数据无效
  static const String invalidPuzzleData = 'INVALID_PUZZLE_DATA_2003';
  
  /// 谜题答案错误
  static const String incorrectAnswer = 'INCORRECT_ANSWER_2004';
  
  /// 谜题已锁定
  static const String puzzleLocked = 'PUZZLE_LOCKED_2005';
  
  /// 前置谜题未完成
  static const String prerequisiteNotMet = 'PREREQUISITE_NOT_MET_2006';
  
  /// 技能等级不足
  static const String insufficientSkillLevel = 'INSUFFICIENT_SKILL_LEVEL_2007';
  
  /// 谜题加载失败
  static const String puzzleLoadFailed = 'PUZZLE_LOAD_FAILED_2008';

  // ============ 游戏进度相关错误 (3000-3999) ============
  
  /// 进度保存失败
  static const String progressSaveFailed = 'PROGRESS_SAVE_FAILED_3001';
  
  /// 进度数据无效
  static const String invalidProgressData = 'INVALID_PROGRESS_DATA_3002';
  
  /// 游戏会话无效
  static const String invalidGameSession = 'INVALID_GAME_SESSION_3003';
  
  /// 游戏时间无效
  static const String invalidGameTime = 'INVALID_GAME_TIME_3004';
  
  /// 星级评分无效
  static const String invalidStarRating = 'INVALID_STAR_RATING_3005';

  // ============ 提示系统相关错误 (4000-4999) ============
  
  /// 提示不可用
  static const String hintUnavailable = 'HINT_UNAVAILABLE_4001';
  
  /// 提示次数超限
  static const String hintLimitExceeded = 'HINT_LIMIT_EXCEEDED_4002';
  
  /// 提示冷却中
  static const String hintOnCooldown = 'HINT_ON_COOLDOWN_4003';
  
  /// 提示生成失败
  static const String hintGenerationFailed = 'HINT_GENERATION_FAILED_4004';
  
  /// 提示类型不支持
  static const String unsupportedHintType = 'UNSUPPORTED_HINT_TYPE_4005';

  // ============ 成就系统相关错误 (5000-5999) ============
  
  /// 成就不存在
  static const String achievementNotFound = 'ACHIEVEMENT_NOT_FOUND_5001';
  
  /// 成就已解锁
  static const String achievementAlreadyUnlocked = 'ACHIEVEMENT_ALREADY_UNLOCKED_5002';
  
  /// 成就条件不满足
  static const String achievementConditionNotMet = 'ACHIEVEMENT_CONDITION_NOT_MET_5003';
  
  /// 成就数据无效
  static const String invalidAchievementData = 'INVALID_ACHIEVEMENT_DATA_5004';
  
  /// 成就解锁失败
  static const String achievementUnlockFailed = 'ACHIEVEMENT_UNLOCK_FAILED_5005';

  // ============ 数据存储相关错误 (6000-6999) ============
  
  /// 数据库连接失败
  static const String databaseConnectionFailed = 'DATABASE_CONNECTION_FAILED_6001';
  
  /// 数据序列化失败
  static const String dataSerializationFailed = 'DATA_SERIALIZATION_FAILED_6002';
  
  /// 数据反序列化失败
  static const String dataDeserializationFailed = 'DATA_DESERIALIZATION_FAILED_6003';
  
  /// 数据完整性检查失败
  static const String dataIntegrityCheckFailed = 'DATA_INTEGRITY_CHECK_FAILED_6004';
  
  /// 存储空间不足
  static const String insufficientStorage = 'INSUFFICIENT_STORAGE_6005';
  
  /// 数据备份失败
  static const String dataBackupFailed = 'DATA_BACKUP_FAILED_6006';
  
  /// 数据恢复失败
  static const String dataRestoreFailed = 'DATA_RESTORE_FAILED_6007';

  // ============ 系统级错误 (7000-7999) ============
  
  /// 网络连接失败
  static const String networkConnectionFailed = 'NETWORK_CONNECTION_FAILED_7001';
  
  /// 权限不足
  static const String insufficientPermissions = 'INSUFFICIENT_PERMISSIONS_7002';
  
  /// 系统资源不足
  static const String insufficientSystemResources = 'INSUFFICIENT_SYSTEM_RESOURCES_7003';
  
  /// 操作超时
  static const String operationTimeout = 'OPERATION_TIMEOUT_7004';
  
  /// 并发冲突
  static const String concurrencyConflict = 'CONCURRENCY_CONFLICT_7005';
  
  /// 配置错误
  static const String configurationError = 'CONFIGURATION_ERROR_7006';
  
  /// 版本不兼容
  static const String versionIncompatible = 'VERSION_INCOMPATIBLE_7007';

  // ============ 验证相关错误 (8000-8999) ============
  
  /// 参数为空
  static const String parameterNull = 'PARAMETER_NULL_8001';
  
  /// 参数格式错误
  static const String invalidParameterFormat = 'INVALID_PARAMETER_FORMAT_8002';
  
  /// 参数值超出范围
  static const String parameterOutOfRange = 'PARAMETER_OUT_OF_RANGE_8003';
  
  /// 必需参数缺失
  static const String requiredParameterMissing = 'REQUIRED_PARAMETER_MISSING_8004';
  
  /// 参数类型错误
  static const String invalidParameterType = 'INVALID_PARAMETER_TYPE_8005';
  
  /// 参数组合无效
  static const String invalidParameterCombination = 'INVALID_PARAMETER_COMBINATION_8006';

  // ============ 业务逻辑错误 (9000-9999) ============
  
  /// 操作不被允许
  static const String operationNotAllowed = 'OPERATION_NOT_ALLOWED_9001';
  
  /// 状态冲突
  static const String stateConflict = 'STATE_CONFLICT_9002';
  
  /// 业务规则违反
  static const String businessRuleViolation = 'BUSINESS_RULE_VIOLATION_9003';
  
  /// 资源冲突
  static const String resourceConflict = 'RESOURCE_CONFLICT_9004';
  
  /// 依赖项缺失
  static const String dependencyMissing = 'DEPENDENCY_MISSING_9005';
  
  /// 循环依赖
  static const String circularDependency = 'CIRCULAR_DEPENDENCY_9006';
  
  /// 未知错误
  static const String unknown = 'UNKNOWN_ERROR_9999';

  // ============ 临时兼容性错误码 ============
  
  /// 数据保存错误（临时）
  static const String dataSaveError = 'DATA_SAVE_ERROR_6008';
  
  /// 数据加载错误（临时）
  static const String dataLoadError = 'DATA_LOAD_ERROR_6009';
  
  /// 网络连接错误（临时）
  static const String networkConnectionError = 'NETWORK_CONNECTION_ERROR_7008';
  
  /// 无效参数（临时）
  static const String invalidParameter = 'INVALID_PARAMETER_8007';
}

/// 错误消息映射
class ErrorMessages {
  // 私有构造函数，防止实例化
  ErrorMessages._();

  /// 错误码到中文消息的映射
  static const Map<String, String> _chineseMessages = {
    // 用户相关错误
    ErrorCodes.userNotFound: '用户不存在',
    ErrorCodes.userAlreadyExists: '用户已存在',
    ErrorCodes.userLimitExceeded: '用户数量已达上限(最多4个用户)',
    ErrorCodes.invalidNickname: '昵称格式不正确',
    ErrorCodes.nicknameAlreadyTaken: '昵称已被使用',
    ErrorCodes.invalidAvatarId: '头像ID无效',
    ErrorCodes.userDataCorrupted: '用户数据已损坏',

    // 谜题相关错误
    ErrorCodes.puzzleNotFound: '谜题不存在',
    ErrorCodes.unsupportedPuzzleType: '不支持的谜题类型',
    ErrorCodes.invalidPuzzleData: '谜题数据无效',
    ErrorCodes.incorrectAnswer: '答案不正确，再试试看！',
    ErrorCodes.puzzleLocked: '谜题已锁定',
    ErrorCodes.prerequisiteNotMet: '需要先完成前置关卡',
    ErrorCodes.insufficientSkillLevel: '技能等级不足',
    ErrorCodes.puzzleLoadFailed: '谜题加载失败',

    // 游戏进度相关错误
    ErrorCodes.progressSaveFailed: '进度保存失败',
    ErrorCodes.invalidProgressData: '进度数据无效',
    ErrorCodes.invalidGameSession: '游戏会话无效',
    ErrorCodes.invalidGameTime: '游戏时间无效',
    ErrorCodes.invalidStarRating: '星级评分无效',

    // 提示系统相关错误
    ErrorCodes.hintUnavailable: '提示暂不可用',
    ErrorCodes.hintLimitExceeded: '提示次数已用完',
    ErrorCodes.hintOnCooldown: '提示冷却中，请稍后再试',
    ErrorCodes.hintGenerationFailed: '提示生成失败',
    ErrorCodes.unsupportedHintType: '不支持的提示类型',

    // 成就系统相关错误
    ErrorCodes.achievementNotFound: '成就不存在',
    ErrorCodes.achievementAlreadyUnlocked: '成就已解锁',
    ErrorCodes.achievementConditionNotMet: '成就条件不满足',
    ErrorCodes.invalidAchievementData: '成就数据无效',
    ErrorCodes.achievementUnlockFailed: '成就解锁失败',

    // 数据存储相关错误
    ErrorCodes.databaseConnectionFailed: '数据库连接失败',
    ErrorCodes.dataSerializationFailed: '数据序列化失败',
    ErrorCodes.dataDeserializationFailed: '数据反序列化失败',
    ErrorCodes.dataIntegrityCheckFailed: '数据完整性检查失败',
    ErrorCodes.insufficientStorage: '存储空间不足',
    ErrorCodes.dataBackupFailed: '数据备份失败',
    ErrorCodes.dataRestoreFailed: '数据恢复失败',

    // 系统级错误
    ErrorCodes.networkConnectionFailed: '网络连接失败',
    ErrorCodes.insufficientPermissions: '权限不足',
    ErrorCodes.insufficientSystemResources: '系统资源不足',
    ErrorCodes.operationTimeout: '操作超时',
    ErrorCodes.concurrencyConflict: '并发冲突',
    ErrorCodes.configurationError: '配置错误',
    ErrorCodes.versionIncompatible: '版本不兼容',

    // 验证相关错误
    ErrorCodes.parameterNull: '参数不能为空',
    ErrorCodes.invalidParameterFormat: '参数格式错误',
    ErrorCodes.parameterOutOfRange: '参数值超出范围',
    ErrorCodes.requiredParameterMissing: '缺少必需参数',
    ErrorCodes.invalidParameterType: '参数类型错误',
    ErrorCodes.invalidParameterCombination: '参数组合无效',

    // 业务逻辑错误
    ErrorCodes.operationNotAllowed: '操作不被允许',
    ErrorCodes.stateConflict: '状态冲突',
    ErrorCodes.businessRuleViolation: '违反业务规则',
    ErrorCodes.resourceConflict: '资源冲突',
    ErrorCodes.dependencyMissing: '依赖项缺失',
    ErrorCodes.circularDependency: '循环依赖',
    ErrorCodes.unknown: '未知错误',

    // 临时兼容性错误
    ErrorCodes.dataSaveError: '数据保存失败',
    ErrorCodes.dataLoadError: '数据加载失败',
    ErrorCodes.networkConnectionError: '网络连接失败',
    ErrorCodes.invalidParameter: '参数无效',
  };

  /// 错误码到英文消息的映射
  static const Map<String, String> _englishMessages = {
    // 用户相关错误
    ErrorCodes.userNotFound: 'User not found',
    ErrorCodes.userAlreadyExists: 'User already exists',
    ErrorCodes.userLimitExceeded: 'User limit exceeded (maximum 4 users)',
    ErrorCodes.invalidNickname: 'Invalid nickname format',
    ErrorCodes.nicknameAlreadyTaken: 'Nickname already taken',
    ErrorCodes.invalidAvatarId: 'Invalid avatar ID',
    ErrorCodes.userDataCorrupted: 'User data corrupted',

    // 谜题相关错误
    ErrorCodes.puzzleNotFound: 'Puzzle not found',
    ErrorCodes.unsupportedPuzzleType: 'Unsupported puzzle type',
    ErrorCodes.invalidPuzzleData: 'Invalid puzzle data',
    ErrorCodes.incorrectAnswer: 'Incorrect answer, try again!',
    ErrorCodes.puzzleLocked: 'Puzzle locked',
    ErrorCodes.prerequisiteNotMet: 'Prerequisite not met',
    ErrorCodes.insufficientSkillLevel: 'Insufficient skill level',
    ErrorCodes.puzzleLoadFailed: 'Puzzle load failed',

    // 游戏进度相关错误
    ErrorCodes.progressSaveFailed: 'Progress save failed',
    ErrorCodes.invalidProgressData: 'Invalid progress data',
    ErrorCodes.invalidGameSession: 'Invalid game session',
    ErrorCodes.invalidGameTime: 'Invalid game time',
    ErrorCodes.invalidStarRating: 'Invalid star rating',

    // 提示系统相关错误
    ErrorCodes.hintUnavailable: 'Hint unavailable',
    ErrorCodes.hintLimitExceeded: 'Hint limit exceeded',
    ErrorCodes.hintOnCooldown: 'Hint on cooldown',
    ErrorCodes.hintGenerationFailed: 'Hint generation failed',
    ErrorCodes.unsupportedHintType: 'Unsupported hint type',

    // 成就系统相关错误
    ErrorCodes.achievementNotFound: 'Achievement not found',
    ErrorCodes.achievementAlreadyUnlocked: 'Achievement already unlocked',
    ErrorCodes.achievementConditionNotMet: 'Achievement condition not met',
    ErrorCodes.invalidAchievementData: 'Invalid achievement data',
    ErrorCodes.achievementUnlockFailed: 'Achievement unlock failed',

    // 数据存储相关错误
    ErrorCodes.databaseConnectionFailed: 'Database connection failed',
    ErrorCodes.dataSerializationFailed: 'Data serialization failed',
    ErrorCodes.dataDeserializationFailed: 'Data deserialization failed',
    ErrorCodes.dataIntegrityCheckFailed: 'Data integrity check failed',
    ErrorCodes.insufficientStorage: 'Insufficient storage',
    ErrorCodes.dataBackupFailed: 'Data backup failed',
    ErrorCodes.dataRestoreFailed: 'Data restore failed',

    // 系统级错误
    ErrorCodes.networkConnectionFailed: 'Network connection failed',
    ErrorCodes.insufficientPermissions: 'Insufficient permissions',
    ErrorCodes.insufficientSystemResources: 'Insufficient system resources',
    ErrorCodes.operationTimeout: 'Operation timeout',
    ErrorCodes.concurrencyConflict: 'Concurrency conflict',
    ErrorCodes.configurationError: 'Configuration error',
    ErrorCodes.versionIncompatible: 'Version incompatible',

    // 验证相关错误
    ErrorCodes.parameterNull: 'Parameter cannot be null',
    ErrorCodes.invalidParameterFormat: 'Invalid parameter format',
    ErrorCodes.parameterOutOfRange: 'Parameter out of range',
    ErrorCodes.requiredParameterMissing: 'Required parameter missing',
    ErrorCodes.invalidParameterType: 'Invalid parameter type',
    ErrorCodes.invalidParameterCombination: 'Invalid parameter combination',

    // 业务逻辑错误
    ErrorCodes.operationNotAllowed: 'Operation not allowed',
    ErrorCodes.stateConflict: 'State conflict',
    ErrorCodes.businessRuleViolation: 'Business rule violation',
    ErrorCodes.resourceConflict: 'Resource conflict',
    ErrorCodes.dependencyMissing: 'Dependency missing',
    ErrorCodes.circularDependency: 'Circular dependency',
    ErrorCodes.unknown: 'Unknown error',

    // 临时兼容性错误
    ErrorCodes.dataSaveError: 'Data save failed',
    ErrorCodes.dataLoadError: 'Data load failed',
    ErrorCodes.networkConnectionError: 'Network connection failed',
    ErrorCodes.invalidParameter: 'Invalid parameter',
  };

  /// 根据错误码和语言获取错误消息
  static String getMessage(String errorCode, {String language = 'zh'}) {
    final messages = language == 'zh' ? _chineseMessages : _englishMessages;
    return messages[errorCode] ?? messages[ErrorCodes.unknown] ?? 'Unknown error';
  }

  /// 获取中文错误消息
  static String getChineseMessage(String errorCode) {
    return getMessage(errorCode, language: 'zh');
  }

  /// 获取英文错误消息
  static String getEnglishMessage(String errorCode) {
    return getMessage(errorCode, language: 'en');
  }
}



/// 错误元数据
class ErrorMetadata {
  /// 错误严重程度
  final ErrorSeverity severity;
  
  /// 错误分类
  final ErrorCategory category;
  
  /// 是否可重试
  final bool retryable;
  
  /// 是否应该上报
  final bool reportable;
  
  /// 用户友好的错误提示
  final bool userFriendly;

  const ErrorMetadata({
    required this.severity,
    required this.category,
    this.retryable = false,
    this.reportable = true,
    this.userFriendly = true,
  });
}

/// 错误码元数据映射
class ErrorMetadataRegistry {
  // 私有构造函数，防止实例化
  ErrorMetadataRegistry._();

  /// 错误码到元数据的映射
  static const Map<String, ErrorMetadata> _metadata = {
    // 用户相关错误
    ErrorCodes.userNotFound: ErrorMetadata(
      severity: ErrorSeverity.medium,
      category: ErrorCategory.user,
      retryable: false,
      userFriendly: true,
    ),
    ErrorCodes.userAlreadyExists: ErrorMetadata(
      severity: ErrorSeverity.medium,
      category: ErrorCategory.business,
      retryable: false,
      userFriendly: true,
    ),
    ErrorCodes.userLimitExceeded: ErrorMetadata(
      severity: ErrorSeverity.medium,
      category: ErrorCategory.business,
      retryable: false,
      userFriendly: true,
    ),
    ErrorCodes.invalidNickname: ErrorMetadata(
      severity: ErrorSeverity.medium,
      category: ErrorCategory.user,
      retryable: false,
      userFriendly: true,
    ),

    // 谜题相关错误
    ErrorCodes.puzzleNotFound: ErrorMetadata(
      severity: ErrorSeverity.high,
      category: ErrorCategory.data,
      retryable: true,
      userFriendly: true,
    ),
    ErrorCodes.puzzleLoadFailed: ErrorMetadata(
      severity: ErrorSeverity.high,
      category: ErrorCategory.data,
      retryable: true,
      userFriendly: true,
    ),

    // 系统级错误
    ErrorCodes.databaseConnectionFailed: ErrorMetadata(
      severity: ErrorSeverity.critical,
      category: ErrorCategory.system,
      retryable: true,
      reportable: true,
      userFriendly: false,
    ),
    ErrorCodes.networkConnectionFailed: ErrorMetadata(
      severity: ErrorSeverity.high,
      category: ErrorCategory.system,
      retryable: true,
      userFriendly: true,
    ),

    // 默认元数据
    ErrorCodes.unknown: ErrorMetadata(
      severity: ErrorSeverity.high,
      category: ErrorCategory.system,
      retryable: false,
      reportable: true,
      userFriendly: false,
    ),
  };

  /// 获取错误元数据
  static ErrorMetadata getMetadata(String errorCode) {
    return _metadata[errorCode] ?? _metadata[ErrorCodes.unknown]!;
  }

  /// 检查错误是否可重试
  static bool isRetryable(String errorCode) {
    return getMetadata(errorCode).retryable;
  }

  /// 检查错误是否应该上报
  static bool isReportable(String errorCode) {
    return getMetadata(errorCode).reportable;
  }

  /// 检查错误是否用户友好
  static bool isUserFriendly(String errorCode) {
    return getMetadata(errorCode).userFriendly;
  }

  /// 获取错误严重程度
  static ErrorSeverity getSeverity(String errorCode) {
    return getMetadata(errorCode).severity;
  }

  /// 获取错误分类
  static ErrorCategory getCategory(String errorCode) {
    return getMetadata(errorCode).category;
  }
} 
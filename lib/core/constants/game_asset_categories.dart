import 'dart:math';

/// 素材分类系统
///
/// 支持各种游戏类型的素材分类管理和随机组合生成

// =============================================================================
// 镜像对称游戏素材分类
// =============================================================================

/// 衣服类型分类
enum ClothingType {
  shirt('衬衫', 'shirt'),
  dress('裙子', 'dress'),
  tshirt('T恤', 'tshirt'),
  pants('裤子', 'pants'),
  skirt('短裙', 'skirt'),
  jacket('夹克', 'jacket'),
  sweater('毛衣', 'sweater'),
  hoodie('连帽衫', 'hoodie');

  const ClothingType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 花纹类型分类
enum PatternType {
  heart('心形', 'heart'),
  star('星形', 'star'),
  circle('圆形', 'circle'),
  square('方形', 'square'),
  triangle('三角形', 'triangle'),
  diamond('菱形', 'diamond'),
  bowTie('蝴蝶结', 'bow_tie'),
  chain('链条纹', 'chain'),
  stripe('条纹', 'stripe'),
  dot('圆点', 'dot'),
  flower('花朵', 'flower'),
  leaf('叶子', 'leaf');

  const PatternType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 颜色类型分类
enum ColorType {
  red('红色', 'red'),
  blue('蓝色', 'blue'),
  green('绿色', 'green'),
  yellow('黄色', 'yellow'),
  purple('紫色', 'purple'),
  orange('橙色', 'orange'),
  pink('粉色', 'pink'),
  cyan('青色', 'cyan'),
  black('黑色', 'black'),
  white('白色', 'white'),
  gray('灰色', 'gray'),
  brown('棕色', 'brown');

  const ColorType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

// =============================================================================
// 图形推理游戏素材分类
// =============================================================================

/// 基础图形类型
enum ShapeType {
  circle('圆形', 'circle'),
  square('正方形', 'square'),
  triangle('三角形', 'triangle'),
  diamond('菱形', 'diamond'),
  pentagon('五边形', 'pentagon'),
  hexagon('六边形', 'hexagon'),
  star('星形', 'star'),
  heart('心形', 'heart'),
  cross('十字', 'cross'),
  arrow('箭头', 'arrow');

  const ShapeType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 图形填充类型
enum FillType {
  solid('实心', 'solid'),
  outline('空心', 'outline'),
  striped('条纹', 'striped'),
  dotted('点状', 'dotted'),
  gradient('渐变', 'gradient');

  const FillType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 图形大小类型
enum SizeType {
  small('小', 'small'),
  medium('中', 'medium'),
  large('大', 'large');

  const SizeType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

// =============================================================================
// 空间想象游戏素材分类
// =============================================================================

/// 3D形状类型
enum Shape3DType {
  cube('立方体', 'cube'),
  pyramid('金字塔', 'pyramid'),
  cylinder('圆柱体', 'cylinder'),
  sphere('球体', 'sphere'),
  cone('圆锥体', 'cone'),
  prism('棱柱', 'prism'),
  torus('环形', 'torus'),
  dodecahedron('十二面体', 'dodecahedron');

  const Shape3DType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 视角类型
enum ViewType {
  front('正面', 'front'),
  back('背面', 'back'),
  left('左侧', 'left'),
  right('右侧', 'right'),
  top('顶部', 'top'),
  bottom('底部', 'bottom'),
  isometric('等轴', 'isometric');

  const ViewType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 材质类型
enum AssetMaterialType {
  metal('金属', 'metal'),
  wood('木质', 'wood'),
  plastic('塑料', 'plastic'),
  glass('玻璃', 'glass'),
  stone('石质', 'stone'),
  fabric('布料', 'fabric');

  const AssetMaterialType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

// =============================================================================
// 数字逻辑游戏素材分类
// =============================================================================

/// 数字符号类型
enum NumberSymbolType {
  digit('数字', 'digit'),
  fruit('水果', 'fruit'),
  animal('动物', 'animal'),
  shape('图形', 'shape'),
  color('颜色', 'color'),
  letter('字母', 'letter');

  const NumberSymbolType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 水果类型
enum FruitType {
  apple('苹果', 'apple'),
  banana('香蕉', 'banana'),
  orange('橙子', 'orange'),
  grape('葡萄', 'grape'),
  strawberry('草莓', 'strawberry'),
  watermelon('西瓜', 'watermelon'),
  pineapple('菠萝', 'pineapple'),
  peach('桃子', 'peach'),
  cherry('樱桃', 'cherry'),
  lemon('柠檬', 'lemon');

  const FruitType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 动物类型
enum AnimalType {
  cat('猫', 'cat'),
  dog('狗', 'dog'),
  rabbit('兔子', 'rabbit'),
  bird('鸟', 'bird'),
  fish('鱼', 'fish'),
  elephant('大象', 'elephant'),
  lion('狮子', 'lion'),
  tiger('老虎', 'tiger'),
  bear('熊', 'bear'),
  panda('熊猫', 'panda');

  const AnimalType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

// =============================================================================
// 编程启蒙游戏素材分类
// =============================================================================

/// 角色类型
enum CharacterType {
  robot('机器人', 'robot'),
  astronaut('宇航员', 'astronaut'),
  knight('骑士', 'knight'),
  princess('公主', 'princess'),
  wizard('巫师', 'wizard'),
  pirate('海盗', 'pirate'),
  ninja('忍者', 'ninja'),
  superhero('超级英雄', 'superhero');

  const CharacterType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 角色状态
enum CharacterState {
  idle('待机', 'idle'),
  walking('行走', 'walking'),
  running('奔跑', 'running'),
  jumping('跳跃', 'jumping'),
  celebrating('庆祝', 'celebrating'),
  thinking('思考', 'thinking'),
  confused('困惑', 'confused'),
  sleeping('睡觉', 'sleeping');

  const CharacterState(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 指令类型
enum CommandType {
  moveForward('前进', 'move_forward'),
  moveBackward('后退', 'move_backward'),
  turnLeft('左转', 'turn_left'),
  turnRight('右转', 'turn_right'),
  jump('跳跃', 'jump'),
  collect('收集', 'collect'),
  use('使用', 'use'),
  wait('等待', 'wait'),
  repeat('重复', 'repeat'),
  ifCondition('如果', 'if_condition');

  const CommandType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

/// 环境类型
enum EnvironmentType {
  forest('森林', 'forest'),
  desert('沙漠', 'desert'),
  ocean('海洋', 'ocean'),
  mountain('山脉', 'mountain'),
  city('城市', 'city'),
  space('太空', 'space'),
  castle('城堡', 'castle'),
  laboratory('实验室', 'laboratory');

  const EnvironmentType(this.displayName, this.assetKey);
  final String displayName;
  final String assetKey;
}

// =============================================================================
// 素材组合配置
// =============================================================================

/// 素材组合配置
class AssetCombination {
  /// 组合名称
  final String name;

  /// 组合描述
  final String description;

  /// 组合权重（用于随机选择）
  final int weight;

  /// 组合标签（用于筛选）
  final List<String> tags;

  /// 难度等级
  final int difficulty;

  const AssetCombination({
    required this.name,
    required this.description,
    this.weight = 1,
    this.tags = const [],
    this.difficulty = 1,
  });
}

/// 镜像对称游戏素材组合
class MirrorSymmetryAssetCombination extends AssetCombination {
  final ClothingType clothingType;
  final PatternType patternType;
  final ColorType colorType;

  const MirrorSymmetryAssetCombination({
    required this.clothingType,
    required this.patternType,
    required this.colorType,
    required super.name,
    required super.description,
    super.weight = 1,
    super.tags = const [],
    super.difficulty = 1,
  });

  /// 生成素材路径
  String generateAssetPath() {
    return 'mirror_symmetry/combinations/${clothingType.assetKey}_${patternType.assetKey}_${colorType.assetKey}';
  }
}

/// 图形推理游戏素材组合
class GraphicPatternAssetCombination extends AssetCombination {
  final ShapeType shapeType;
  final FillType fillType;
  final ColorType colorType;
  final SizeType sizeType;

  const GraphicPatternAssetCombination({
    required this.shapeType,
    required this.fillType,
    required this.colorType,
    required this.sizeType,
    required super.name,
    required super.description,
    super.weight = 1,
    super.tags = const [],
    super.difficulty = 1,
  });

  /// 生成素材路径
  String generateAssetPath() {
    return 'graphic_pattern/combinations/${shapeType.assetKey}_${fillType.assetKey}_${colorType.assetKey}_${sizeType.assetKey}';
  }
}

/// 空间想象游戏素材组合
class SpatialVisualizationAssetCombination extends AssetCombination {
  final Shape3DType shapeType;
  final ViewType viewType;
  final AssetMaterialType materialType;
  final ColorType colorType;

  const SpatialVisualizationAssetCombination({
    required this.shapeType,
    required this.viewType,
    required this.materialType,
    required this.colorType,
    required super.name,
    required super.description,
    super.weight = 1,
    super.tags = const [],
    super.difficulty = 1,
  });

  /// 生成素材路径
  String generateAssetPath() {
    return 'spatial_visualization/combinations/${shapeType.assetKey}_${viewType.assetKey}_${materialType.assetKey}_${colorType.assetKey}';
  }
}

/// 编程启蒙游戏素材组合
class CodingAssetCombination extends AssetCombination {
  final CharacterType characterType;
  final CharacterState characterState;
  final EnvironmentType environmentType;

  const CodingAssetCombination({
    required this.characterType,
    required this.characterState,
    required this.environmentType,
    required super.name,
    required super.description,
    super.weight = 1,
    super.tags = const [],
    super.difficulty = 1,
  });

  /// 生成角色素材路径
  String generateCharacterAssetPath() {
    return 'intro_coding/combinations/${characterType.assetKey}_${characterState.assetKey}';
  }

  /// 生成环境素材路径
  String generateEnvironmentAssetPath() {
    return 'intro_coding/environments/${environmentType.assetKey}';
  }
}

// =============================================================================
// 随机生成器
// =============================================================================

/// 素材组合随机生成器
class AssetCombinationGenerator {
  static final Random _random = Random();

  /// 生成随机镜像对称素材组合
  static MirrorSymmetryAssetCombination generateMirrorSymmetryAsset({
    int? difficulty,
    List<String>? requiredTags,
  }) {
    final clothingType =
        ClothingType.values[_random.nextInt(ClothingType.values.length)];
    final patternType =
        PatternType.values[_random.nextInt(PatternType.values.length)];
    final colorType =
        ColorType.values[_random.nextInt(ColorType.values.length)];

    return MirrorSymmetryAssetCombination(
      clothingType: clothingType,
      patternType: patternType,
      colorType: colorType,
      name:
          '${clothingType.displayName}${patternType.displayName}${colorType.displayName}',
      description:
          '${colorType.displayName}的${clothingType.displayName}，带有${patternType.displayName}图案',
      difficulty: difficulty ?? _random.nextInt(5) + 1,
      tags: requiredTags ?? [],
    );
  }

  /// 生成随机图形推理素材组合
  static GraphicPatternAssetCombination generateGraphicPatternAsset({
    int? difficulty,
    List<String>? requiredTags,
  }) {
    final shapeType =
        ShapeType.values[_random.nextInt(ShapeType.values.length)];
    final fillType = FillType.values[_random.nextInt(FillType.values.length)];
    final colorType =
        ColorType.values[_random.nextInt(ColorType.values.length)];
    final sizeType = SizeType.values[_random.nextInt(SizeType.values.length)];

    return GraphicPatternAssetCombination(
      shapeType: shapeType,
      fillType: fillType,
      colorType: colorType,
      sizeType: sizeType,
      name:
          '${sizeType.displayName}${colorType.displayName}${fillType.displayName}${shapeType.displayName}',
      description:
          '${sizeType.displayName}的${colorType.displayName}${fillType.displayName}${shapeType.displayName}',
      difficulty: difficulty ?? _random.nextInt(5) + 1,
      tags: requiredTags ?? [],
    );
  }

  /// 生成随机空间想象素材组合
  static SpatialVisualizationAssetCombination
  generateSpatialVisualizationAsset({
    int? difficulty,
    List<String>? requiredTags,
  }) {
    final shapeType =
        Shape3DType.values[_random.nextInt(Shape3DType.values.length)];
    final viewType = ViewType.values[_random.nextInt(ViewType.values.length)];
    final materialType = AssetMaterialType
        .values[_random.nextInt(AssetMaterialType.values.length)];
    final colorType =
        ColorType.values[_random.nextInt(ColorType.values.length)];

    return SpatialVisualizationAssetCombination(
      shapeType: shapeType,
      viewType: viewType,
      materialType: materialType,
      colorType: colorType,
      name:
          '${colorType.displayName}${materialType.displayName}${shapeType.displayName}${viewType.displayName}视图',
      description:
          '${viewType.displayName}视图的${colorType.displayName}${materialType.displayName}${shapeType.displayName}',
      difficulty: difficulty ?? _random.nextInt(5) + 1,
      tags: requiredTags ?? [],
    );
  }

  /// 生成随机编程启蒙素材组合
  static CodingAssetCombination generateCodingAsset({
    int? difficulty,
    List<String>? requiredTags,
  }) {
    final characterType =
        CharacterType.values[_random.nextInt(CharacterType.values.length)];
    final characterState =
        CharacterState.values[_random.nextInt(CharacterState.values.length)];
    final environmentType =
        EnvironmentType.values[_random.nextInt(EnvironmentType.values.length)];

    return CodingAssetCombination(
      characterType: characterType,
      characterState: characterState,
      environmentType: environmentType,
      name:
          '${environmentType.displayName}中的${characterState.displayName}${characterType.displayName}',
      description:
          '在${environmentType.displayName}环境中${characterState.displayName}的${characterType.displayName}',
      difficulty: difficulty ?? _random.nextInt(5) + 1,
      tags: requiredTags ?? [],
    );
  }

  /// 生成指定数量的素材组合
  static List<T> generateMultiple<T extends AssetCombination>(
    T Function() generator,
    int count, {
    bool allowDuplicates = false,
  }) {
    final List<T> results = [];
    final Set<String> usedNames = {};

    int attempts = 0;
    while (results.length < count && attempts < count * 10) {
      final asset = generator();

      if (allowDuplicates || !usedNames.contains(asset.name)) {
        results.add(asset);
        usedNames.add(asset.name);
      }

      attempts++;
    }

    return results;
  }
}

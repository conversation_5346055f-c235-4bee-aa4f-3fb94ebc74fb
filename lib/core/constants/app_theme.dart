import 'package:flutter/material.dart';
import '../theme/ux_theme_config.dart';

/// LogicLab应用主题配置
///
/// 基于UXThemeConfig构建的游戏特定主题
class AppTheme {
  AppTheme._();

  // ===== 游戏特定色彩 =====

  /// 游戏功能色彩
  static const Color starColor = Color(0xFFFFD700); // 金色星星
  static const Color hintColor = UXThemeConfig.accentBlue; // 蓝色提示
  static const Color correctColor = UXThemeConfig.successGreen; // 正确绿色
  static const Color incorrectColor = UXThemeConfig.errorRed; // 错误红色

  /// 主题世界色彩
  static const Color forestPrimary = UXThemeConfig.accentTeal;
  static const Color forestSecondary = Color(0xFF8BC34A);
  static const Color oceanPrimary = UXThemeConfig.primaryBlue;
  static const Color oceanSecondary = Color(0xFF03DAC6);
  static const Color spacePrimary = UXThemeConfig.accentBlue;
  static const Color spaceSecondary = Color(0xFF9C27B0);

  // ===== 游戏特定尺寸 =====

  /// 谜题相关尺寸
  static const double puzzleCardSize = 120.0;
  static const double puzzleIconSize = 32.0;
  static const double puzzleGridSpacing = UXThemeConfig.paddingM;

  /// 游戏界面尺寸
  static const double gameHeaderHeight = 80.0;
  static const double gameFooterHeight = 100.0;
  static const double progressBarHeight = 8.0;
  static const double starSize = 32.0;
  static const double achievementBadgeSize = 48.0;

  // ===== 主题构建器 =====

  /// 获取游戏主题数据（亮色）
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: UXThemeConfig.lightColorScheme,

      // 自定义应用栏主题
      appBarTheme: AppBarTheme(
        backgroundColor: UXThemeConfig.primaryBlue,
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(
          fontSize: UXThemeConfig.fontSizeL,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // 自定义按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: UXThemeConfig.primaryBlue,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(
            horizontal: UXThemeConfig.paddingL,
            vertical: UXThemeConfig.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          ),
          textStyle: TextStyle(
            fontSize: UXThemeConfig.fontSizeBody,
            fontWeight: FontWeight.bold,
          ),
          minimumSize: const Size(120, 48),
          elevation: 4,
          shadowColor: Colors.black.withValues(alpha: 0.2),
        ),
      ),

      // 自定义卡片主题
      cardTheme: CardThemeData(
        color: UXThemeConfig.backgroundPrimary,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        ),
        margin: EdgeInsets.all(UXThemeConfig.paddingS),
      ),
    );
  }

  /// 获取游戏主题数据（暗色）
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: UXThemeConfig.darkColorScheme,

      // 自定义应用栏主题
      appBarTheme: AppBarTheme(
        backgroundColor: const Color(0xFF121212),
        foregroundColor: Colors.white,
        titleTextStyle: TextStyle(
          fontSize: UXThemeConfig.fontSizeL,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // 自定义按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: UXThemeConfig.primaryBlue,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(
            horizontal: UXThemeConfig.paddingL,
            vertical: UXThemeConfig.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          ),
          textStyle: TextStyle(
            fontSize: UXThemeConfig.fontSizeBody,
            fontWeight: FontWeight.bold,
          ),
          minimumSize: const Size(120, 48),
          elevation: 4,
          shadowColor: Colors.black.withValues(alpha: 0.3),
        ),
      ),

      // 自定义卡片主题
      cardTheme: CardThemeData(
        color: const Color(0xFF1E1E1E),
        shadowColor: Colors.black.withValues(alpha: 0.2),
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        ),
        margin: EdgeInsets.all(UXThemeConfig.paddingS),
      ),
    );
  }

  // ===== 主题世界配置 =====

  /// 获取主题世界的色彩方案
  static ColorScheme getThemeWorldColors(
    String worldId, {
    bool isDark = false,
  }) {
    final Color seedColor;

    switch (worldId) {
      case 'forest':
        seedColor = forestPrimary;
        break;
      case 'ocean':
        seedColor = oceanPrimary;
        break;
      case 'space':
        seedColor = spacePrimary;
        break;
      default:
        seedColor = UXThemeConfig.primaryBlue;
    }

    return isDark
        ? ColorScheme.fromSeed(
            seedColor: seedColor,
            brightness: Brightness.dark,
          )
        : ColorScheme.fromSeed(
            seedColor: seedColor,
            brightness: Brightness.light,
          );
  }

  /// 获取主题世界的完整主题
  static ThemeData getThemeWorldTheme(String worldId, {bool isDark = false}) {
    final baseTheme = isDark ? darkTheme : lightTheme;
    final colorScheme = getThemeWorldColors(worldId, isDark: isDark);

    return baseTheme.copyWith(
      colorScheme: colorScheme,
      primaryColor: colorScheme.primary,
      scaffoldBackgroundColor: colorScheme.surface,
    );
  }

  // ===== 游戏特定动画 =====

  /// 星星出现动画时长
  static const Duration starAnimationDuration = Duration(milliseconds: 800);

  /// 关卡完成动画时长
  static const Duration levelCompleteAnimationDuration = Duration(
    milliseconds: 1200,
  );

  /// 提示动画时长
  static const Duration hintAnimationDuration = Duration(milliseconds: 600);

  /// 页面转场动画时长
  static const Duration pageTransitionDuration = Duration(milliseconds: 350);

  // ===== 游戏音效配置 =====

  /// 默认音效音量
  static const double defaultSfxVolume = 0.7;

  /// 默认背景音乐音量
  static const double defaultBgmVolume = 0.5;
}

/// 游戏主题扩展
extension GameThemeExtension on ThemeData {
  /// 获取游戏主题配置
  AppTheme get game => AppTheme._();
}

/// 游戏构建上下文扩展
extension GameBuildContextExtension on BuildContext {
  /// 获取游戏主题配置
  AppTheme get gameTheme => AppTheme._();

  /// 获取响应式谜题网格大小
  double get responsivePuzzleGridSize {
    final screenWidth = MediaQuery.of(this).size.width;
    if (screenWidth < 480) {
      return 200.0;
    } else if (screenWidth < 768) {
      return 240.0;
    } else {
      return 400.0;
    }
  }

  /// 获取响应式谜题单元格大小
  double get responsivePuzzleCellSize {
    final screenWidth = MediaQuery.of(this).size.width;
    if (screenWidth < 480) {
      return 40.0;
    } else if (screenWidth < 768) {
      return 52.0;
    } else {
      return 80.0;
    }
  }
}

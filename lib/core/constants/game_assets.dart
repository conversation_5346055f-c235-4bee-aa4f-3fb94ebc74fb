import 'package:flutter/foundation.dart';

/// 衣服类型枚举 - 增强类型安全性
enum ClothingType { shirt, dress, pants, skirt, jacket }

/// 游戏素材管理类
///
/// 提供类型安全的素材路径管理，支持调试模式和错误处理
class GameAssets {
  static const String _basePath = 'assets/images';

  // 调试模式开关
  static bool _debugMode = false;

  /// 启用调试模式 - 在开发时输出素材加载信息
  static void enableDebugMode() => _debugMode = true;

  /// 禁用调试模式
  static void disableDebugMode() => _debugMode = false;

  /// 内部素材路径获取方法 - 支持调试输出
  static String _getAssetPath(String path) {
    if (_debugMode) {
      debugPrint('Loading asset: $path');
    }

    // 在debug模式下检查文件是否存在的占位符
    // 实际项目中可以添加文件存在性检查
    assert(() {
      // 在debug模式下检查文件是否存在
      // 这里可以添加实际的文件检查逻辑
      return true;
    }());

    return path;
  }

  // =============================================================================
  // 镜像对称游戏素材 - 使用类型安全的枚举
  // =============================================================================

  /// 获取镜像对称游戏中的衣服素材路径
  ///
  /// [type] 衣服类型枚举
  /// [id] 衣服ID，如 '01', '02'
  static String mirrorClothing(ClothingType type, String id) {
    final path = '$_basePath/mirror_symmetry/clothes/${type.name}_$id.png';
    return _getAssetPath(path);
  }

  /// 获取镜像对称游戏中的镜子框架素材路径
  static String mirrorFrame() {
    final path = '$_basePath/mirror_symmetry/mirror_frame.png';
    return _getAssetPath(path);
  }

  /// 获取镜像对称游戏中的特效素材路径
  ///
  /// [effect] 特效名称，如 'shine', 'reflection'
  static String mirrorEffect(String effect) {
    final path = '$_basePath/mirror_symmetry/effects/$effect.png';
    return _getAssetPath(path);
  }

  // =============================================================================
  // 图形推理游戏素材
  // =============================================================================

  /// 获取图形推理游戏中的图形素材路径
  ///
  /// [id] 图形ID，如 'circle_red', 'square_blue'
  static String graphicPattern(String id) {
    final path = '$_basePath/graphic_pattern/shapes/$id.png';
    return _getAssetPath(path);
  }

  /// 获取图形推理游戏中的网格背景素材路径
  static String graphicGrid() {
    final path = '$_basePath/graphic_pattern/grid_background.png';
    return _getAssetPath(path);
  }

  // =============================================================================
  // 空间想象游戏素材
  // =============================================================================

  /// 获取空间想象游戏中的3D模型素材路径
  ///
  /// [id] 模型ID，如 'cube_view1', 'pyramid_view1'
  static String spatialShape(String id) {
    final path = '$_basePath/spatial_visualization/3d_models/$id.png';
    return _getAssetPath(path);
  }

  /// 获取空间想象游戏中的展开图素材路径
  ///
  /// [id] 展开图ID，如 'cube_expanded', 'pyramid_expanded'
  static String spatialExpanded(String id) {
    final path = '$_basePath/spatial_visualization/expanded/$id.png';
    return _getAssetPath(path);
  }

  // =============================================================================
  // 数字逻辑游戏素材
  // =============================================================================

  /// 获取数字逻辑游戏中的水果素材路径
  ///
  /// [fruit] 水果名称，如 'apple', 'banana', 'grape'
  static String numericFruit(String fruit) {
    final path = '$_basePath/numeric_logic/fruits/$fruit.png';
    return _getAssetPath(path);
  }

  /// 获取数字逻辑游戏中的网格背景素材路径
  static String numericGrid() {
    final path = '$_basePath/numeric_logic/grid_background.png';
    return _getAssetPath(path);
  }

  // =============================================================================
  // 编程启蒙游戏素材
  // =============================================================================

  /// 获取编程启蒙游戏中的角色素材路径
  ///
  /// [state] 角色状态，如 'idle', 'walking', 'celebrating'
  static String codingCharacter(String state) {
    final path = '$_basePath/intro_coding/characters/robot_$state.png';
    return _getAssetPath(path);
  }

  /// 获取编程启蒙游戏中的指令素材路径
  ///
  /// [command] 指令名称，如 'move_forward', 'turn_left', 'turn_right'
  static String codingCommand(String command) {
    final path = '$_basePath/intro_coding/commands/$command.png';
    return _getAssetPath(path);
  }

  // =============================================================================
  // 通用UI素材
  // =============================================================================

  /// 获取通用UI按钮素材路径
  ///
  /// [type] 按钮类型，如 'primary', 'secondary', 'icon'
  static String uiButton(String type) {
    final path = '$_basePath/ui/buttons/$type.png';
    return _getAssetPath(path);
  }

  /// 获取通用UI图标素材路径
  ///
  /// [name] 图标名称，如 'hint', 'pause', 'star'
  static String uiIcon(String name) {
    final path = '$_basePath/ui/icons/$name.png';
    return _getAssetPath(path);
  }

  /// 获取通用UI背景素材路径
  ///
  /// [type] 背景类型，如 'game', 'card', 'menu'
  static String uiBackground(String type) {
    final path = '$_basePath/ui/backgrounds/$type.png';
    return _getAssetPath(path);
  }
}

/// LogicLab应用字符串常量
/// 提供应用中所有文本的集中管理，支持国际化
library;

/// 应用字符串配置类
class AppStrings {
  AppStrings._();

  // ============ 成就相关文本 ============
  
  /// 成就标题
  static const Map<String, String> achievementTitles = {
    'first_puzzle': '初学者',
    'puzzle_master_10': '谜题新手',
    'puzzle_master_50': '谜题专家',
    'speed_master': '速度大师',
    'lightning_fast': '闪电侠',
    'pattern_expert': '图形推理专家',
    'spatial_master': '空间想象大师',
    'logic_genius': '逻辑天才',
    'coding_prodigy': '编程神童',
    'perfectionist': '完美主义者',
    'no_hints_master': '独立思考者',
    'ultimate_champion': '终极冠军',
  };

  /// 成就描述
  static const Map<String, String> achievementDescriptions = {
    'first_puzzle': '完成第一个谜题',
    'puzzle_master_10': '累计完成10个谜题',
    'puzzle_master_50': '累计完成50个谜题',
    'speed_master': '在30秒内完成一个谜题',
    'lightning_fast': '在15秒内完成一个谜题',
    'pattern_expert': '图形推理技能达到5级',
    'spatial_master': '空间想象技能达到5级',
    'logic_genius': '数字逻辑技能达到5级',
    'coding_prodigy': '编程启蒙技能达到5级',
    'perfectionist': '连续10次获得3星评分',
    'no_hints_master': '不使用提示完成20个谜题',
    'ultimate_champion': '完成所有类型的谜题并达到最高等级',
  };

  // ============ 提示相关文本 ============
  
  /// 图形推理提示
  static const List<String> graphicPatternHints = [
    '观察每一行和每一列的规律，图形是如何变化的？',
    '注意图形的颜色和形状变化模式',
    '找出空白位置应该填入的图形',
  ];

  /// 空间想象提示
  static const List<String> spatialVisualizationHints = [
    '想象一下如何将展开图折叠成立体图形',
    '注意面与面的连接关系',
    '想象物体在三维空间中的旋转',
  ];

  /// 数字逻辑提示
  static const List<String> numericLogicHints = [
    '记住：每一行、每一列、每个2×2的小方格内，四种图标都只能出现一次',
    '检查是否有重复的图标在同一行、列或小方格中',
    '寻找数字之间的数学关系',
  ];

  /// 编程启蒙提示
  static const List<String> codingHints = [
    '想想小动物需要怎样移动才能到达宝物？',
    '思考算法的基本步骤',
    '注意循环和条件判断的逻辑',
  ];

  /// 镜像对称提示
  static const List<String> mirrorSymmetryHints = [
    '观察图形的对称轴位置',
    '想象图形沿对称轴翻折后的样子',
    '想象一下通过镜子看图形的样子',
  ];

  // ============ 游戏状态文本 ============
  
  /// 提示级别描述
  static const Map<int, String> hintLevelDescriptions = {
    1: '轻度提示',
    2: '中度提示',
    3: '强度提示',
  };

  /// 提示类型描述
  static const Map<String, String> hintTypeDescriptions = {
    'text': '文字提示',
    'highlight': '高亮提示',
    'exclude': '排除提示',
    'direction': '方向提示',
  };

  /// 完成等级描述
  static const Map<int, String> completionLevels = {
    0: '未完成',
    1: '及格',
    2: '良好',
    3: '优秀',
  };

  // ============ 错误消息文本 ============
  
  /// 通用错误消息
  static const String genericErrorMessage = '操作失败，请稍后重试';
  static const String networkErrorMessage = '网络连接失败，请检查网络设置';
  static const String dataErrorMessage = '数据加载失败，请重新启动应用';

  /// 提示相关错误消息
  static const String hintUnavailableMessage = '提示暂不可用';
  static const String hintLimitExceededMessage = '提示次数已用完';
  static const String hintCooldownMessage = '提示冷却中，请稍后再试';

  /// 成就相关错误消息
  static const String achievementNotFoundMessage = '成就不存在';
  static const String achievementAlreadyUnlockedMessage = '成就已解锁';

  // ============ 界面文本 ============
  
  /// 按钮文本
  static const String continueButton = '继续';
  static const String retryButton = '重试';
  static const String skipButton = '跳过';
  static const String backButton = '返回';
  static const String nextButton = '下一个';
  static const String finishButton = '完成';
  static const String startButton = '开始';
  static const String pauseButton = '暂停';
  static const String resumeButton = '继续';
  static const String hintButton = '提示';

  /// 状态文本
  static const String loadingText = '加载中...';
  static const String completedText = '已完成';
  static const String lockedText = '已锁定';
  static const String unlockedText = '已解锁';
  static const String perfectText = '完美';

  /// 时间相关文本
  static const String secondsAgo = '秒前';
  static const String minutesAgo = '分钟前';
  static const String hoursAgo = '小时前';
  static const String daysAgo = '天前';
  static const String justNow = '刚刚';

  // ============ 通知文本 ============
  
  /// 成就通知
  static const String achievementUnlockedTitle = '解锁成就';
  static const String multipleAchievementsUnlockedTitle = '解锁了多个成就！';
  static const String achievementPointsEarned = '获得了成就点数';

  /// 游戏通知
  static const String puzzleCompletedTitle = '谜题完成！';
  static const String newRecordTitle = '新纪录！';
  static const String perfectCompletionTitle = '完美完成！';

  // ============ 辅助方法 ============
  
  /// 获取成就标题
  static String getAchievementTitle(String achievementId) {
    return achievementTitles[achievementId] ?? '未知成就';
  }

  /// 获取成就描述
  static String getAchievementDescription(String achievementId) {
    return achievementDescriptions[achievementId] ?? '暂无描述';
  }

  /// 获取提示文本
  static String getHintText(String puzzleType, int hintLevel) {
    List<String> hints;
    switch (puzzleType) {
      case 'graphicPattern':
        hints = graphicPatternHints;
        break;
      case 'spatialVisualization':
        hints = spatialVisualizationHints;
        break;
      case 'numericLogic':
        hints = numericLogicHints;
        break;
      case 'coding':
        hints = codingHints;
        break;
      case 'mirrorSymmetry':
        hints = mirrorSymmetryHints;
        break;
      default:
        return '暂无提示';
    }
    
    if (hintLevel <= 0 || hintLevel > hints.length) {
      return '暂无提示';
    }
    
    return hints[hintLevel - 1];
  }

  /// 获取提示级别描述
  static String getHintLevelDescription(int level) {
    return hintLevelDescriptions[level] ?? '提示';
  }

  /// 获取提示类型描述
  static String getHintTypeDescription(String type) {
    return hintTypeDescriptions[type] ?? '提示';
  }

  /// 获取完成等级描述
  static String getCompletionLevelDescription(int starRating) {
    return completionLevels[starRating] ?? '未知';
  }

  /// 格式化时间差
  static String formatTimeDifference(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}$daysAgo';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}$hoursAgo';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}$minutesAgo';
    } else if (difference.inSeconds > 0) {
      return '${difference.inSeconds}$secondsAgo';
    } else {
      return justNow;
    }
  }

  /// 格式化时间显示
  static String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  /// 格式化多个成就通知标题
  static String formatMultipleAchievementsTitle(int count) {
    return '解锁了 $count 个成就！';
  }

  /// 格式化成就点数消息
  static String formatAchievementPoints(int points) {
    return '获得了 $points 点成就点数';
  }
} 
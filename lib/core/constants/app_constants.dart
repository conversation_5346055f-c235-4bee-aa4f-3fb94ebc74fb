/// LogicLab应用核心常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'LogicLab';
  static const String appVersion = '1.0.0';
  
  // 用户相关
  static const int maxUsers = 4;
  static const int maxNicknameLength = 20;
  
  // 游戏相关
  static const int maxStarsPerLevel = 3;
  static const int hintCooldownSeconds = 30;
  static const int maxErrorsBeforeHint = 2;
  static const int recentLevelsForDifficulty = 10;
  
  // 时间相关
  static const int sessionTimeoutMinutes = 60;
  static const int autoSaveIntervalSeconds = 30;
  
  // 音频相关
  static const double defaultMusicVolume = 0.7;
  static const double defaultSfxVolume = 0.8;
  
  // 动画相关
  static const int celebrationAnimationDurationMs = 2500;
  static const int hintAnimationDurationMs = 1000;
  
  // 数据库相关
  static const String userProfileBoxName = 'user_profiles';
  static const String settingsBoxName = 'app_settings';
  static const String achievementsBoxName = 'achievements';
}

/// 谜题类型枚举
enum PuzzleType {
  graphicPattern3x3('GRAPHIC_PATTERN_3X3', '图形推理'),
  spatialVisualization('SPATIAL_VISUALIZATION', '空间想象'),
  numericLogic('NUMERIC_LOGIC', '数字逻辑'),
  introToCoding('INTRO_TO_CODING', '编程启蒙'),
  mirrorSymmetry('MIRROR_SYMMETRY', '镜像对称');

  const PuzzleType(this.id, this.displayName);
  
  final String id;
  final String displayName;
}

/// 难度等级枚举
enum DifficultyLevel {
  easy('easy', '简单', 1),
  medium('medium', '中等', 2),
  hard('hard', '困难', 3),
  expert('expert', '专家', 4);

  const DifficultyLevel(this.id, this.displayName, this.level);
  
  final String id;
  final String displayName;
  final int level;
}

/// 主题世界枚举
enum ThemeWorld {
  forest('forest', '奇妙森林', '🌲'),
  ocean('ocean', '蔚蓝海洋', '🌊'),
  space('space', '梦幻太空', '🚀');

  const ThemeWorld(this.id, this.displayName, this.emoji);
  
  final String id;
  final String displayName;
  final String emoji;
} 
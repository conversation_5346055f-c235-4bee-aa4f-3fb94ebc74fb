import 'dart:math';
import 'package:flutter/foundation.dart';
import '../constants/game_asset_categories.dart';
import '../managers/asset_manager.dart';
import '../utils/result.dart';
import '../exceptions/app_exceptions.dart';
import '../../domain/entities/puzzle_entity.dart';
import '../../domain/value_objects/puzzle_types.dart';

/// 基于分类素材的题目生成器
///
/// 支持各种游戏类型的随机题目生成，使用分类素材系统
class PuzzleGenerator {
  static PuzzleGenerator? _instance;
  static PuzzleGenerator get instance => _instance ??= PuzzleGenerator._();

  PuzzleGenerator._();

  final AssetManager _assetManager = AssetManager.instance;
  final Random _random = Random();

  // =============================================================================
  // 镜像对称题目生成
  // =============================================================================

  /// 生成镜像对称题目
  Future<Result<PuzzleEntity>> generateMirrorSymmetryPuzzle({
    int difficulty = 1,
    List<String>? requiredTags,
    Map<String, dynamic>? constraints,
  }) async {
    try {
      // 根据难度调整素材选择范围
      final (clothingTypes, patternTypes, colorTypes) =
          _getMirrorSymmetryConstraints(difficulty);

      // 生成题目素材组合
      final assetResult = await _assetManager.generateRandomMirrorSymmetryAsset(
        difficulty: difficulty,
        requiredTags: requiredTags,
        allowedClothingTypes: clothingTypes,
        allowedPatternTypes: patternTypes,
        allowedColorTypes: colorTypes,
      );

      if (assetResult.isFailure) {
        return Result.failure(assetResult.exception!);
      }

      final assetCombination = assetResult.data!;

      // 生成题目数据
      final puzzleData = await _generateMirrorSymmetryPuzzleData(
        assetCombination,
        difficulty,
      );

      // 创建题目实体
      final puzzle = PuzzleEntity(
        levelId: 'mirror_${DateTime.now().millisecondsSinceEpoch}',
        schemaVersion: '1.0.0',
        author: 'PuzzleGenerator',
        tags: assetCombination.tags,
        puzzleType: PuzzleType.mirrorSymmetry,
        difficulty:
            DifficultyLevel.values[difficulty - 1], // 转换int到DifficultyLevel
        prompt: '找出镜像对称的图案',
        data: puzzleData,
        correctAnswer: puzzleData['correctAnswer'],
        themeWorld: null,
        orderInWorld: null,
        maxHints: 3,
      );

      return Result.success(puzzle);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'MIRROR_PUZZLE_GENERATION_FAILED',
          message: '镜像对称题目生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 获取镜像对称游戏的约束条件
  (List<ClothingType>, List<PatternType>, List<ColorType>)
  _getMirrorSymmetryConstraints(int difficulty) {
    switch (difficulty) {
      case 1:
        // 简单：基础衣服类型，简单花纹，基础颜色
        return (
          [ClothingType.shirt, ClothingType.dress, ClothingType.tshirt],
          [PatternType.heart, PatternType.star, PatternType.circle],
          [ColorType.red, ColorType.blue, ColorType.green, ColorType.yellow],
        );
      case 2:
        // 中等：增加衣服类型，增加花纹复杂度
        return (
          [
            ClothingType.shirt,
            ClothingType.dress,
            ClothingType.tshirt,
            ClothingType.pants,
          ],
          [
            PatternType.heart,
            PatternType.star,
            PatternType.circle,
            PatternType.square,
            PatternType.triangle,
          ],
          [
            ColorType.red,
            ColorType.blue,
            ColorType.green,
            ColorType.yellow,
            ColorType.purple,
            ColorType.orange,
          ],
        );
      case 3:
        // 困难：更多衣服类型，复杂花纹
        return (
          ClothingType.values.take(6).toList(),
          PatternType.values.take(8).toList(),
          ColorType.values.take(8).toList(),
        );
      case 4:
        // 专家：几乎所有类型
        return (
          ClothingType.values.take(7).toList(),
          PatternType.values.take(10).toList(),
          ColorType.values.take(10).toList(),
        );
      default:
        // 大师：所有类型
        return (ClothingType.values, PatternType.values, ColorType.values);
    }
  }

  /// 生成镜像对称题目数据
  Future<Map<String, dynamic>> _generateMirrorSymmetryPuzzleData(
    MirrorSymmetryAssetCombination assetCombination,
    int difficulty,
  ) async {
    // 获取主题目素材
    final mainAssetResult = await _assetManager.getMirrorSymmetryAsset(
      assetCombination,
    );
    final mainAssetPath = mainAssetResult.isSuccess
        ? mainAssetResult.data!
        : '';

    // 生成选项
    final options = await _generateMirrorSymmetryOptions(
      assetCombination,
      difficulty,
    );

    // 随机选择正确答案的位置
    final correctIndex = _random.nextInt(options.length);

    return {
      'mainImage': mainAssetPath,
      'clothingType': assetCombination.clothingType.assetKey,
      'patternType': assetCombination.patternType.assetKey,
      'colorType': assetCombination.colorType.assetKey,
      'options': options,
      'correctAnswer': correctIndex,
      'difficulty': difficulty,
      'showMirrorLine': difficulty <= 2, // 简单难度显示镜像线
    };
  }

  /// 生成镜像对称选项
  Future<List<Map<String, dynamic>>> _generateMirrorSymmetryOptions(
    MirrorSymmetryAssetCombination correctAsset,
    int difficulty,
  ) async {
    final options = <Map<String, dynamic>>[];

    // 添加正确答案
    final correctAssetResult = await _assetManager.getMirrorSymmetryAsset(
      correctAsset,
    );
    options.add({
      'assetPath': correctAssetResult.isSuccess ? correctAssetResult.data! : '',
      'isCorrect': true,
      'description': correctAsset.description,
    });

    // 生成错误选项
    final optionCount = difficulty <= 2 ? 3 : 4; // 简单难度3个选项，困难难度4个选项
    for (int i = 1; i < optionCount; i++) {
      final wrongAsset = await _generateWrongMirrorSymmetryOption(
        correctAsset,
        i,
      );
      final wrongAssetResult = await _assetManager.getMirrorSymmetryAsset(
        wrongAsset,
      );

      options.add({
        'assetPath': wrongAssetResult.isSuccess ? wrongAssetResult.data! : '',
        'isCorrect': false,
        'description': wrongAsset.description,
      });
    }

    // 打乱选项顺序
    options.shuffle(_random);

    return options;
  }

  /// 生成错误的镜像对称选项
  Future<MirrorSymmetryAssetCombination> _generateWrongMirrorSymmetryOption(
    MirrorSymmetryAssetCombination correctAsset,
    int optionIndex,
  ) async {
    switch (optionIndex) {
      case 1:
        // 改变花纹类型
        final wrongPatterns = PatternType.values
            .where((p) => p != correctAsset.patternType)
            .toList();
        final wrongPattern =
            wrongPatterns[_random.nextInt(wrongPatterns.length)];
        return MirrorSymmetryAssetCombination(
          clothingType: correctAsset.clothingType,
          patternType: wrongPattern,
          colorType: correctAsset.colorType,
          name:
              '${correctAsset.clothingType.displayName}_${wrongPattern.displayName}_${correctAsset.colorType.displayName}',
          description:
              '${correctAsset.colorType.displayName}的${correctAsset.clothingType.displayName}，带有${wrongPattern.displayName}图案',
        );
      case 2:
        // 改变颜色类型
        final wrongColors = ColorType.values
            .where((c) => c != correctAsset.colorType)
            .toList();
        final wrongColor = wrongColors[_random.nextInt(wrongColors.length)];
        return MirrorSymmetryAssetCombination(
          clothingType: correctAsset.clothingType,
          patternType: correctAsset.patternType,
          colorType: wrongColor,
          name:
              '${correctAsset.clothingType.displayName}_${correctAsset.patternType.displayName}_${wrongColor.displayName}',
          description:
              '${wrongColor.displayName}的${correctAsset.clothingType.displayName}，带有${correctAsset.patternType.displayName}图案',
        );
      default:
        // 改变衣服类型
        final wrongClothings = ClothingType.values
            .where((c) => c != correctAsset.clothingType)
            .toList();
        final wrongClothing =
            wrongClothings[_random.nextInt(wrongClothings.length)];
        return MirrorSymmetryAssetCombination(
          clothingType: wrongClothing,
          patternType: correctAsset.patternType,
          colorType: correctAsset.colorType,
          name:
              '${wrongClothing.displayName}_${correctAsset.patternType.displayName}_${correctAsset.colorType.displayName}',
          description:
              '${correctAsset.colorType.displayName}的${wrongClothing.displayName}，带有${correctAsset.patternType.displayName}图案',
        );
    }
  }

  // =============================================================================
  // 图形推理题目生成
  // =============================================================================

  /// 生成图形推理题目
  Future<Result<PuzzleEntity>> generateGraphicPatternPuzzle({
    int difficulty = 1,
    List<String>? requiredTags,
    Map<String, dynamic>? constraints,
  }) async {
    try {
      // 根据难度调整素材选择范围
      final (shapeTypes, fillTypes, colorTypes, sizeTypes) =
          _getGraphicPatternConstraints(difficulty);

      // 生成3x3网格的图形推理题目
      final gridAssets = <GraphicPatternAssetCombination>[];

      // 生成8个已知图形（3x3网格缺少右下角）
      for (int i = 0; i < 8; i++) {
        final assetResult = await _assetManager
            .generateRandomGraphicPatternAsset(
              difficulty: difficulty,
              requiredTags: requiredTags,
              allowedShapeTypes: shapeTypes,
              allowedFillTypes: fillTypes,
              allowedColorTypes: colorTypes,
              allowedSizeTypes: sizeTypes,
            );

        if (assetResult.isSuccess) {
          gridAssets.add(assetResult.data!);
        }
      }

      // 生成正确答案（基于规律）
      final correctAsset = _generateGraphicPatternCorrectAnswer(
        gridAssets,
        difficulty,
      );

      // 生成题目数据
      final puzzleData = await _generateGraphicPatternPuzzleData(
        gridAssets,
        correctAsset,
        difficulty,
      );

      // 创建题目实体
      final puzzle = PuzzleEntity(
        levelId: 'graphic_${DateTime.now().millisecondsSinceEpoch}',
        schemaVersion: '1.0.0',
        author: 'PuzzleGenerator',
        tags: requiredTags ?? [],
        puzzleType: PuzzleType.graphicPattern3x3,
        difficulty:
            DifficultyLevel.values[difficulty - 1], // 转换int到DifficultyLevel
        prompt: '找出空白位置应该填入的图形',
        data: puzzleData,
        correctAnswer: puzzleData['correctAnswer'],
        themeWorld: null,
        orderInWorld: null,
        maxHints: 3,
      );

      return Result.success(puzzle);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'GRAPHIC_PUZZLE_GENERATION_FAILED',
          message: '图形推理题目生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 获取图形推理游戏的约束条件
  (List<ShapeType>, List<FillType>, List<ColorType>, List<SizeType>)
  _getGraphicPatternConstraints(int difficulty) {
    switch (difficulty) {
      case 1:
        // 简单：基础图形，简单填充，基础颜色，固定大小
        return (
          [ShapeType.circle, ShapeType.square, ShapeType.triangle],
          [FillType.solid, FillType.outline],
          [ColorType.red, ColorType.blue, ColorType.green],
          [SizeType.medium],
        );
      case 2:
        // 中等：增加图形类型，增加填充类型
        return (
          [
            ShapeType.circle,
            ShapeType.square,
            ShapeType.triangle,
            ShapeType.diamond,
          ],
          [FillType.solid, FillType.outline, FillType.striped],
          [ColorType.red, ColorType.blue, ColorType.green, ColorType.yellow],
          [SizeType.small, SizeType.medium],
        );
      case 3:
        // 困难：更多图形类型，更多填充类型
        return (
          ShapeType.values.take(6).toList(),
          FillType.values.take(4).toList(),
          ColorType.values.take(6).toList(),
          [SizeType.small, SizeType.medium, SizeType.large],
        );
      default:
        // 专家：所有类型
        return (
          ShapeType.values,
          FillType.values,
          ColorType.values,
          SizeType.values,
        );
    }
  }

  /// 生成图形推理的正确答案
  GraphicPatternAssetCombination _generateGraphicPatternCorrectAnswer(
    List<GraphicPatternAssetCombination> gridAssets,
    int difficulty,
  ) {
    // 简化的规律生成：基于第一行的规律
    if (gridAssets.length >= 3) {
      // 尝试找出颜色规律
      final firstRowColors = gridAssets
          .take(3)
          .map((e) => e.colorType)
          .toList();
      final secondRowColors = gridAssets
          .skip(3)
          .take(3)
          .map((e) => e.colorType)
          .toList();

      // 如果前两行颜色有规律，继续这个规律
      if (firstRowColors.length == 3 && secondRowColors.length == 3) {
        final thirdRowColor = _findColorPattern(
          firstRowColors,
          secondRowColors,
        );

        return GraphicPatternAssetCombination(
          shapeType: gridAssets[6].shapeType, // 使用第三行第一个的形状
          fillType: gridAssets[7].fillType, // 使用第三行第二个的填充
          colorType: thirdRowColor,
          sizeType: SizeType.medium,
          name: 'generated_answer',
          description: '根据规律生成的答案',
        );
      }
    }

    // 如果无法找出规律，返回一个随机但合理的答案
    return GraphicPatternAssetCombination(
      shapeType: ShapeType.values[_random.nextInt(ShapeType.values.length)],
      fillType: FillType.values[_random.nextInt(FillType.values.length)],
      colorType: ColorType.values[_random.nextInt(ColorType.values.length)],
      sizeType: SizeType.medium,
      name: 'random_answer',
      description: '随机生成的答案',
    );
  }

  /// 找出颜色规律
  ColorType _findColorPattern(
    List<ColorType> firstRow,
    List<ColorType> secondRow,
  ) {
    // 简单的循环规律检测
    final allColors = [...firstRow, ...secondRow];
    final uniqueColors = allColors.toSet().toList();

    if (uniqueColors.length <= 3) {
      // 如果颜色种类不多，尝试循环规律
      final pattern = uniqueColors;
      final nextIndex = allColors.length % pattern.length;
      return pattern[nextIndex];
    }

    // 默认返回第一行的第一个颜色
    return firstRow.first;
  }

  /// 生成图形推理题目数据
  Future<Map<String, dynamic>> _generateGraphicPatternPuzzleData(
    List<GraphicPatternAssetCombination> gridAssets,
    GraphicPatternAssetCombination correctAsset,
    int difficulty,
  ) async {
    // 获取网格素材路径
    final gridAssetPaths = <String>[];
    for (final asset in gridAssets) {
      final assetResult = await _assetManager.getGraphicPatternAsset(asset);
      gridAssetPaths.add(assetResult.isSuccess ? assetResult.data! : '');
    }

    // 生成选项
    final options = await _generateGraphicPatternOptions(
      correctAsset,
      difficulty,
    );

    // 随机选择正确答案的位置
    final correctIndex = _random.nextInt(options.length);

    return {
      'gridAssets': gridAssetPaths,
      'gridSize': 3,
      'missingPosition': 8, // 右下角位置（0-8）
      'options': options,
      'correctAnswer': correctIndex,
      'difficulty': difficulty,
      'showGrid': difficulty <= 2, // 简单难度显示网格线
    };
  }

  /// 生成图形推理选项
  Future<List<Map<String, dynamic>>> _generateGraphicPatternOptions(
    GraphicPatternAssetCombination correctAsset,
    int difficulty,
  ) async {
    final options = <Map<String, dynamic>>[];

    // 添加正确答案
    final correctAssetResult = await _assetManager.getGraphicPatternAsset(
      correctAsset,
    );
    options.add({
      'assetPath': correctAssetResult.isSuccess ? correctAssetResult.data! : '',
      'isCorrect': true,
      'description': correctAsset.description,
    });

    // 生成错误选项
    final optionCount = difficulty <= 2 ? 3 : 4;
    for (int i = 1; i < optionCount; i++) {
      final wrongAsset = await _generateWrongGraphicPatternOption(
        correctAsset,
        i,
      );
      final wrongAssetResult = await _assetManager.getGraphicPatternAsset(
        wrongAsset,
      );

      options.add({
        'assetPath': wrongAssetResult.isSuccess ? wrongAssetResult.data! : '',
        'isCorrect': false,
        'description': wrongAsset.description,
      });
    }

    // 打乱选项顺序
    options.shuffle(_random);

    return options;
  }

  /// 生成错误的图形推理选项
  Future<GraphicPatternAssetCombination> _generateWrongGraphicPatternOption(
    GraphicPatternAssetCombination correctAsset,
    int optionIndex,
  ) async {
    switch (optionIndex) {
      case 1:
        // 改变形状
        final wrongShapes = ShapeType.values
            .where((s) => s != correctAsset.shapeType)
            .toList();
        final wrongShape = wrongShapes[_random.nextInt(wrongShapes.length)];
        return GraphicPatternAssetCombination(
          shapeType: wrongShape,
          fillType: correctAsset.fillType,
          colorType: correctAsset.colorType,
          sizeType: correctAsset.sizeType,
          name: 'wrong_shape_option',
          description: '错误的形状选项',
        );
      case 2:
        // 改变颜色
        final wrongColors = ColorType.values
            .where((c) => c != correctAsset.colorType)
            .toList();
        final wrongColor = wrongColors[_random.nextInt(wrongColors.length)];
        return GraphicPatternAssetCombination(
          shapeType: correctAsset.shapeType,
          fillType: correctAsset.fillType,
          colorType: wrongColor,
          sizeType: correctAsset.sizeType,
          name: 'wrong_color_option',
          description: '错误的颜色选项',
        );
      default:
        // 改变填充
        final wrongFills = FillType.values
            .where((f) => f != correctAsset.fillType)
            .toList();
        final wrongFill = wrongFills[_random.nextInt(wrongFills.length)];
        return GraphicPatternAssetCombination(
          shapeType: correctAsset.shapeType,
          fillType: wrongFill,
          colorType: correctAsset.colorType,
          sizeType: correctAsset.sizeType,
          name: 'wrong_fill_option',
          description: '错误的填充选项',
        );
    }
  }

  // =============================================================================
  // 通用工具方法
  // =============================================================================

  /// 批量生成题目
  Future<Result<List<PuzzleEntity>>> generateMultiplePuzzles(
    PuzzleType puzzleType,
    int count, {
    int difficulty = 1,
    List<String>? requiredTags,
    Map<String, dynamic>? constraints,
  }) async {
    try {
      final puzzles = <PuzzleEntity>[];

      for (int i = 0; i < count; i++) {
        final puzzleResult = switch (puzzleType) {
          PuzzleType.mirrorSymmetry => await generateMirrorSymmetryPuzzle(
            difficulty: difficulty,
            requiredTags: requiredTags,
            constraints: constraints,
          ),
          PuzzleType.graphicPattern3x3 => await generateGraphicPatternPuzzle(
            difficulty: difficulty,
            requiredTags: requiredTags,
            constraints: constraints,
          ),
          _ => Result.failure(
            DataGenerationException(
              errorCode: 'UNSUPPORTED_PUZZLE_TYPE',
              message: '不支持的题目类型: ${puzzleType.displayName}',
            ),
          ),
        };

        if (puzzleResult.isSuccess) {
          puzzles.add(puzzleResult.data!);
        } else {
          if (kDebugMode) {
            debugPrint(
              'PuzzleGenerator: Failed to generate puzzle ${i + 1}: ${puzzleResult.exception}',
            );
          }
        }
      }

      return Result.success(puzzles);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'BATCH_PUZZLE_GENERATION_FAILED',
          message: '批量题目生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // =============================================================================
  // 空间想象题目生成
  // =============================================================================

  /// 生成空间想象题目
  Future<Result<PuzzleEntity>> generateSpatialVisualizationPuzzle({
    int difficulty = 1,
    List<String>? requiredTags,
    Map<String, dynamic>? constraints,
  }) async {
    try {
      // 根据难度调整素材选择范围
      final (shapeTypes, viewTypes, materialTypes, colorTypes) =
          _getSpatialVisualizationConstraints(difficulty);

      // 生成题目素材组合
      final assetResult = await _assetManager
          .generateRandomSpatialVisualizationAsset(
            difficulty: difficulty,
            requiredTags: requiredTags,
            allowedShapeTypes: shapeTypes,
            allowedViewTypes: viewTypes,
            allowedMaterialTypes: materialTypes,
            allowedColorTypes: colorTypes,
          );

      if (assetResult.isFailure) {
        return Result.failure(assetResult.exception!);
      }

      final assetCombination = assetResult.data!;

      // 生成题目数据
      final puzzleData = await _generateSpatialVisualizationPuzzleData(
        assetCombination,
        difficulty,
      );

      // 创建题目实体
      final puzzle = PuzzleEntity(
        levelId: 'spatial_${DateTime.now().millisecondsSinceEpoch}',
        schemaVersion: '1.0.0',
        author: 'PuzzleGenerator',
        tags: assetCombination.tags,
        puzzleType: PuzzleType.spatialVisualization,
        difficulty: DifficultyLevel.values[difficulty - 1],
        prompt: '找出正确的3D图形',
        data: puzzleData,
        correctAnswer: puzzleData['correctAnswer'],
        themeWorld: null,
        orderInWorld: null,
        maxHints: 3,
      );

      return Result.success(puzzle);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'SPATIAL_PUZZLE_GENERATION_FAILED',
          message: '空间想象题目生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 获取空间想象游戏的约束条件
  (List<Shape3DType>, List<ViewType>, List<AssetMaterialType>, List<ColorType>)
  _getSpatialVisualizationConstraints(int difficulty) {
    switch (difficulty) {
      case 1:
        return (
          [Shape3DType.cube, Shape3DType.pyramid],
          [ViewType.front, ViewType.isometric],
          [AssetMaterialType.plastic, AssetMaterialType.wood],
          [ColorType.red, ColorType.blue, ColorType.green],
        );
      case 2:
        return (
          Shape3DType.values.take(4).toList(),
          ViewType.values.take(4).toList(),
          AssetMaterialType.values.take(3).toList(),
          ColorType.values.take(6).toList(),
        );
      default:
        return (
          Shape3DType.values,
          ViewType.values,
          AssetMaterialType.values,
          ColorType.values,
        );
    }
  }

  /// 生成空间想象题目数据
  Future<Map<String, dynamic>> _generateSpatialVisualizationPuzzleData(
    SpatialVisualizationAssetCombination assetCombination,
    int difficulty,
  ) async {
    // 获取主题目素材
    final mainAssetResult = await _assetManager.getSpatialVisualizationAsset(
      assetCombination,
    );
    final mainAssetPath = mainAssetResult.isSuccess
        ? mainAssetResult.data!
        : '';

    // 生成选项
    final options = await _generateSpatialVisualizationOptions(
      assetCombination,
      difficulty,
    );

    // 随机选择正确答案的位置
    final correctIndex = _random.nextInt(options.length);

    return {
      'mainImage': mainAssetPath,
      'shapeType': assetCombination.shapeType.assetKey,
      'viewType': assetCombination.viewType.assetKey,
      'materialType': assetCombination.materialType.assetKey,
      'colorType': assetCombination.colorType.assetKey,
      'options': options,
      'correctAnswer': correctIndex,
      'difficulty': difficulty,
      'showGridLines': difficulty <= 2,
    };
  }

  /// 生成空间想象选项
  Future<List<Map<String, dynamic>>> _generateSpatialVisualizationOptions(
    SpatialVisualizationAssetCombination correctAsset,
    int difficulty,
  ) async {
    final options = <Map<String, dynamic>>[];

    // 添加正确答案
    final correctAssetResult = await _assetManager.getSpatialVisualizationAsset(
      correctAsset,
    );
    options.add({
      'assetPath': correctAssetResult.isSuccess ? correctAssetResult.data! : '',
      'isCorrect': true,
      'description': correctAsset.description,
    });

    // 生成错误选项
    final optionCount = difficulty <= 2 ? 3 : 4;
    for (int i = 1; i < optionCount; i++) {
      final wrongAsset = await _generateWrongSpatialVisualizationOption(
        correctAsset,
        i,
      );
      final wrongAssetResult = await _assetManager.getSpatialVisualizationAsset(
        wrongAsset,
      );

      options.add({
        'assetPath': wrongAssetResult.isSuccess ? wrongAssetResult.data! : '',
        'isCorrect': false,
        'description': wrongAsset.description,
      });
    }

    // 打乱选项顺序
    options.shuffle(_random);

    return options;
  }

  /// 生成错误的空间想象选项
  Future<SpatialVisualizationAssetCombination>
  _generateWrongSpatialVisualizationOption(
    SpatialVisualizationAssetCombination correctAsset,
    int variationIndex,
  ) async {
    switch (variationIndex) {
      case 1:
        // 改变形状
        final differentShapes = Shape3DType.values
            .where((s) => s != correctAsset.shapeType)
            .toList();
        return SpatialVisualizationAssetCombination(
          shapeType: differentShapes[_random.nextInt(differentShapes.length)],
          viewType: correctAsset.viewType,
          materialType: correctAsset.materialType,
          colorType: correctAsset.colorType,
          name: 'wrong_shape_$variationIndex',
          description: '错误的形状选项',
        );
      case 2:
        // 改变视角
        final differentViews = ViewType.values
            .where((v) => v != correctAsset.viewType)
            .toList();
        return SpatialVisualizationAssetCombination(
          shapeType: correctAsset.shapeType,
          viewType: differentViews[_random.nextInt(differentViews.length)],
          materialType: correctAsset.materialType,
          colorType: correctAsset.colorType,
          name: 'wrong_view_$variationIndex',
          description: '错误的视角选项',
        );
      default:
        // 改变材质
        final differentMaterials = AssetMaterialType.values
            .where((m) => m != correctAsset.materialType)
            .toList();
        return SpatialVisualizationAssetCombination(
          shapeType: correctAsset.shapeType,
          viewType: correctAsset.viewType,
          materialType:
              differentMaterials[_random.nextInt(differentMaterials.length)],
          colorType: correctAsset.colorType,
          name: 'wrong_material_$variationIndex',
          description: '错误的材质选项',
        );
    }
  }

  // =============================================================================
  // 编程启蒙题目生成
  // =============================================================================

  /// 生成编程启蒙题目
  Future<Result<PuzzleEntity>> generateIntroCodingPuzzle({
    int difficulty = 1,
    List<String>? requiredTags,
    Map<String, dynamic>? constraints,
  }) async {
    try {
      // 根据难度调整素材选择范围
      final (characterTypes, characterStates, environmentTypes) =
          _getIntroCodingConstraints(difficulty);

      // 生成题目素材组合
      final assetResult = await _assetManager.generateRandomIntroCodingAsset(
        difficulty: difficulty,
        requiredTags: requiredTags,
        allowedCharacterTypes: characterTypes,
        allowedCharacterStates: characterStates,
        allowedEnvironmentTypes: environmentTypes,
      );

      if (assetResult.isFailure) {
        return Result.failure(assetResult.exception!);
      }

      final assetCombination = assetResult.data!;

      // 生成题目数据
      final puzzleData = await _generateIntroCodingPuzzleData(
        assetCombination,
        difficulty,
      );

      // 创建题目实体
      final puzzle = PuzzleEntity(
        levelId: 'coding_${DateTime.now().millisecondsSinceEpoch}',
        schemaVersion: '1.0.0',
        author: 'PuzzleGenerator',
        tags: assetCombination.tags,
        puzzleType: PuzzleType.introToCoding,
        difficulty: DifficultyLevel.values[difficulty - 1],
        prompt: '编写正确的指令序列',
        data: puzzleData,
        correctAnswer: puzzleData['correctAnswer'],
        themeWorld: null,
        orderInWorld: null,
        maxHints: 3,
      );

      return Result.success(puzzle);
    } catch (e, stackTrace) {
      return Result.failure(
        DataGenerationException(
          errorCode: 'CODING_PUZZLE_GENERATION_FAILED',
          message: '编程启蒙题目生成失败',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// 获取编程启蒙游戏的约束条件
  (List<CharacterType>, List<CharacterState>, List<EnvironmentType>)
  _getIntroCodingConstraints(int difficulty) {
    switch (difficulty) {
      case 1:
        return (
          [CharacterType.robot],
          [CharacterState.idle, CharacterState.walking],
          [EnvironmentType.laboratory, EnvironmentType.city],
        );
      case 2:
        return (
          CharacterType.values.take(3).toList(),
          CharacterState.values.take(4).toList(),
          EnvironmentType.values.take(4).toList(),
        );
      default:
        return (
          CharacterType.values,
          CharacterState.values,
          EnvironmentType.values,
        );
    }
  }

  /// 生成编程启蒙题目数据
  Future<Map<String, dynamic>> _generateIntroCodingPuzzleData(
    CodingAssetCombination assetCombination,
    int difficulty,
  ) async {
    // 获取角色和环境素材
    final characterAssetPath = assetCombination.generateCharacterAssetPath();
    final environmentAssetPath = assetCombination
        .generateEnvironmentAssetPath();

    // 生成迷宫地图
    final mazeData = _generateMazeData(difficulty);

    // 生成可用指令
    final availableCommands = _generateAvailableCommands(difficulty);

    // 生成正确答案序列
    final correctSequence = _generateCorrectCommandSequence(
      mazeData,
      difficulty,
    );

    return {
      'characterAsset': characterAssetPath,
      'environmentAsset': environmentAssetPath,
      'characterType': assetCombination.characterType.assetKey,
      'characterState': assetCombination.characterState.assetKey,
      'environmentType': assetCombination.environmentType.assetKey,
      'mazeData': mazeData,
      'availableCommands': availableCommands,
      'correctAnswer': correctSequence,
      'difficulty': difficulty,
      'maxCommands': difficulty <= 2 ? 5 : 10,
    };
  }

  /// 生成迷宫数据
  Map<String, dynamic> _generateMazeData(int difficulty) {
    final size = difficulty <= 2 ? 4 : 6;
    final maze = List.generate(size, (i) => List.generate(size, (j) => 0));

    // 设置起点和终点
    maze[0][0] = 1; // 起点
    maze[size - 1][size - 1] = 2; // 终点

    // 添加一些障碍物
    final obstacleCount = (size * size * 0.2).round();
    for (int i = 0; i < obstacleCount; i++) {
      final x = _random.nextInt(size);
      final y = _random.nextInt(size);
      if (maze[x][y] == 0) {
        maze[x][y] = 3; // 障碍物
      }
    }

    return {
      'size': size,
      'grid': maze,
      'startPosition': [0, 0],
      'endPosition': [size - 1, size - 1],
    };
  }

  /// 生成可用指令
  List<Map<String, dynamic>> _generateAvailableCommands(int difficulty) {
    final commands = <Map<String, dynamic>>[];

    // 基础指令
    commands.addAll([
      {'type': 'move_forward', 'icon': 'arrow_upward', 'name': '前进'},
      {'type': 'turn_left', 'icon': 'turn_left', 'name': '左转'},
      {'type': 'turn_right', 'icon': 'turn_right', 'name': '右转'},
    ]);

    // 高级指令
    if (difficulty >= 2) {
      commands.addAll([
        {'type': 'jump', 'icon': 'jump', 'name': '跳跃'},
        {'type': 'wait', 'icon': 'pause', 'name': '等待'},
      ]);
    }

    if (difficulty >= 3) {
      commands.addAll([
        {'type': 'repeat', 'icon': 'repeat', 'name': '重复'},
        {'type': 'if_condition', 'icon': 'help', 'name': '如果'},
      ]);
    }

    return commands;
  }

  /// 生成正确的指令序列
  List<String> _generateCorrectCommandSequence(
    Map<String, dynamic> mazeData,
    int difficulty,
  ) {
    final sequence = <String>[];

    // 简化的路径生成逻辑
    final size = mazeData['size'] as int;

    // 基础路径：向右和向下
    for (int i = 0; i < size - 1; i++) {
      sequence.add('move_forward');
      if (i < size - 2) sequence.add('turn_right');
    }

    sequence.add('turn_right');
    for (int i = 0; i < size - 1; i++) {
      sequence.add('move_forward');
    }

    return sequence;
  }
}

import 'package:flutter/material.dart';
import '../../data/models/puzzle.dart';

class CodingWidget extends StatelessWidget {
  final CodingData puzzleData;
  final List<String> currentCommands;
  final Function(String)? onCommandAdded;
  final Function(int)? onCommandRemoved;
  final Function()? onCommandsCleared;
  final Function()? onRunCommands;
  final bool isRunning;
  final bool showHint;
  final String? hintDirection;

  const CodingWidget({
    super.key,
    required this.puzzleData,
    required this.currentCommands,
    this.onCommandAdded,
    this.onCommandRemoved,
    this.onCommandsCleared,
    this.onRunCommands,
    this.isRunning = false,
    this.showHint = false,
    this.hintDirection,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 迷宫显示区域
        _buildMazeArea(),
        
        const SizedBox(height: 16),
        
        // 指令序列区域
        _buildCommandSequenceArea(),
        
        const SizedBox(height: 16),
        
        // 可用指令区域
        _buildAvailableCommandsArea(),
        
        const SizedBox(height: 16),
        
        // 控制按钮区域
        _buildControlButtonsArea(),
      ],
    );
  }

  Widget _buildMazeArea() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              '帮助小动物找到宝物',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            // 迷宫网格
            AspectRatio(
              aspectRatio: 1.0,
              child: _buildMazeGrid(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMazeGrid() {
    final mazeGrid = puzzleData.maze['grid'] as List<List<int>>;
    final rows = mazeGrid.length;
    final cols = mazeGrid[0].length;

    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: cols,
        crossAxisSpacing: 1,
        mainAxisSpacing: 1,
      ),
      itemCount: rows * cols,
      itemBuilder: (context, index) {
        final row = index ~/ cols;
        final col = index % cols;
        return _buildMazeCell(row, col, mazeGrid[row][col]);
      },
    );
  }

  Widget _buildMazeCell(int row, int col, int cellType) {
    final startX = puzzleData.startPosition['x']!;
    final startY = puzzleData.startPosition['y']!;
    final endX = puzzleData.endPosition['x']!;
    final endY = puzzleData.endPosition['y']!;

    final isStart = col == startX && row == startY;
    final isEnd = col == endX && row == endY;
    final isWall = cellType == 1;

    return Container(
      decoration: BoxDecoration(
        color: isWall
            ? Colors.grey[800]
            : isStart
                ? Colors.green[100]
                : isEnd
                    ? Colors.yellow[100]
                    : Colors.grey[50],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 0.5,
        ),
      ),
      child: Center(
        child: isStart
            ? Icon(
                Icons.pets,
                color: Colors.green[600],
                size: 20,
              )
            : isEnd
                ? Icon(
                    Icons.star,
                    color: Colors.amber[600],
                    size: 20,
                  )
                : null,
      ),
    );
  }

  Widget _buildCommandSequenceArea() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '指令序列 (${currentCommands.length}/${puzzleData.maxCommands})',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                if (showHint && hintDirection != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.yellow[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.yellow[600]!),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.lightbulb,
                          color: Colors.yellow[700],
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '提示：$hintDirection',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.yellow[800],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            // 指令序列显示
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(minHeight: 60),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: currentCommands.isEmpty
                  ? Center(
                      child: Text(
                        '拖拽指令到这里',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                      ),
                    )
                  : Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: currentCommands.asMap().entries.map((entry) {
                        final index = entry.key;
                        final command = entry.value;
                        return _buildCommandChip(command, index);
                      }).toList(),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommandChip(String command, int index) {
    return GestureDetector(
      onTap: () => onCommandRemoved?.call(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _getCommandColor(command),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getCommandIcon(command),
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              _getCommandDisplayName(command),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              width: 16,
              height: 16,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.grey,
                size: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableCommandsArea() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              '可用指令',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: puzzleData.availableCommands.map((command) {
                return _buildAvailableCommandButton(command);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableCommandButton(String command) {
    final canAdd = currentCommands.length < puzzleData.maxCommands;

    return GestureDetector(
      onTap: canAdd ? () => onCommandAdded?.call(command) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: canAdd ? _getCommandColor(command) : Colors.grey[300],
          borderRadius: BorderRadius.circular(12),
          boxShadow: canAdd
              ? [
                  BoxShadow(
                    color: _getCommandColor(command).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getCommandIcon(command),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              _getCommandDisplayName(command),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtonsArea() {
    return Row(
      children: [
        // 清空按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: currentCommands.isNotEmpty && !isRunning
                ? onCommandsCleared
                : null,
            icon: const Icon(Icons.clear_all),
            label: const Text('清空'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 运行按钮
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: currentCommands.isNotEmpty && !isRunning
                ? onRunCommands
                : null,
            icon: isRunning
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(isRunning ? '运行中...' : '运行'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 获取指令颜色
  Color _getCommandColor(String command) {
    switch (command.toLowerCase()) {
      case 'forward':
        return const Color(0xFF2196F3); // 蓝色
      case 'turn_left':
        return const Color(0xFFFF9800); // 橙色
      case 'turn_right':
        return const Color(0xFF9C27B0); // 紫色
      case 'jump':
        return const Color(0xFF4CAF50); // 绿色
      case 'wait':
        return const Color(0xFF607D8B); // 蓝灰色
      default:
        return Colors.grey;
    }
  }

  // 获取指令图标
  IconData _getCommandIcon(String command) {
    switch (command.toLowerCase()) {
      case 'forward':
        return Icons.arrow_upward;
      case 'turn_left':
        return Icons.turn_left;
      case 'turn_right':
        return Icons.turn_right;
      case 'jump':
        return Icons.height;
      case 'wait':
        return Icons.pause;
      default:
        return Icons.help;
    }
  }

  // 获取指令显示名称
  String _getCommandDisplayName(String command) {
    switch (command.toLowerCase()) {
      case 'forward':
        return '前进';
      case 'turn_left':
        return '左转';
      case 'turn_right':
        return '右转';
      case 'jump':
        return '跳跃';
      case 'wait':
        return '等待';
      default:
        return command;
    }
  }
} 
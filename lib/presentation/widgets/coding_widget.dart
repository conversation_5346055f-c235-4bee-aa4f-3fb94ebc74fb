import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../core/constants/game_assets.dart';
import '../../core/utils/game_animations.dart';
import 'game_core/base_game_widget.dart';

/// 编程启蒙游戏组件
///
/// 基于新架构的编程启蒙游戏实现，包含：
/// - 基础编程概念和算法思维
/// - 指令序列编程
/// - 类型安全的素材管理
/// - 统一的动画系统
/// - 通用的游戏基类
class CodingWidget extends BaseGameWidget {
  const CodingWidget({
    super.key,
    required super.puzzleData,
    required super.onAnswerSubmitted,
    super.onHintRequested,
    super.onRestart,
    super.onPause,
  });

  @override
  State<CodingWidget> createState() => _CodingWidgetState();
}

class _CodingWidgetState extends BaseGameWidgetState<CodingWidget> {
  
  // =============================================================================
  // 游戏特定状态
  // =============================================================================
  
  /// 当前指令序列
  final List<String> _currentCommands = [];
  
  /// 是否正在运行指令
  bool _isRunning = false;
  
  /// 当前机器人位置
  int _robotPosition = 0;
  
  /// 机器人朝向 (0: 上, 1: 右, 2: 下, 3: 左)
  int _robotDirection = 0;
  
  /// 迷宫动画控制器
  late AnimationController _mazeAnimationController;
  
  /// 指令序列动画控制器
  late AnimationController _commandAnimationController;
  
  /// 机器人移动动画控制器
  late AnimationController _robotMoveController;
  
  /// 指令执行动画控制器
  late AnimationController _commandExecutionController;
  
  /// 迷宫缩放动画
  late Animation<double> _mazeScaleAnimation;
  
  /// 指令添加动画
  late Animation<double> _commandAddAnimation;
  
  /// 机器人移动动画
  late Animation<double> _robotMoveAnimation;
  
  // =============================================================================
  // BaseGameWidgetState 抽象方法实现
  // =============================================================================
  
  @override
  void initializeGameSpecific() {
    // 迷宫动画
    _mazeAnimationController = AnimationController(
      duration: GameAnimations.normal,
      vsync: this,
    );
    
    // 指令动画
    _commandAnimationController = AnimationController(
      duration: GameAnimations.fast,
      vsync: this,
    );
    
    // 机器人移动动画
    _robotMoveController = AnimationController(
      duration: GameAnimations.normal,
      vsync: this,
    );
    
    // 指令执行动画
    _commandExecutionController = AnimationController(
      duration: GameAnimations.slow,
      vsync: this,
    );
    
    // 创建动画
    _mazeScaleAnimation = GameAnimations.scaleIn(_mazeAnimationController);
    _commandAddAnimation = GameAnimations.scaleIn(_commandAnimationController);
    _robotMoveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _robotMoveController,
      curve: Curves.easeInOut,
    ));
    
    // 开始动画
    _mazeAnimationController.forward();
    
    // 初始化游戏数据
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    _robotPosition = data['startPosition'] as int? ?? 0;
    _robotDirection = data['startDirection'] as int? ?? 0;
  }
  
  @override
  Widget buildGameContent(BuildContext context) {
    return Column(
      children: [
        // 迷宫显示区域
        _buildMazeArea(),
        
        SizedBox(height: UXThemeConfig.paddingL),
        
        // 指令序列区域
        _buildCommandSequenceArea(),
        
        SizedBox(height: UXThemeConfig.paddingL),
        
        // 可用指令区域
        _buildAvailableCommandsArea(),
        
        SizedBox(height: UXThemeConfig.paddingL),
        
        // 控制按钮区域
        _buildControlButtonsArea(),
      ],
    );
  }
  
  @override
  void disposeGameSpecific() {
    _mazeAnimationController.dispose();
    _commandAnimationController.dispose();
    _robotMoveController.dispose();
    _commandExecutionController.dispose();
  }
  
  // =============================================================================
  // 游戏UI构建方法
  // =============================================================================
  
  /// 构建迷宫区域
  Widget _buildMazeArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final maze = data['maze'] as List<dynamic>? ?? [];
    final targetPosition = data['targetPosition'] as int? ?? 15;
    
    return AnimatedBuilder(
      animation: _mazeScaleAnimation,
      builder: (context, child) {
        return ScaleTransition(
          scale: _mazeScaleAnimation,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(UXThemeConfig.paddingM),
              child: Column(
                children: [
                  // 游戏说明
                  _buildGameInstructions(),
                  
                  SizedBox(height: UXThemeConfig.paddingM),
                  
                  // 4x4迷宫网格
                  AspectRatio(
                    aspectRatio: 1.0,
                    child: GridView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        crossAxisSpacing: UXThemeConfig.paddingS,
                        mainAxisSpacing: UXThemeConfig.paddingS,
                      ),
                      itemCount: 16,
                      itemBuilder: (context, index) {
                        return _buildMazeCell(index, maze, targetPosition);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  /// 构建游戏说明
  Widget _buildGameInstructions() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(
          color: UXThemeConfig.primaryBlue.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: UXThemeConfig.primaryBlue,
            size: 20,
          ),
          SizedBox(width: UXThemeConfig.paddingS),
          Expanded(
            child: Text(
              '编程机器人到达目标位置，避开障碍物',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: UXThemeConfig.primaryBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建迷宫单元格
  Widget _buildMazeCell(int index, List<dynamic> maze, int targetPosition) {
    final isWall = index < maze.length && maze[index] == 1;
    final isRobot = index == _robotPosition;
    final isTarget = index == targetPosition;
    final isPath = !isWall && !isRobot && !isTarget;
    
    return AnimatedBuilder(
      animation: _robotMoveAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: isWall
                ? Colors.grey[800]
                : isTarget
                    ? Colors.green[400]
                    : isPath
                        ? Colors.grey[100]
                        : Colors.transparent,
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Center(
            child: _buildCellContent(isWall, isRobot, isTarget),
          ),
        );
      },
    );
  }
  
  /// 构建单元格内容
  Widget _buildCellContent(bool isWall, bool isRobot, bool isTarget) {
    if (isWall) {
      return Icon(
        Icons.crop_square,
        color: Colors.grey[600],
        size: 24,
      );
    } else if (isRobot) {
      return AnimatedBuilder(
        animation: _robotMoveAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: 1.0 + (_robotMoveAnimation.value * 0.2),
            child: _buildRobotIcon(),
          );
        },
      );
    } else if (isTarget) {
      return Icon(
        Icons.star,
        color: Colors.white,
        size: 24,
      );
    } else {
      return Container();
    }
  }
  
  /// 构建机器人图标
  Widget _buildRobotIcon() {
    // 尝试使用素材，如果失败则使用图标
    try {
      return Image.asset(
        GameAssets.codingCharacter('idle'),
        width: 28,
        height: 28,
        errorBuilder: (context, error, stackTrace) {
          return Transform.rotate(
            angle: _robotDirection * 1.5708, // 90度 * 方向
            child: Icon(
              Icons.smart_toy,
              color: UXThemeConfig.primaryBlue,
              size: 24,
            ),
          );
        },
      );
    } catch (e) {
      return Transform.rotate(
        angle: _robotDirection * 1.5708, // 90度 * 方向
        child: Icon(
          Icons.smart_toy,
          color: UXThemeConfig.primaryBlue,
          size: 24,
        ),
      );
    }
  }
  
  /// 构建指令序列区域
  Widget _buildCommandSequenceArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final maxCommands = data['maxCommands'] as int? ?? 10;
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingM),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '指令序列 (${_currentCommands.length}/$maxCommands)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: UXThemeConfig.textDark,
                  ),
                ),
                if (isShowingHint)
                  AnimatedBuilder(
                    animation: _commandExecutionController,
                    builder: (context, child) {
                      return Opacity(
                        opacity: 0.5 + (_commandExecutionController.value * 0.5),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: UXThemeConfig.paddingS,
                            vertical: UXThemeConfig.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.yellow[600],
                            borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                          ),
                          child: Text(
                            '提示',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
            
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 指令序列显示区域
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(minHeight: 60),
              padding: EdgeInsets.all(UXThemeConfig.paddingM),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: _currentCommands.isEmpty
                  ? Center(
                      child: Text(
                        '点击下方指令添加到序列',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                      ),
                    )
                  : Wrap(
                      spacing: UXThemeConfig.paddingS,
                      runSpacing: UXThemeConfig.paddingS,
                      children: _currentCommands.asMap().entries.map((entry) {
                        final index = entry.key;
                        final command = entry.value;
                        return _buildCommandChip(command, index);
                      }).toList(),
                    ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建指令芯片
  Widget _buildCommandChip(String command, int index) {
    return AnimatedBuilder(
      animation: _commandAddAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: !_isRunning ? () => _removeCommand(index) : null,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: UXThemeConfig.paddingM,
              vertical: UXThemeConfig.paddingS,
            ),
            decoration: BoxDecoration(
              color: _getCommandColor(command),
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildCommandIcon(command),
                SizedBox(width: UXThemeConfig.paddingXS),
                Text(
                  _getCommandDisplayName(command),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: UXThemeConfig.paddingXS),
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.grey,
                    size: 12,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// 构建可用指令区域
  Widget _buildAvailableCommandsArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final availableCommands = data['availableCommands'] as List<dynamic>? ?? 
        ['forward', 'turn_left', 'turn_right', 'jump'];
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingM),
        child: Column(
          children: [
            Text(
              '可用指令',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: UXThemeConfig.textDark,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            Wrap(
              spacing: UXThemeConfig.paddingM,
              runSpacing: UXThemeConfig.paddingM,
              children: availableCommands.map((command) {
                return _buildAvailableCommandButton(command as String);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建可用指令按钮
  Widget _buildAvailableCommandButton(String command) {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final maxCommands = data['maxCommands'] as int? ?? 10;
    final canAdd = _currentCommands.length < maxCommands && !_isRunning;

    return GestureDetector(
      onTap: canAdd ? () => _addCommand(command) : null,
      child: AnimatedContainer(
        duration: GameAnimations.fast,
        padding: EdgeInsets.symmetric(
          horizontal: UXThemeConfig.paddingM,
          vertical: UXThemeConfig.paddingM,
        ),
        decoration: BoxDecoration(
          color: canAdd ? _getCommandColor(command) : Colors.grey[300],
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          boxShadow: canAdd
              ? [
                  BoxShadow(
                    color: _getCommandColor(command).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCommandIcon(command),
            SizedBox(width: UXThemeConfig.paddingS),
            Text(
              _getCommandDisplayName(command),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建控制按钮区域
  Widget _buildControlButtonsArea() {
    return Row(
      children: [
        // 清空按钮
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _currentCommands.isNotEmpty && !_isRunning
                ? _clearCommands
                : null,
            icon: const Icon(Icons.clear_all),
            label: const Text('清空'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[600],
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: UXThemeConfig.paddingM),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
              ),
            ),
          ),
        ),
        
        SizedBox(width: UXThemeConfig.paddingM),
        
        // 运行按钮
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: _currentCommands.isNotEmpty && !_isRunning
                ? _runCommands
                : null,
            icon: _isRunning
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(_isRunning ? '运行中...' : '运行'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: UXThemeConfig.paddingM),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  // =============================================================================
  // 游戏逻辑方法
  // =============================================================================
  
  /// 添加指令
  void _addCommand(String command) {
    if (isPaused || isShowingFeedback || _isRunning) return;
    
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final maxCommands = data['maxCommands'] as int? ?? 10;
    
    if (_currentCommands.length >= maxCommands) return;
    
    setState(() {
      _currentCommands.add(command);
    });
    
    // 播放添加动画
    _commandAnimationController.forward().then((_) {
      _commandAnimationController.reverse();
    });
  }
  
  /// 移除指令
  void _removeCommand(int index) {
    if (isPaused || isShowingFeedback || _isRunning) return;
    
    if (index >= 0 && index < _currentCommands.length) {
      setState(() {
        _currentCommands.removeAt(index);
      });
    }
  }
  
  /// 清空指令
  void _clearCommands() {
    if (isPaused || isShowingFeedback || _isRunning) return;
    
    setState(() {
      _currentCommands.clear();
    });
  }
  
  /// 运行指令序列
  void _runCommands() async {
    if (isPaused || isShowingFeedback || _isRunning || _currentCommands.isEmpty) return;
    
    setState(() {
      _isRunning = true;
    });
    
    // 重置机器人位置
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    _robotPosition = data['startPosition'] as int? ?? 0;
    _robotDirection = data['startDirection'] as int? ?? 0;
    
    // 执行每个指令
    for (int i = 0; i < _currentCommands.length; i++) {
      if (!_isRunning) break; // 如果被中断就停止
      
      await _executeCommand(_currentCommands[i]);
      await Future.delayed(const Duration(milliseconds: 500));
    }
    
    // 检查是否到达目标
    final targetPosition = data['targetPosition'] as int? ?? 15;
    final isSuccess = _robotPosition == targetPosition;
    
    setState(() {
      _isRunning = false;
    });
    
    // 提交结果
    handleAnswerSubmission({
      'commands': _currentCommands,
      'finalPosition': _robotPosition,
      'isSuccess': isSuccess,
    });
  }
  
  /// 执行单个指令
  Future<void> _executeCommand(String command) async {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final maze = data['maze'] as List<dynamic>? ?? [];
    
    switch (command.toLowerCase()) {
      case 'forward':
        await _moveForward(maze);
        break;
      case 'turn_left':
        _turnLeft();
        break;
      case 'turn_right':
        _turnRight();
        break;
      case 'jump':
        await _jump(maze);
        break;
      case 'wait':
        await Future.delayed(const Duration(milliseconds: 500));
        break;
    }
  }
  
  /// 向前移动
  Future<void> _moveForward(List<dynamic> maze) async {
    final newPosition = _getNextPosition();
    
    if (newPosition != -1 && newPosition < 16 && 
        (newPosition >= maze.length || maze[newPosition] != 1)) {
      _robotMoveController.forward();
      await Future.delayed(const Duration(milliseconds: 300));
      
      setState(() {
        _robotPosition = newPosition;
      });
      
      _robotMoveController.reverse();
    }
  }
  
  /// 向左转
  void _turnLeft() {
    setState(() {
      _robotDirection = (_robotDirection - 1 + 4) % 4;
    });
  }
  
  /// 向右转
  void _turnRight() {
    setState(() {
      _robotDirection = (_robotDirection + 1) % 4;
    });
  }
  
  /// 跳跃
  Future<void> _jump(List<dynamic> maze) async {
    final jumpPosition = _getJumpPosition();
    
    if (jumpPosition != -1 && jumpPosition < 16 && 
        (jumpPosition >= maze.length || maze[jumpPosition] != 1)) {
      _robotMoveController.forward();
      await Future.delayed(const Duration(milliseconds: 300));
      
      setState(() {
        _robotPosition = jumpPosition;
      });
      
      _robotMoveController.reverse();
    }
  }
  
  /// 获取下一个位置
  int _getNextPosition() {
    final row = _robotPosition ~/ 4;
    final col = _robotPosition % 4;
    
    switch (_robotDirection) {
      case 0: // 上
        return row > 0 ? (row - 1) * 4 + col : -1;
      case 1: // 右
        return col < 3 ? row * 4 + (col + 1) : -1;
      case 2: // 下
        return row < 3 ? (row + 1) * 4 + col : -1;
      case 3: // 左
        return col > 0 ? row * 4 + (col - 1) : -1;
      default:
        return -1;
    }
  }
  
  /// 获取跳跃位置
  int _getJumpPosition() {
    final row = _robotPosition ~/ 4;
    final col = _robotPosition % 4;
    
    switch (_robotDirection) {
      case 0: // 上
        return row > 1 ? (row - 2) * 4 + col : -1;
      case 1: // 右
        return col < 2 ? row * 4 + (col + 2) : -1;
      case 2: // 下
        return row < 2 ? (row + 2) * 4 + col : -1;
      case 3: // 左
        return col > 1 ? row * 4 + (col - 2) : -1;
      default:
        return -1;
    }
  }
  
  // =============================================================================
  // 素材管理方法
  // =============================================================================
  
  /// 获取指令颜色
  Color _getCommandColor(String command) {
    switch (command.toLowerCase()) {
      case 'forward':
        return const Color(0xFF2196F3); // 蓝色
      case 'turn_left':
        return const Color(0xFFFF9800); // 橙色
      case 'turn_right':
        return const Color(0xFF9C27B0); // 紫色
      case 'jump':
        return const Color(0xFF4CAF50); // 绿色
      case 'wait':
        return const Color(0xFF607D8B); // 蓝灰色
      default:
        return Colors.grey;
    }
  }

  /// 构建指令图标
  Widget _buildCommandIcon(String command) {
    // 尝试使用 GameAssets 加载图片，如果失败则使用图标
    try {
      return Image.asset(
        GameAssets.codingCommand(command),
        width: 16,
        height: 16,
        color: Colors.white,
        errorBuilder: (context, error, stackTrace) {
          return Icon(
            _getCommandIcon(command),
            color: Colors.white,
            size: 16,
          );
        },
      );
    } catch (e) {
      return Icon(
        _getCommandIcon(command),
        color: Colors.white,
        size: 16,
      );
    }
  }

  /// 获取指令图标
  IconData _getCommandIcon(String command) {
    switch (command.toLowerCase()) {
      case 'forward':
        return Icons.arrow_upward;
      case 'turn_left':
        return Icons.turn_left;
      case 'turn_right':
        return Icons.turn_right;
      case 'jump':
        return Icons.height;
      case 'wait':
        return Icons.pause;
      default:
        return Icons.help;
    }
  }

  /// 获取指令显示名称
  String _getCommandDisplayName(String command) {
    switch (command.toLowerCase()) {
      case 'forward':
        return '前进';
      case 'turn_left':
        return '左转';
      case 'turn_right':
        return '右转';
      case 'jump':
        return '跳跃';
      case 'wait':
        return '等待';
      default:
        return command;
    }
  }
} 
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../core/service_locator.dart';
import '../../services/theme_service.dart';

/// 动画主题包装器
///
/// 为主题切换提供平滑的过渡动画效果
/// 支持多种动画类型和自定义配置
/// 更新时间：2024-12-19 UTC
class AnimatedThemeWrapper extends StatefulWidget {
  final Widget child;
  final Duration? animationDuration;
  final Curve? animationCurve;
  final AnimationType animationType;

  const AnimatedThemeWrapper({
    super.key,
    required this.child,
    this.animationDuration,
    this.animationCurve,
    this.animationType = AnimationType.fade,
  });

  @override
  State<AnimatedThemeWrapper> createState() => _AnimatedThemeWrapperState();
}

class _AnimatedThemeWrapperState extends State<AnimatedThemeWrapper>
    with TickerProviderStateMixin {
  final Logger _logger = Logger();
  late ThemeService _themeService;
  late AnimationController _animationController;
  late Animation<double> _animation;

  ThemeData? _previousTheme;
  ThemeData? _currentTheme;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _themeService = sl<ThemeService>();
    _themeService.addListener(_onThemeChanged);

    _animationController = AnimationController(
      duration: widget.animationDuration ?? const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve ?? Curves.easeInOut,
    );

    // 初始化当前主题
    _currentTheme = _getCurrentTheme();
    _previousTheme = _currentTheme;
  }

  @override
  void dispose() {
    _themeService.removeListener(_onThemeChanged);
    _animationController.dispose();
    super.dispose();
  }

  /// 获取当前主题
  ThemeData _getCurrentTheme() {
    final brightness = MediaQuery.of(context).platformBrightness;
    final isDark =
        _themeService.themeMode == ThemeMode.dark ||
        (_themeService.themeMode == ThemeMode.system &&
            brightness == Brightness.dark);

    return isDark
        ? _themeService.getDarkTheme()
        : _themeService.getLightTheme();
  }

  /// 主题变化回调
  void _onThemeChanged() {
    if (!mounted) return;

    final newTheme = _getCurrentTheme();
    if (_currentTheme != newTheme) {
      _startThemeTransition(newTheme);
    }
  }

  /// 开始主题过渡动画
  void _startThemeTransition(ThemeData newTheme) {
    if (!_themeService.animatedTransitions) {
      // 如果禁用动画，直接切换
      setState(() {
        _previousTheme = _currentTheme;
        _currentTheme = newTheme;
      });
      return;
    }

    setState(() {
      _previousTheme = _currentTheme;
      _currentTheme = newTheme;
      _isAnimating = true;
    });

    _animationController.reset();
    _animationController.forward().then((_) {
      if (mounted) {
        setState(() {
          _isAnimating = false;
          _previousTheme = _currentTheme;
        });
      }
    });

    _logger.d('Started theme transition animation');
  }

  @override
  Widget build(BuildContext context) {
    if (!_isAnimating || _previousTheme == null || _currentTheme == null) {
      return Theme(
        data: _currentTheme ?? Theme.of(context),
        child: widget.child,
      );
    }

    return _buildAnimatedTransition();
  }

  /// 构建动画过渡效果
  Widget _buildAnimatedTransition() {
    switch (widget.animationType) {
      case AnimationType.fade:
        return _buildFadeTransition();
      case AnimationType.slide:
        return _buildSlideTransition();
      case AnimationType.scale:
        return _buildScaleTransition();
      case AnimationType.rotation:
        return _buildRotationTransition();
      case AnimationType.colorBlend:
        return _buildColorBlendTransition();
    }
  }

  /// 淡入淡出过渡
  Widget _buildFadeTransition() {
    return Stack(
      children: [
        // 旧主题（淡出）
        FadeTransition(
          opacity: Tween<double>(begin: 1.0, end: 0.0).animate(_animation),
          child: Theme(data: _previousTheme!, child: widget.child),
        ),
        // 新主题（淡入）
        FadeTransition(
          opacity: _animation,
          child: Theme(data: _currentTheme!, child: widget.child),
        ),
      ],
    );
  }

  /// 滑动过渡
  Widget _buildSlideTransition() {
    return Stack(
      children: [
        // 旧主题（向左滑出）
        SlideTransition(
          position: Tween<Offset>(
            begin: Offset.zero,
            end: const Offset(-1.0, 0.0),
          ).animate(_animation),
          child: Theme(data: _previousTheme!, child: widget.child),
        ),
        // 新主题（从右滑入）
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(_animation),
          child: Theme(data: _currentTheme!, child: widget.child),
        ),
      ],
    );
  }

  /// 缩放过渡
  Widget _buildScaleTransition() {
    return Stack(
      children: [
        // 旧主题（缩小）
        ScaleTransition(
          scale: Tween<double>(begin: 1.0, end: 0.8).animate(_animation),
          child: FadeTransition(
            opacity: Tween<double>(begin: 1.0, end: 0.0).animate(_animation),
            child: Theme(data: _previousTheme!, child: widget.child),
          ),
        ),
        // 新主题（放大）
        ScaleTransition(
          scale: Tween<double>(begin: 0.8, end: 1.0).animate(_animation),
          child: FadeTransition(
            opacity: _animation,
            child: Theme(data: _currentTheme!, child: widget.child),
          ),
        ),
      ],
    );
  }

  /// 旋转过渡
  Widget _buildRotationTransition() {
    return Stack(
      children: [
        // 旧主题（旋转淡出）
        RotationTransition(
          turns: Tween<double>(begin: 0.0, end: 0.25).animate(_animation),
          child: FadeTransition(
            opacity: Tween<double>(begin: 1.0, end: 0.0).animate(_animation),
            child: Theme(data: _previousTheme!, child: widget.child),
          ),
        ),
        // 新主题（反向旋转淡入）
        RotationTransition(
          turns: Tween<double>(begin: -0.25, end: 0.0).animate(_animation),
          child: FadeTransition(
            opacity: _animation,
            child: Theme(data: _currentTheme!, child: widget.child),
          ),
        ),
      ],
    );
  }

  /// 颜色混合过渡
  Widget _buildColorBlendTransition() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        // 混合两个主题的颜色
        final blendedTheme = _blendThemes(
          _previousTheme!,
          _currentTheme!,
          _animation.value,
        );

        return Theme(data: blendedTheme, child: widget.child);
      },
    );
  }

  /// 混合两个主题的颜色
  ThemeData _blendThemes(ThemeData from, ThemeData to, double t) {
    return ThemeData(
      brightness: to.brightness,

      // 混合颜色方案
      colorScheme: ColorScheme.lerp(from.colorScheme, to.colorScheme, t),

      // 混合应用栏主题
      appBarTheme: AppBarTheme.lerp(from.appBarTheme, to.appBarTheme, t),

      // 混合按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom().copyWith(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            final fromColor = from.elevatedButtonTheme.style?.backgroundColor
                ?.resolve(states);
            final toColor = to.elevatedButtonTheme.style?.backgroundColor
                ?.resolve(states);
            if (fromColor != null && toColor != null) {
              return Color.lerp(fromColor, toColor, t);
            }
            return toColor;
          }),
        ),
      ),

      // 混合卡片主题
      cardTheme: CardThemeData.lerp(from.cardTheme, to.cardTheme, t),

      // 混合文本主题
      textTheme: TextTheme.lerp(from.textTheme, to.textTheme, t),

      // 其他属性使用目标主题
      scaffoldBackgroundColor: Color.lerp(
        from.scaffoldBackgroundColor,
        to.scaffoldBackgroundColor,
        t,
      ),
      canvasColor: Color.lerp(from.canvasColor, to.canvasColor, t),
      cardColor: Color.lerp(from.cardColor, to.cardColor, t),
      dividerColor: Color.lerp(from.dividerColor, to.dividerColor, t),
      highlightColor: Color.lerp(from.highlightColor, to.highlightColor, t),
      splashColor: Color.lerp(from.splashColor, to.splashColor, t),
      // selectedRowColor已弃用，移除此属性
      unselectedWidgetColor: Color.lerp(
        from.unselectedWidgetColor,
        to.unselectedWidgetColor,
        t,
      ),
      disabledColor: Color.lerp(from.disabledColor, to.disabledColor, t),
      secondaryHeaderColor: Color.lerp(
        from.secondaryHeaderColor,
        to.secondaryHeaderColor,
        t,
      ),
      // dialogBackgroundColor和indicatorColor已弃用，移除这些属性
      hintColor: Color.lerp(from.hintColor, to.hintColor, t),
      shadowColor: Color.lerp(from.shadowColor, to.shadowColor, t),
      focusColor: Color.lerp(from.focusColor, to.focusColor, t),
      hoverColor: Color.lerp(from.hoverColor, to.hoverColor, t),
    );
  }
}

/// 动画类型枚举
enum AnimationType {
  /// 淡入淡出
  fade,

  /// 滑动过渡
  slide,

  /// 缩放过渡
  scale,

  /// 旋转过渡
  rotation,

  /// 颜色混合
  colorBlend,
}

/// 主题动画配置
class ThemeAnimationConfig {
  final Duration duration;
  final Curve curve;
  final AnimationType type;
  final bool enabled;

  const ThemeAnimationConfig({
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.type = AnimationType.fade,
    this.enabled = true,
  });

  /// 快速配置
  static const ThemeAnimationConfig fast = ThemeAnimationConfig(
    duration: Duration(milliseconds: 150),
    curve: Curves.easeOut,
    type: AnimationType.fade,
  );

  /// 慢速配置
  static const ThemeAnimationConfig slow = ThemeAnimationConfig(
    duration: Duration(milliseconds: 600),
    curve: Curves.easeInOut,
    type: AnimationType.colorBlend,
  );

  /// 弹性配置
  static const ThemeAnimationConfig bouncy = ThemeAnimationConfig(
    duration: Duration(milliseconds: 400),
    curve: Curves.elasticOut,
    type: AnimationType.scale,
  );

  /// 禁用动画
  static const ThemeAnimationConfig disabled = ThemeAnimationConfig(
    duration: Duration.zero,
    enabled: false,
  );
}

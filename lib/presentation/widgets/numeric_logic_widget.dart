import 'package:flutter/material.dart';
import '../../data/models/puzzle.dart';

class NumericLogicWidget extends StatelessWidget {
  final NumericLogicData puzzleData;
  final List<String?> currentGrid;
  final String? selectedItem;
  final Function(int)? onGridTap;
  final Function(String)? onItemSelected;
  final bool showHint;
  final int? highlightIndex;

  const NumericLogicWidget({
    super.key,
    required this.puzzleData,
    required this.currentGrid,
    this.selectedItem,
    this.onGridTap,
    this.onItemSelected,
    this.showHint = false,
    this.highlightIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 游戏规则说明
        _buildRulesCard(),
        
        const SizedBox(height: 16),
        
        // 4x4游戏网格
        _buildGameGrid(),
        
        const SizedBox(height: 16),
        
        // 可用图标选择器
        _buildItemSelector(),
      ],
    );
  }

  Widget _buildRulesCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: const Color(0xFF6C5CE7),
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '每行、每列、每个2×2宫格内，四种图标都只能出现一次',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF6C5CE7),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameGrid() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: Stack(
            children: [
              // 4x4网格
              GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 2,
                ),
                itemCount: 16,
                itemBuilder: (context, index) {
                  return _buildGridCell(context, index);
                },
              ),
              // 2x2宫格分割线
              _buildSubgridLines(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubgridLines() {
    return CustomPaint(
      painter: SubgridLinesPainter(),
      child: Container(),
    );
  }

  Widget _buildGridCell(BuildContext context, int index) {
    final isEmptyCell = currentGrid[index] == null;
    final cellContent = currentGrid[index];
    final isHighlighted = highlightIndex == index;
    final hasConflict = _hasConflict(index);

    return GestureDetector(
      onTap: isEmptyCell ? () => onGridTap?.call(index) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: hasConflict
              ? Colors.red[50]
              : isHighlighted
                  ? Colors.yellow[100]
                  : isEmptyCell
                      ? (selectedItem != null
                          ? const Color(0xFF6C5CE7).withValues(alpha: 0.1)
                          : Colors.grey[50])
                      : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: hasConflict
                ? Colors.red[300]!
                : isHighlighted
                    ? Colors.yellow[600]!
                    : isEmptyCell
                        ? (selectedItem != null
                            ? const Color(0xFF6C5CE7)
                            : Colors.grey[300]!)
                        : Colors.grey[200]!,
            width: isHighlighted ? 3 : (isEmptyCell && selectedItem != null ? 2 : 1),
          ),
          boxShadow: [
            if (!isEmptyCell || isHighlighted)
              BoxShadow(
                color: hasConflict
                    ? Colors.red.withValues(alpha: 0.2)
                    : isHighlighted
                        ? Colors.yellow.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: isEmptyCell ? _buildEmptyCell(index) : _buildContentCell(cellContent!),
      ),
    );
  }

  Widget _buildEmptyCell(int index) {
    return Container(
      child: selectedItem != null
          ? Stack(
              children: [
                Center(
                  child: _buildItemIcon(selectedItem!, isPreview: true),
                ),
                if (showHint && highlightIndex == index)
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.yellow[600],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.lightbulb,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                  ),
              ],
            )
          : Center(
              child: Icon(
                Icons.add,
                color: Colors.grey[400],
                size: 20,
              ),
            ),
    );
  }

  Widget _buildContentCell(String content) {
    return Center(
      child: _buildItemIcon(content),
    );
  }

  Widget _buildItemIcon(String itemId, {bool isPreview = false}) {
    final size = isPreview ? 24.0 : 32.0;
    final opacity = isPreview ? 0.6 : 1.0;

    return Opacity(
      opacity: opacity,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: _getItemColor(itemId),
          shape: _getItemShape(itemId),
          borderRadius: _getItemShape(itemId) == BoxShape.rectangle
              ? BorderRadius.circular(6)
              : null,
        ),
        child: _getItemIcon(itemId, size),
      ),
    );
  }

  Widget _buildItemSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              '选择图标',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: puzzleData.availableItems.map((item) {
                return _buildItemSelectorButton(item);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemSelectorButton(String item) {
    final isSelected = selectedItem == item;
    final remainingCount = _getRemainingCount(item);

    return GestureDetector(
      onTap: remainingCount > 0 ? () => onItemSelected?.call(item) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: remainingCount == 0
              ? Colors.grey[100]
              : isSelected
                  ? const Color(0xFF6C5CE7).withValues(alpha: 0.2)
                  : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: remainingCount == 0
                ? Colors.grey[300]!
                : isSelected
                    ? const Color(0xFF6C5CE7)
                    : Colors.grey[300]!,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: _buildItemIcon(item),
            ),
            // 剩余数量指示
            Positioned(
              bottom: 4,
              right: 4,
              child: Container(
                width: 18,
                height: 18,
                decoration: BoxDecoration(
                  color: remainingCount == 0 ? Colors.grey[400] : const Color(0xFF6C5CE7),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    remainingCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 获取图标颜色
  Color _getItemColor(String itemId) {
    switch (itemId.toLowerCase()) {
      case 'apple':
        return Colors.red[400]!;
      case 'banana':
        return Colors.yellow[600]!;
      case 'grape':
        return Colors.purple[400]!;
      case 'orange':
        return Colors.orange[400]!;
      case 'strawberry':
        return Colors.pink[400]!;
      case 'lemon':
        return Colors.yellow[400]!;
      case 'cherry':
        return Colors.red[600]!;
      case 'blueberry':
        return Colors.blue[400]!;
      default:
        return Colors.grey[400]!;
    }
  }

  // 获取图标形状
  BoxShape _getItemShape(String itemId) {
    switch (itemId.toLowerCase()) {
      case 'apple':
      case 'orange':
      case 'cherry':
      case 'blueberry':
        return BoxShape.circle;
      default:
        return BoxShape.rectangle;
    }
  }

  // 获取图标内容
  Widget? _getItemIcon(String itemId, double size) {
    final iconSize = size * 0.6;
    final color = Colors.white;

    switch (itemId.toLowerCase()) {
      case 'apple':
        return Icon(Icons.apple, color: color, size: iconSize);
      case 'banana':
        return Icon(Icons.sports_volleyball, color: color, size: iconSize);
      case 'grape':
        return Icon(Icons.circle, color: color, size: iconSize);
      case 'orange':
        return Icon(Icons.circle_outlined, color: color, size: iconSize);
      case 'strawberry':
        return Icon(Icons.favorite, color: color, size: iconSize);
      case 'lemon':
        return Icon(Icons.star, color: color, size: iconSize);
      case 'cherry':
        return Icon(Icons.brightness_1, color: color, size: iconSize);
      case 'blueberry':
        return Icon(Icons.fiber_manual_record, color: color, size: iconSize);
      default:
        return Icon(Icons.help, color: color, size: iconSize);
    }
  }

  // 获取剩余可用数量
  int _getRemainingCount(String item) {
    final usedCount = currentGrid.where((cell) => cell == item).length;
    return 4 - usedCount; // 每种图标最多使用4次（4x4网格）
  }

  // 检查是否有冲突
  bool _hasConflict(int index) {
    final item = currentGrid[index];
    if (item == null) return false;

    final row = index ~/ 4;
    final col = index % 4;

    // 检查行冲突
    for (int c = 0; c < 4; c++) {
      if (c != col && currentGrid[row * 4 + c] == item) {
        return true;
      }
    }

    // 检查列冲突
    for (int r = 0; r < 4; r++) {
      if (r != row && currentGrid[r * 4 + col] == item) {
        return true;
      }
    }

    // 检查2x2宫格冲突
    final subgridRow = (row ~/ 2) * 2;
    final subgridCol = (col ~/ 2) * 2;
    
    for (int r = subgridRow; r < subgridRow + 2; r++) {
      for (int c = subgridCol; c < subgridCol + 2; c++) {
        if ((r != row || c != col) && currentGrid[r * 4 + c] == item) {
          return true;
        }
      }
    }

    return false;
  }
}

/// 2x2宫格分割线绘制器
class SubgridLinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF6C5CE7)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final cellSize = size.width / 4;

    // 垂直分割线（在第2列后）
    final verticalLine = Offset(cellSize * 2, 0);
    final verticalLineEnd = Offset(cellSize * 2, size.height);
    canvas.drawLine(verticalLine, verticalLineEnd, paint);

    // 水平分割线（在第2行后）
    final horizontalLine = Offset(0, cellSize * 2);
    final horizontalLineEnd = Offset(size.width, cellSize * 2);
    canvas.drawLine(horizontalLine, horizontalLineEnd, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 
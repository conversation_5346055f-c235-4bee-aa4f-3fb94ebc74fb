import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../core/constants/game_assets.dart';
import '../../core/utils/game_animations.dart';
import 'game_core/base_game_widget.dart';

/// 数字逻辑游戏组件
///
/// 基于新架构的数字逻辑游戏实现，包含：
/// - 4x4数独类逻辑推理
/// - 类型安全的素材管理
/// - 统一的动画系统
/// - 通用的游戏基类
class NumericLogicWidget extends BaseGameWidget {
  const NumericLogicWidget({
    super.key,
    required super.puzzleData,
    required super.onAnswerSubmitted,
    super.onHintRequested,
    super.onRestart,
    super.onPause,
  });

  @override
  State<NumericLogicWidget> createState() => _NumericLogicWidgetState();
}

class _NumericLogicWidgetState extends BaseGameWidgetState<NumericLogicWidget> {
  
  // =============================================================================
  // 游戏特定状态
  // =============================================================================
  
  /// 当前网格状态 (16个位置)
  final List<String?> _currentGrid = List.filled(16, null);
  
  /// 当前选中的物品
  String? _selectedItem;
  
  /// 网格动画控制器
  late AnimationController _gridAnimationController;
  
  /// 物品选择动画控制器
  late AnimationController _itemAnimationController;
  
  /// 提示动画控制器
  late AnimationController _hintAnimationController;
  
  /// 网格缩放动画
  late Animation<double> _gridScaleAnimation;
  
  /// 物品选择脉冲动画
  late Animation<double> _itemPulseAnimation;
  
  /// 提示闪烁动画
  late Animation<double> _hintBlinkAnimation;
  
  // =============================================================================
  // BaseGameWidgetState 抽象方法实现
  // =============================================================================
  
  @override
  void initializeGameSpecific() {
    // 网格动画
    _gridAnimationController = AnimationController(
      duration: GameAnimations.normal,
      vsync: this,
    );
    
    // 物品选择动画
    _itemAnimationController = AnimationController(
      duration: GameAnimations.fast,
      vsync: this,
    );
    
    // 提示动画
    _hintAnimationController = AnimationController(
      duration: GameAnimations.slow,
      vsync: this,
    );
    
    // 创建动画
    _gridScaleAnimation = GameAnimations.scaleIn(_gridAnimationController);
    _itemPulseAnimation = GameAnimations.pulse(_itemAnimationController);
    _hintBlinkAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hintAnimationController,
      curve: Curves.easeInOut,
    ));
    
    // 开始动画
    _gridAnimationController.forward();
    
    // 循环播放提示动画
    _hintAnimationController.repeat(reverse: true);
    
    // 初始化游戏数据
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final initialGrid = data['initialGrid'] as List<dynamic>? ?? [];
    
    // 设置初始网格状态
    for (int i = 0; i < 16 && i < initialGrid.length; i++) {
      _currentGrid[i] = initialGrid[i] as String?;
    }
  }
  
  @override
  Widget buildGameContent(BuildContext context) {
    return Column(
      children: [
        // 游戏规则说明
        _buildRulesCard(),
        
        SizedBox(height: UXThemeConfig.paddingL),
        
        // 4x4游戏网格
        _buildGameGrid(),
        
        SizedBox(height: UXThemeConfig.paddingL),
        
        // 可用图标选择器
        _buildItemSelector(),
        
        SizedBox(height: UXThemeConfig.paddingL),
        
        // 提交按钮
        _buildSubmitButton(),
      ],
    );
  }
  
  @override
  void disposeGameSpecific() {
    _gridAnimationController.dispose();
    _itemAnimationController.dispose();
    _hintAnimationController.dispose();
  }
  
  // =============================================================================
  // 游戏UI构建方法
  // =============================================================================
  
  /// 构建游戏规则说明卡片
  Widget _buildRulesCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(
          color: UXThemeConfig.primaryBlue.withValues(alpha: 0.3),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingM),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: UXThemeConfig.primaryBlue,
              size: 20,
            ),
            SizedBox(width: UXThemeConfig.paddingS),
            Expanded(
              child: Text(
                '每行、每列、每个2×2宫格内，四种图标都只能出现一次',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: UXThemeConfig.primaryBlue,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建4x4游戏网格
  Widget _buildGameGrid() {
    return AnimatedBuilder(
      animation: _gridScaleAnimation,
      builder: (context, child) {
        return ScaleTransition(
          scale: _gridScaleAnimation,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(UXThemeConfig.paddingM),
              child: AspectRatio(
                aspectRatio: 1.0,
                child: Stack(
                  children: [
                    // 4x4网格
                    GridView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        crossAxisSpacing: 2,
                        mainAxisSpacing: 2,
                      ),
                      itemCount: 16,
                      itemBuilder: (context, index) {
                        return _buildGridCell(context, index);
                      },
                    ),
                    // 2x2宫格分割线
                    _buildSubgridLines(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  /// 构建2x2宫格分割线
  Widget _buildSubgridLines() {
    return CustomPaint(
      painter: SubgridLinesPainter(),
      child: Container(),
    );
  }
  
  /// 构建网格单元格
  Widget _buildGridCell(BuildContext context, int index) {
    final isEmptyCell = _currentGrid[index] == null;
    final cellContent = _currentGrid[index];
    final isHighlighted = isShowingHint && _getHintIndex() == index;
    final hasConflict = _hasConflict(index);
    final isInitialCell = _isInitialCell(index);

    return GestureDetector(
      onTap: (!isInitialCell && isEmptyCell) ? () => _onGridTap(index) : null,
      child: AnimatedContainer(
        duration: GameAnimations.fast,
        decoration: BoxDecoration(
          color: hasConflict
              ? Colors.red[50]
              : isHighlighted
                  ? Colors.yellow[100]
                  : isEmptyCell
                      ? (_selectedItem != null
                          ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1)
                          : Colors.grey[50])
                      : Colors.white,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
          border: Border.all(
            color: hasConflict
                ? Colors.red[300]!
                : isHighlighted
                    ? Colors.yellow[600]!
                    : isEmptyCell
                        ? (_selectedItem != null
                            ? UXThemeConfig.primaryBlue
                            : Colors.grey[300]!)
                        : Colors.grey[200]!,
            width: isHighlighted ? 3 : (isEmptyCell && _selectedItem != null ? 2 : 1),
          ),
          boxShadow: [
            if (!isEmptyCell || isHighlighted)
              BoxShadow(
                color: hasConflict
                    ? Colors.red.withValues(alpha: 0.2)
                    : isHighlighted
                        ? Colors.yellow.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: isEmptyCell ? _buildEmptyCell(index) : _buildContentCell(cellContent!, isInitialCell),
      ),
    );
  }
  
  /// 构建空单元格
  Widget _buildEmptyCell(int index) {
    final isHintCell = isShowingHint && _getHintIndex() == index;
    
    return Container(
      child: _selectedItem != null
          ? Stack(
              children: [
                Center(
                  child: _buildItemIcon(_selectedItem!, isPreview: true),
                ),
                if (isHintCell)
                  AnimatedBuilder(
                    animation: _hintBlinkAnimation,
                    builder: (context, child) {
                      return Positioned(
                        top: 4,
                        right: 4,
                        child: Opacity(
                          opacity: _hintBlinkAnimation.value,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Colors.yellow[600],
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.lightbulb,
                              color: Colors.white,
                              size: 10,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            )
          : Center(
              child: Icon(
                Icons.add,
                color: Colors.grey[400],
                size: 20,
              ),
            ),
    );
  }
  
  /// 构建内容单元格
  Widget _buildContentCell(String content, bool isInitial) {
    return Container(
      decoration: BoxDecoration(
        color: isInitial ? Colors.grey[100] : Colors.transparent,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
      ),
      child: Center(
        child: _buildItemIcon(content, isInitial: isInitial),
      ),
    );
  }
  
  /// 构建物品图标
  Widget _buildItemIcon(String itemId, {bool isPreview = false, bool isInitial = false}) {
    final size = isPreview ? 24.0 : 32.0;
    final opacity = isPreview ? 0.6 : (isInitial ? 0.8 : 1.0);

    return Opacity(
      opacity: opacity,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: _getItemColor(itemId),
          shape: _getItemShape(itemId),
          borderRadius: _getItemShape(itemId) == BoxShape.rectangle
              ? BorderRadius.circular(6)
              : null,
        ),
        child: _getItemIcon(itemId, size),
      ),
    );
  }
  
  /// 构建物品选择器
  Widget _buildItemSelector() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final availableItems = data['availableItems'] as List<dynamic>? ?? ['apple', 'banana', 'grape', 'orange'];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingM),
        child: Column(
          children: [
            Text(
              '选择图标',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: UXThemeConfig.textDark,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: availableItems.map((item) {
                return _buildItemSelectorButton(item as String);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建物品选择按钮
  Widget _buildItemSelectorButton(String item) {
    final isSelected = _selectedItem == item;
    final remainingCount = _getRemainingCount(item);

    return GestureDetector(
      onTap: remainingCount > 0 ? () => _onItemSelected(item) : null,
      child: AnimatedBuilder(
        animation: _itemPulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: isSelected ? _itemPulseAnimation.value : 1.0,
            child: AnimatedContainer(
              duration: GameAnimations.fast,
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: remainingCount == 0
                    ? Colors.grey[100]
                    : isSelected
                        ? UXThemeConfig.primaryBlue.withValues(alpha: 0.2)
                        : Colors.grey[50],
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                border: Border.all(
                  color: remainingCount == 0
                      ? Colors.grey[300]!
                      : isSelected
                          ? UXThemeConfig.primaryBlue
                          : Colors.grey[300]!,
                  width: isSelected ? 3 : 1,
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: _buildItemIcon(item),
                  ),
                  // 剩余数量指示
                  Positioned(
                    bottom: 4,
                    right: 4,
                    child: Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: remainingCount == 0 ? Colors.grey[400] : UXThemeConfig.primaryBlue,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          remainingCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// 构建提交按钮
  Widget _buildSubmitButton() {
    final isGridComplete = _isGridComplete();
    
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: isGridComplete ? _submitAnswer : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isGridComplete ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          ),
          elevation: isGridComplete ? 4 : 0,
        ),
        child: Text(
          isGridComplete ? '提交答案' : '请完成所有格子',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
  
  // =============================================================================
  // 游戏逻辑方法
  // =============================================================================
  
  /// 网格点击处理
  void _onGridTap(int index) {
    if (isPaused || isShowingFeedback || _selectedItem == null) return;
    
    setState(() {
      _currentGrid[index] = _selectedItem;
    });
    
    // 播放放置动画
    _itemAnimationController.forward().then((_) {
      _itemAnimationController.reverse();
    });
  }
  
  /// 物品选择处理
  void _onItemSelected(String item) {
    if (isPaused || isShowingFeedback) return;
    
    setState(() {
      _selectedItem = _selectedItem == item ? null : item;
    });
    
    // 播放选择动画
    if (_selectedItem == item) {
      _itemAnimationController.forward().then((_) {
        _itemAnimationController.reverse();
      });
    }
  }
  
  /// 提交答案
  void _submitAnswer() {
    if (!_isGridComplete() || isPaused || isShowingFeedback) return;
    
    // 验证答案
    final isCorrect = _validateGrid();
    
    // 调用父类的答案提交处理
    handleAnswerSubmission({
      'grid': _currentGrid,
      'isCorrect': isCorrect,
    });
  }
  
  /// 获取提示位置
  int _getHintIndex() {
    // 找到第一个空位置
    for (int i = 0; i < 16; i++) {
      if (_currentGrid[i] == null && !_isInitialCell(i)) {
        return i;
      }
    }
    return -1;
  }
  
  /// 检查是否为初始单元格
  bool _isInitialCell(int index) {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final initialGrid = data['initialGrid'] as List<dynamic>? ?? [];
    
    if (index < initialGrid.length) {
      return initialGrid[index] != null;
    }
    return false;
  }
  
  /// 检查网格是否完成
  bool _isGridComplete() {
    return _currentGrid.every((cell) => cell != null);
  }
  
  /// 验证网格是否正确
  bool _validateGrid() {
    // 检查所有行
    for (int row = 0; row < 4; row++) {
      final rowItems = <String>[];
      for (int col = 0; col < 4; col++) {
        final item = _currentGrid[row * 4 + col];
        if (item != null) {
          if (rowItems.contains(item)) return false;
          rowItems.add(item);
        }
      }
    }
    
    // 检查所有列
    for (int col = 0; col < 4; col++) {
      final colItems = <String>[];
      for (int row = 0; row < 4; row++) {
        final item = _currentGrid[row * 4 + col];
        if (item != null) {
          if (colItems.contains(item)) return false;
          colItems.add(item);
        }
      }
    }
    
    // 检查所有2x2宫格
    for (int subgridRow = 0; subgridRow < 2; subgridRow++) {
      for (int subgridCol = 0; subgridCol < 2; subgridCol++) {
        final subgridItems = <String>[];
        for (int row = subgridRow * 2; row < (subgridRow + 1) * 2; row++) {
          for (int col = subgridCol * 2; col < (subgridCol + 1) * 2; col++) {
            final item = _currentGrid[row * 4 + col];
            if (item != null) {
              if (subgridItems.contains(item)) return false;
              subgridItems.add(item);
            }
          }
        }
      }
    }
    
    return true;
  }
  
  /// 获取剩余可用数量
  int _getRemainingCount(String item) {
    final usedCount = _currentGrid.where((cell) => cell == item).length;
    return 4 - usedCount; // 每种图标最多使用4次（4x4网格）
  }

  /// 检查是否有冲突
  bool _hasConflict(int index) {
    final item = _currentGrid[index];
    if (item == null) return false;

    final row = index ~/ 4;
    final col = index % 4;

    // 检查行冲突
    for (int c = 0; c < 4; c++) {
      if (c != col && _currentGrid[row * 4 + c] == item) {
        return true;
      }
    }

    // 检查列冲突
    for (int r = 0; r < 4; r++) {
      if (r != row && _currentGrid[r * 4 + col] == item) {
        return true;
      }
    }

    // 检查2x2宫格冲突
    final subgridRow = (row ~/ 2) * 2;
    final subgridCol = (col ~/ 2) * 2;
    
    for (int r = subgridRow; r < subgridRow + 2; r++) {
      for (int c = subgridCol; c < subgridCol + 2; c++) {
        if ((r != row || c != col) && _currentGrid[r * 4 + c] == item) {
          return true;
        }
      }
    }

    return false;
  }
  
  // =============================================================================
  // 素材管理方法
  // =============================================================================
  
  /// 获取物品颜色
  Color _getItemColor(String itemId) {
    switch (itemId.toLowerCase()) {
      case 'apple':
        return Colors.red[400]!;
      case 'banana':
        return Colors.yellow[600]!;
      case 'grape':
        return Colors.purple[400]!;
      case 'orange':
        return Colors.orange[400]!;
      case 'strawberry':
        return Colors.pink[400]!;
      case 'lemon':
        return Colors.yellow[400]!;
      case 'cherry':
        return Colors.red[600]!;
      case 'blueberry':
        return Colors.blue[400]!;
      default:
        return Colors.grey[400]!;
    }
  }

  /// 获取物品形状
  BoxShape _getItemShape(String itemId) {
    switch (itemId.toLowerCase()) {
      case 'apple':
      case 'orange':
      case 'cherry':
      case 'blueberry':
        return BoxShape.circle;
      default:
        return BoxShape.rectangle;
    }
  }

  /// 获取物品图标
  Widget? _getItemIcon(String itemId, double size) {
    final iconSize = size * 0.6;
    final color = Colors.white;

    // 尝试使用 GameAssets 加载图片，如果失败则使用图标
    try {
      return Image.asset(
        GameAssets.numericFruit(itemId),
        width: iconSize,
        height: iconSize,
        color: color,
        errorBuilder: (context, error, stackTrace) {
          return _getFallbackIcon(itemId, iconSize, color);
        },
      );
    } catch (e) {
      return _getFallbackIcon(itemId, iconSize, color);
    }
  }
  
  /// 获取备用图标
  Widget _getFallbackIcon(String itemId, double iconSize, Color color) {
    switch (itemId.toLowerCase()) {
      case 'apple':
        return Icon(Icons.apple, color: color, size: iconSize);
      case 'banana':
        return Icon(Icons.sports_volleyball, color: color, size: iconSize);
      case 'grape':
        return Icon(Icons.circle, color: color, size: iconSize);
      case 'orange':
        return Icon(Icons.circle_outlined, color: color, size: iconSize);
      case 'strawberry':
        return Icon(Icons.favorite, color: color, size: iconSize);
      case 'lemon':
        return Icon(Icons.star, color: color, size: iconSize);
      case 'cherry':
        return Icon(Icons.brightness_1, color: color, size: iconSize);
      case 'blueberry':
        return Icon(Icons.fiber_manual_record, color: color, size: iconSize);
      default:
        return Icon(Icons.help, color: color, size: iconSize);
    }
  }
}

/// 2x2宫格分割线绘制器
class SubgridLinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = UXThemeConfig.primaryBlue
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final cellSize = size.width / 4;

    // 垂直分割线（在第2列后）
    final verticalLine = Offset(cellSize * 2, 0);
    final verticalLineEnd = Offset(cellSize * 2, size.height);
    canvas.drawLine(verticalLine, verticalLineEnd, paint);

    // 水平分割线（在第2行后）
    final horizontalLine = Offset(0, cellSize * 2);
    final horizontalLineEnd = Offset(size.width, cellSize * 2);
    canvas.drawLine(horizontalLine, horizontalLineEnd, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 
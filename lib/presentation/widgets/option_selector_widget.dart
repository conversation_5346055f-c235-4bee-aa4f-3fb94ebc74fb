import 'package:flutter/material.dart';

class OptionSelectorWidget extends StatelessWidget {
  final List<String> options;
  final String? selectedOption;
  final Function(String) onOptionSelected;

  const OptionSelectorWidget({
    super.key,
    required this.options,
    this.selectedOption,
    required this.onOptionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '选择答案',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3436),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: options.length > 4 ? 3 : 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1.0,
                ),
                itemCount: options.length,
                itemBuilder: (context, index) {
                  return _buildOptionItem(options[index], index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionItem(String option, int index) {
    final isSelected = selectedOption == option;
    
    return GestureDetector(
      onTap: () => onOptionSelected(option),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected 
              ? const Color(0xFF6C5CE7).withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected 
                ? const Color(0xFF6C5CE7)
                : Colors.grey[300]!,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: _buildShapeWidget(option),
            ),
            // 选项标签
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? const Color(0xFF6C5CE7)
                      : Colors.grey[400],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D...
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
            // 选中指示器
            if (isSelected)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(
                    color: Color(0xFF6C5CE7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildShapeWidget(String shapeId) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: _getShapeColor(shapeId),
        shape: _getShapeType(shapeId),
        borderRadius: _getShapeType(shapeId) == BoxShape.rectangle 
            ? BorderRadius.circular(10) 
            : null,
      ),
      child: _getShapeIcon(shapeId),
    );
  }

  Color _getShapeColor(String shapeId) {
    switch (shapeId.toLowerCase()) {
      case 'red_circle':
      case 'red_square':
      case 'red_triangle':
        return Colors.red;
      case 'blue_circle':
      case 'blue_square':
      case 'blue_triangle':
        return Colors.blue;
      case 'green_circle':
      case 'green_square':
      case 'green_triangle':
        return Colors.green;
      case 'yellow_circle':
      case 'yellow_square':
      case 'yellow_triangle':
        return Colors.yellow;
      case 'purple_circle':
      case 'purple_square':
      case 'purple_triangle':
        return Colors.purple;
      case 'orange_circle':
      case 'orange_square':
      case 'orange_triangle':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  BoxShape _getShapeType(String shapeId) {
    if (shapeId.toLowerCase().contains('circle')) {
      return BoxShape.circle;
    }
    return BoxShape.rectangle;
  }

  Widget? _getShapeIcon(String shapeId) {
    if (shapeId.toLowerCase().contains('triangle')) {
      return const Icon(
        Icons.change_history,
        color: Colors.white,
        size: 24,
      );
    }
    return null;
  }
} 
import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../core/utils/game_animations.dart';
import 'game_core/base_game_widget.dart';

/// 图形推理游戏组件
///
/// 基于新架构的图形推理游戏实现，包含：
/// - 3x3网格模式识别
/// - 类型安全的素材管理
/// - 统一的动画系统
/// - 通用的游戏基类
class GraphicPatternWidget extends BaseGameWidget {
  const GraphicPatternWidget({
    super.key,
    required super.puzzleData,
    required super.onAnswerSubmitted,
    super.onHintRequested,
    super.onRestart,
    super.onPause,
  });

  @override
  State<GraphicPatternWidget> createState() => _GraphicPatternWidgetState();
}

class _GraphicPatternWidgetState extends BaseGameWidgetState<GraphicPatternWidget> {
  
  // =============================================================================
  // 游戏特定状态
  // =============================================================================
  
  /// 当前选中的选项
  String? _selectedOption;
  
  /// 网格动画控制器
  late AnimationController _gridAnimationController;
  
  /// 选项选择动画控制器
  late AnimationController _optionAnimationController;
  
  /// 网格缩放动画
  late Animation<double> _gridScaleAnimation;
  
  // =============================================================================
  // BaseGameWidgetState 抽象方法实现
  // =============================================================================
  
  @override
  void initializeGameSpecific() {
    // 初始化图形推理游戏特定的动画
    _gridAnimationController = AnimationController(
      duration: GameAnimations.normal,
      vsync: this,
    );
    
    _optionAnimationController = AnimationController(
      duration: GameAnimations.fast,
      vsync: this,
    );
    
    // 创建动画对象
    _gridScaleAnimation = GameAnimations.scaleIn(_gridAnimationController);
    
    // 启动网格动画
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _gridAnimationController.forward();
      }
    });
  }
  
  @override
  void disposeGameSpecific() {
    _gridAnimationController.dispose();
    _optionAnimationController.dispose();
  }
  
  @override
  void onGameRestarted() {
    super.onGameRestarted();
    setState(() {
      _selectedOption = null;
    });
    _gridAnimationController.reset();
    _optionAnimationController.reset();
  }
  
  @override
  Widget buildGameContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 游戏说明
          _buildGameInstructions(),

          SizedBox(height: UXThemeConfig.paddingL),

          // 3x3游戏网格
          _buildGameGrid(),

          SizedBox(height: UXThemeConfig.paddingL),

          // 选项区域
          _buildOptionsArea(),
        ],
      ),
    );
  }
  
  // =============================================================================
  // 游戏特定UI构建方法
  // =============================================================================
  
  /// 构建游戏说明
  Widget _buildGameInstructions() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: UXThemeConfig.primaryBlue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.grid_3x3,
            color: UXThemeConfig.primaryBlue,
            size: 32,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '图形推理',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.primaryBlue,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '观察3x3网格中的图形规律，选择正确的答案填入空白处',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: UXThemeConfig.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// 构建3x3游戏网格
  Widget _buildGameGrid() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final grid = data['grid'] as List<dynamic>? ?? [];
    final emptyIndex = data['emptyIndex'] as int? ?? 8;
    
    return AnimatedBuilder(
      animation: _gridScaleAnimation,
      builder: (context, child) {
        return ScaleTransition(
          scale: _gridScaleAnimation,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(UXThemeConfig.paddingM),
              child: AspectRatio(
                aspectRatio: 1.0,
                child: GridView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: UXThemeConfig.paddingS,
                    mainAxisSpacing: UXThemeConfig.paddingS,
                  ),
                  itemCount: 9,
                  itemBuilder: (context, index) {
                    return _buildGridCell(index, grid, emptyIndex);
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  /// 构建网格单元格
  Widget _buildGridCell(int index, List<dynamic> grid, int emptyIndex) {
    final isEmptyCell = index == emptyIndex;
    final cellContent = index < grid.length ? grid[index] as String? : null;
    
    return GestureDetector(
      onTap: isEmptyCell && _selectedOption != null ? () => _submitAnswer() : null,
      child: AnimatedContainer(
        duration: GameAnimations.fast,
        decoration: BoxDecoration(
          color: isEmptyCell 
              ? (_selectedOption != null 
                  ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1) 
                  : Colors.grey.shade100)
              : Colors.white,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          border: Border.all(
            color: isEmptyCell 
                ? (_selectedOption != null 
                    ? UXThemeConfig.primaryBlue 
                    : Colors.grey.shade300)
                : Colors.grey.shade200,
            width: isEmptyCell ? 2 : 1,
          ),
          boxShadow: [
            if (!isEmptyCell)
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: isEmptyCell ? _buildEmptyCell() : _buildContentCell(cellContent),
      ),
    );
  }
  
  /// 构建空白单元格
  Widget _buildEmptyCell() {
    return Container(
      child: _selectedOption != null
          ? Stack(
              children: [
                Center(
                  child: _buildShapeWidget(_selectedOption!, isPreview: true),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: UXThemeConfig.primaryBlue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.question_mark,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
              ],
            )
          : Center(
              child: Icon(
                Icons.help_outline,
                color: Colors.grey.shade400,
                size: 32,
              ),
            ),
    );
  }
  
  /// 构建内容单元格
  Widget _buildContentCell(String? content) {
    if (content == null) return Container();
    
    return Center(
      child: _buildShapeWidget(content),
    );
  }
  
  /// 构建图形组件
  Widget _buildShapeWidget(String shapeId, {bool isPreview = false}) {
    return Container(
      width: isPreview ? 30 : 40,
      height: isPreview ? 30 : 40,
      decoration: BoxDecoration(
        color: _getShapeColor(shapeId).withValues(alpha: isPreview ? 0.6 : 1.0),
        shape: _getShapeType(shapeId),
        borderRadius: _getShapeType(shapeId) == BoxShape.rectangle 
            ? BorderRadius.circular(UXThemeConfig.radiusS) 
            : null,
      ),
      child: _getShapeIcon(shapeId, isPreview),
    );
  }
  
  /// 构建选项区域
  Widget _buildOptionsArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final options = data['options'] as List<dynamic>? ?? [];
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择答案',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),
          
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 选项网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: options.length > 4 ? 3 : 2,
              crossAxisSpacing: UXThemeConfig.paddingM,
              mainAxisSpacing: UXThemeConfig.paddingM,
              childAspectRatio: 1.0,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index] as Map<String, dynamic>;
              final optionId = option['id'] as String? ?? '';
              final shapeId = option['shapeId'] as String? ?? '';
              final isSelected = _selectedOption == optionId;
              
              return _buildOptionCard(optionId, shapeId, index, isSelected);
            },
          ),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 提交按钮
          _buildSubmitButton(),
        ],
      ),
    );
  }
  
  /// 构建选项卡片
  Widget _buildOptionCard(String optionId, String shapeId, int index, bool isSelected) {
    return GestureDetector(
      onTap: () => _selectOption(optionId),
      child: AnimatedContainer(
        duration: GameAnimations.fast,
        decoration: BoxDecoration(
          color: isSelected ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          border: Border.all(
            color: isSelected ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            // 选项内容
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 图形显示
                  _buildShapeWidget(shapeId),
                  
                  SizedBox(height: UXThemeConfig.paddingS),
                  
                  // 选项标签
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: UXThemeConfig.paddingS,
                      vertical: UXThemeConfig.paddingXS,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected ? UXThemeConfig.primaryBlue : Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                    ),
                    child: Text(
                      String.fromCharCode(65 + index), // A, B, C, D
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // 选中标记
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: UXThemeConfig.primaryBlue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  /// 构建提交按钮
  Widget _buildSubmitButton() {
    final hasSelection = _selectedOption != null;
    
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: hasSelection ? _submitAnswer : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: hasSelection ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          ),
          elevation: hasSelection ? 4 : 0,
        ),
        child: Text(
          hasSelection ? '提交答案' : '请选择一个答案',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
  
  // =============================================================================
  // 图形相关辅助方法
  // =============================================================================
  
  /// 获取图形颜色
  Color _getShapeColor(String shapeId) {
    switch (shapeId.toLowerCase()) {
      case 'red_circle':
      case 'red_square':
      case 'red_triangle':
        return Colors.red;
      case 'blue_circle':
      case 'blue_square':
      case 'blue_triangle':
        return Colors.blue;
      case 'green_circle':
      case 'green_square':
      case 'green_triangle':
        return Colors.green;
      case 'yellow_circle':
      case 'yellow_square':
      case 'yellow_triangle':
        return Colors.yellow;
      case 'purple_circle':
      case 'purple_square':
      case 'purple_triangle':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
  
  /// 获取图形类型
  BoxShape _getShapeType(String shapeId) {
    if (shapeId.toLowerCase().contains('circle')) {
      return BoxShape.circle;
    }
    return BoxShape.rectangle;
  }
  
  /// 获取图形图标
  Widget? _getShapeIcon(String shapeId, bool isPreview) {
    if (shapeId.toLowerCase().contains('triangle')) {
      return Icon(
        Icons.change_history,
        color: Colors.white,
        size: isPreview ? 16 : 20,
      );
    }
    return null;
  }
  
  // =============================================================================
  // 游戏逻辑方法
  // =============================================================================
  
  /// 选择选项
  void _selectOption(String optionId) {
    if (isPaused || isShowingFeedback) return;
    
    setState(() {
      _selectedOption = optionId;
    });
    
    // 播放选择动画
    _optionAnimationController.forward().then((_) {
      _optionAnimationController.reverse();
    });
  }
  
  /// 提交答案
  void _submitAnswer() {
    if (_selectedOption == null || isPaused || isShowingFeedback) return;
    
    // 调用父类的答案提交处理
    handleAnswerSubmission(_selectedOption);
  }
} 
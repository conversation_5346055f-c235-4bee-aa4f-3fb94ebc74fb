import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../core/constants/game_assets.dart';
import '../../core/utils/game_animations.dart';
import 'game_core/base_game_widget.dart';

/// 镜像对称游戏组件
///
/// 基于新架构的镜像对称游戏实现，包含：
/// - 类型安全的素材管理
/// - 统一的动画系统
/// - 通用的游戏基类
/// - 完整的用户交互
class MirrorSymmetryWidget extends BaseGameWidget {
  const MirrorSymmetryWidget({
    super.key,
    required super.puzzleData,
    required super.onAnswerSubmitted,
    super.onHintRequested,
    super.onRestart,
    super.onPause,
  });

  @override
  State<MirrorSymmetryWidget> createState() => _MirrorSymmetryWidgetState();
}

class _MirrorSymmetryWidgetState extends BaseGameWidgetState<MirrorSymmetryWidget> {
  
  // =============================================================================
  // 游戏特定状态
  // =============================================================================
  
  /// 当前选中的答案
  String? _selectedAnswer;
  
  /// 镜像翻转动画控制器
  late AnimationController _mirrorFlipController;
  
  /// 选项选择动画控制器
  late AnimationController _optionAnimationController;
  
  /// 镜像翻转动画
  late Animation<double> _mirrorFlipAnimation;
  

  
  // =============================================================================
  // BaseGameWidgetState 抽象方法实现
  // =============================================================================
  
  @override
  void initializeGameSpecific() {
    // 初始化镜像对称游戏特定的动画
    _mirrorFlipController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _optionAnimationController = AnimationController(
      duration: GameAnimations.fast,
      vsync: this,
    );
    
    // 创建动画对象
    _mirrorFlipAnimation = GameAnimations.rotate(_mirrorFlipController, turns: 0.5);
    
    // 启动镜像翻转动画
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _mirrorFlipController.forward();
      }
    });
  }
  
  @override
  void disposeGameSpecific() {
    _mirrorFlipController.dispose();
    _optionAnimationController.dispose();
  }
  
  @override
  void onGameRestarted() {
    super.onGameRestarted();
    setState(() {
      _selectedAnswer = null;
    });
    _mirrorFlipController.reset();
    _optionAnimationController.reset();
  }
  
  @override
  Widget buildGameContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 镜像演示区域
          _buildMirrorDemonstration(),

          SizedBox(height: UXThemeConfig.paddingXL),

          // 问题说明
          _buildQuestionText(),

          SizedBox(height: UXThemeConfig.paddingL),

          // 选项区域
          _buildOptionsArea(),
        ],
      ),
    );
  }
  
  // =============================================================================
  // 游戏特定UI构建方法
  // =============================================================================
  
  /// 构建镜像演示区域
  Widget _buildMirrorDemonstration() {
    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题
          Text(
            '镜像对称演示',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),

          SizedBox(height: UXThemeConfig.paddingL),

          // 主要演示区域
          SizedBox(
            height: 200,
            child: Row(
              children: [
                // 原始衣服
                Expanded(flex: 2, child: _buildOriginalClothing()),

                // 镜子
                SizedBox(width: 80, child: _buildMirror()),

                // 镜像中的衣服
                Expanded(flex: 2, child: _buildMirrorReflection()),
              ],
            ),
          ),

          SizedBox(height: UXThemeConfig.paddingM),

          // 说明文字
          Row(
            children: [
              Expanded(
                child: Text(
                  '原始衣服\n图案在右边',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: UXThemeConfig.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 80),
              Expanded(
                child: Text(
                  '镜子中\n图案在左边',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: UXThemeConfig.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// 构建原始衣服
  Widget _buildOriginalClothing() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 使用新的素材管理系统
          _buildClothingDisplay(false), // false表示不是镜像
          
          SizedBox(height: UXThemeConfig.paddingS),
          
          Text(
            '原始衣服',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建镜子
  Widget _buildMirror() {
    return AnimatedBuilder(
      animation: _mirrorFlipAnimation,
      builder: (context, child) {
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()..rotateY(_mirrorFlipAnimation.value * 3.14159),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.grey.shade300,
                  Colors.grey.shade100,
                  Colors.grey.shade300,
                ],
              ),
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
              border: Border.all(color: Colors.grey.shade400, width: 3),
            ),
            child: Center(
              child: Icon(
                Icons.crop_rotate,
                color: Colors.grey.shade600,
                size: 40,
              ),
            ),
          ),
        );
      },
    );
  }
  
  /// 构建镜像反射
  Widget _buildMirrorReflection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.pink.shade50,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: Colors.pink.shade200),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 使用新的素材管理系统
          _buildClothingDisplay(true), // true表示是镜像
          
          SizedBox(height: UXThemeConfig.paddingS),
          
          Text(
            '镜像衣服',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建衣服展示
  Widget _buildClothingDisplay(bool isMirrored) {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final clothingType = data['clothingType'] as String? ?? 'shirt';
    final clothingId = data['clothingId'] as String? ?? '01';
    
    // 将字符串转换为枚举
    final ClothingType type = ClothingType.values.firstWhere(
      (e) => e.name == clothingType,
      orElse: () => ClothingType.shirt,
    );
    
    return Container(
      width: 80,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
        border: Border.all(color: Colors.grey.shade300, width: 2),
      ),
      child: Transform(
        alignment: Alignment.center,
        transform: isMirrored ? (Matrix4.identity()..scale(-1.0, 1.0)) : Matrix4.identity(),
        child: Image.asset(
          GameAssets.mirrorClothing(type, clothingId),
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // 如果素材不存在，显示占位符
            return _buildClothingPlaceholder(type, isMirrored);
          },
        ),
      ),
    );
  }
  
  /// 构建衣服占位符 (当素材不存在时使用)
  Widget _buildClothingPlaceholder(ClothingType type, bool isMirrored) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getClothingIcon(type),
            size: 40,
            color: Colors.grey.shade600,
          ),
          SizedBox(height: UXThemeConfig.paddingXS),
          Text(
            isMirrored ? '镜像' : '原始',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 获取衣服类型对应的图标
  IconData _getClothingIcon(ClothingType type) {
    switch (type) {
      case ClothingType.shirt:
        return Icons.checkroom;
      case ClothingType.dress:
        return Icons.woman;
      case ClothingType.pants:
        return Icons.man;
      case ClothingType.skirt:
        return Icons.woman_2;
      case ClothingType.jacket:
        return Icons.dry_cleaning;
    }
  }
  
  /// 构建问题说明
  Widget _buildQuestionText() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: UXThemeConfig.primaryBlue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.help_outline,
            color: UXThemeConfig.primaryBlue,
            size: 32,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '请选择正确的镜像衣服',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.primaryBlue,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '观察原始衣服的图案位置，选择在镜子中看到的正确镜像',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: UXThemeConfig.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// 构建选项区域
  Widget _buildOptionsArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final options = data['options'] as List<dynamic>? ?? [];
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择答案',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),
          
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 选项网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: options.length > 4 ? 3 : 2,
              crossAxisSpacing: UXThemeConfig.paddingM,
              mainAxisSpacing: UXThemeConfig.paddingM,
              childAspectRatio: 1.0,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index] as Map<String, dynamic>;
              final optionId = option['id'] as String? ?? '';
              final isSelected = _selectedAnswer == optionId;
              
              return _buildOptionCard(option, index, isSelected);
            },
          ),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 提交按钮
          _buildSubmitButton(),
        ],
      ),
    );
  }
  
  /// 构建选项卡片
  Widget _buildOptionCard(Map<String, dynamic> option, int index, bool isSelected) {
    final optionId = option['id'] as String? ?? '';
    final clothingType = option['clothingType'] as String? ?? 'shirt';
    final clothingId = option['clothingId'] as String? ?? '01';
    
    return GestureDetector(
      onTap: () => _selectOption(optionId),
      child: AnimatedContainer(
        duration: GameAnimations.fast,
        decoration: BoxDecoration(
          color: isSelected ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          border: Border.all(
            color: isSelected ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            // 选项内容
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 衣服图片
                  _buildOptionClothing(clothingType, clothingId),
                  
                  SizedBox(height: UXThemeConfig.paddingS),
                  
                  // 选项标签
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: UXThemeConfig.paddingS,
                      vertical: UXThemeConfig.paddingXS,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected ? UXThemeConfig.primaryBlue : Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                    ),
                    child: Text(
                      String.fromCharCode(65 + index), // A, B, C, D
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // 选中标记
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: UXThemeConfig.primaryBlue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  /// 构建选项衣服
  Widget _buildOptionClothing(String clothingType, String clothingId) {
    final ClothingType type = ClothingType.values.firstWhere(
      (e) => e.name == clothingType,
      orElse: () => ClothingType.shirt,
    );
    
    return Container(
      width: 60,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Image.asset(
        GameAssets.mirrorClothing(type, clothingId),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return _buildClothingPlaceholder(type, false);
        },
      ),
    );
  }
  
  /// 构建提交按钮
  Widget _buildSubmitButton() {
    final hasSelection = _selectedAnswer != null;
    
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: hasSelection ? _submitAnswer : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: hasSelection ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          ),
          elevation: hasSelection ? 4 : 0,
        ),
        child: Text(
          hasSelection ? '提交答案' : '请选择一个答案',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
  
  // =============================================================================
  // 游戏逻辑方法
  // =============================================================================
  
  /// 选择选项
  void _selectOption(String optionId) {
    if (isPaused || isShowingFeedback) return;
    
    setState(() {
      _selectedAnswer = optionId;
    });
    
    // 播放选择动画
    _optionAnimationController.forward().then((_) {
      _optionAnimationController.reverse();
    });
  }
  
  /// 提交答案
  void _submitAnswer() {
    if (_selectedAnswer == null || isPaused || isShowingFeedback) return;
    
    // 调用父类的答案提交处理
    handleAnswerSubmission(_selectedAnswer);
  }
}

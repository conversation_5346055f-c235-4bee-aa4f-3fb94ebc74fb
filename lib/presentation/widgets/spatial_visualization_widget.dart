import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../core/constants/game_assets.dart';
import '../../core/utils/game_animations.dart';
import 'game_core/base_game_widget.dart';

/// 空间想象游戏组件
///
/// 基于新架构的空间想象游戏实现，包含：
/// - 3D立体图形折叠
/// - 类型安全的素材管理
/// - 统一的动画系统
/// - 通用的游戏基类
class SpatialVisualizationWidget extends BaseGameWidget {
  const SpatialVisualizationWidget({
    super.key,
    required super.puzzleData,
    required super.onAnswerSubmitted,
    super.onHintRequested,
    super.onRestart,
    super.onPause,
  });

  @override
  State<SpatialVisualizationWidget> createState() => _SpatialVisualizationWidgetState();
}

class _SpatialVisualizationWidgetState extends BaseGameWidgetState<SpatialVisualizationWidget> {
  
  // =============================================================================
  // 游戏特定状态
  // =============================================================================
  
  /// 当前选中的答案
  String? _selectedAnswer;
  
  /// 展开图动画控制器
  late AnimationController _expandedShapeController;
  
  /// 选项选择动画控制器
  late AnimationController _optionAnimationController;
  
  /// 展开图旋转动画
  late Animation<double> _expandedRotationAnimation;
  
  // =============================================================================
  // BaseGameWidgetState 抽象方法实现
  // =============================================================================
  
  @override
  void initializeGameSpecific() {
    // 初始化空间想象游戏特定的动画
    _expandedShapeController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _optionAnimationController = AnimationController(
      duration: GameAnimations.fast,
      vsync: this,
    );
    
    // 创建动画对象
    _expandedRotationAnimation = GameAnimations.rotate(_expandedShapeController, turns: 1.0);
    
    // 启动展开图旋转动画
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        GameAnimations.startRepeating(_expandedShapeController);
      }
    });
  }
  
  @override
  void disposeGameSpecific() {
    _expandedShapeController.dispose();
    _optionAnimationController.dispose();
  }
  
  @override
  void onGameRestarted() {
    super.onGameRestarted();
    setState(() {
      _selectedAnswer = null;
    });
    _expandedShapeController.reset();
    _optionAnimationController.reset();
  }
  
  @override
  Widget buildGameContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 游戏说明
          _buildGameInstructions(),

          SizedBox(height: UXThemeConfig.paddingL),

          // 展开图显示区域
          _buildExpandedShapeArea(),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 箭头指示
          _buildArrowIndicator(),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 3D选项选择区域
          _buildOptionsArea(),
        ],
      ),
    );
  }
  
  // =============================================================================
  // 游戏特定UI构建方法
  // =============================================================================
  
  /// 构建游戏说明
  Widget _buildGameInstructions() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: UXThemeConfig.primaryBlue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.view_in_ar,
            color: UXThemeConfig.primaryBlue,
            size: 32,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '空间想象',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.primaryBlue,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '观察展开图，想象折叠后的3D立体图形，选择正确答案',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: UXThemeConfig.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建展开图显示区域
  Widget _buildExpandedShapeArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final expandedShapeId = data['expandedShape'] as String? ?? 'cube_expanded';
    
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          children: [
            Text(
              '展开图',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: UXThemeConfig.textDark,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            // 展开图显示
            AnimatedBuilder(
              animation: _expandedRotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _expandedRotationAnimation.value * 2 * 3.14159,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Image.asset(
                      GameAssets.spatialExpanded(expandedShapeId),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildExpandedShapePlaceholder(expandedShapeId);
                      },
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建展开图占位符
  Widget _buildExpandedShapePlaceholder(String shapeId) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.unfold_more,
            size: 60,
            color: Colors.grey.shade600,
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            '展开图',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建箭头指示
  Widget _buildArrowIndicator() {
    return Column(
      children: [
        Icon(
          Icons.keyboard_double_arrow_down,
          size: 32,
          color: UXThemeConfig.primaryBlue,
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Text(
          '折叠后是哪个立体图形？',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: UXThemeConfig.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建选项区域
  Widget _buildOptionsArea() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final options = data['options'] as List<dynamic>? ?? [];
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择正确的立体图形',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),
          
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 选项网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: options.length > 4 ? 3 : 2,
              crossAxisSpacing: UXThemeConfig.paddingM,
              mainAxisSpacing: UXThemeConfig.paddingM,
              childAspectRatio: 1.0,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index] as Map<String, dynamic>;
              final optionId = option['id'] as String? ?? '';
              final shapeId = option['shapeId'] as String? ?? '';
              final isSelected = _selectedAnswer == optionId;
              
              return _buildOptionCard(optionId, shapeId, index, isSelected);
            },
          ),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 提交按钮
          _buildSubmitButton(),
        ],
      ),
    );
  }

  /// 构建选项卡片
  Widget _buildOptionCard(String optionId, String shapeId, int index, bool isSelected) {
    return GestureDetector(
      onTap: () => _selectOption(optionId),
      child: AnimatedContainer(
        duration: GameAnimations.fast,
        decoration: BoxDecoration(
          color: isSelected ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          border: Border.all(
            color: isSelected ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            // 3D图形显示
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 3D图形
                  _build3DShape(shapeId),
                  
                  SizedBox(height: UXThemeConfig.paddingS),
                  
                  // 选项标签
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: UXThemeConfig.paddingS,
                      vertical: UXThemeConfig.paddingXS,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected ? UXThemeConfig.primaryBlue : Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                    ),
                    child: Text(
                      String.fromCharCode(65 + index), // A, B, C, D
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // 选中标记
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: UXThemeConfig.primaryBlue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  /// 构建3D图形
  Widget _build3DShape(String shapeId) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Image.asset(
        GameAssets.spatialShape(shapeId),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return _build3DShapePlaceholder(shapeId);
        },
      ),
    );
  }
  
  /// 构建3D图形占位符
  Widget _build3DShapePlaceholder(String shapeId) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.view_in_ar,
            size: 40,
            color: Colors.grey.shade600,
          ),
          SizedBox(height: UXThemeConfig.paddingXS),
          Text(
            '3D图形',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建提交按钮
  Widget _buildSubmitButton() {
    final hasSelection = _selectedAnswer != null;
    
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: hasSelection ? _submitAnswer : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: hasSelection ? UXThemeConfig.primaryBlue : Colors.grey.shade300,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          ),
          elevation: hasSelection ? 4 : 0,
        ),
        child: Text(
          hasSelection ? '提交答案' : '请选择一个答案',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
  
  // =============================================================================
  // 游戏逻辑方法
  // =============================================================================
  
  /// 选择选项
  void _selectOption(String optionId) {
    if (isPaused || isShowingFeedback) return;
    
    setState(() {
      _selectedAnswer = optionId;
    });
    
    // 播放选择动画
    _optionAnimationController.forward().then((_) {
      _optionAnimationController.reverse();
    });
  }
  
  /// 提交答案
  void _submitAnswer() {
    if (_selectedAnswer == null || isPaused || isShowingFeedback) return;
    
    // 调用父类的答案提交处理
    handleAnswerSubmission(_selectedAnswer);
  }
} 
import 'package:flutter/material.dart';
import '../../data/models/puzzle.dart';

class SpatialVisualizationWidget extends StatelessWidget {
  final SpatialVisualizationData puzzleData;
  final String? selectedOption;
  final Function(String)? onOptionSelected;
  final bool showHint;
  final String? excludedOption;

  const SpatialVisualizationWidget({
    super.key,
    required this.puzzleData,
    this.selectedOption,
    this.onOptionSelected,
    this.showHint = false,
    this.excludedOption,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 展开图显示区域
        _buildExpandedShapeArea(),
        
        const SizedBox(height: 24),
        
        // 箭头指示
        _buildArrowIndicator(),
        
        const SizedBox(height: 24),
        
        // 3D选项选择区域
        _buildOptionsArea(),
      ],
    );
  }

  Widget _buildExpandedShapeArea() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text(
              '展开图',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            // 展开图显示
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: _buildExpandedShape(puzzleData.expandedShape),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArrowIndicator() {
    return Column(
      children: [
        Icon(
          Icons.keyboard_double_arrow_down,
          size: 32,
          color: const Color(0xFF6C5CE7),
        ),
        const SizedBox(height: 8),
        Text(
          '折叠后是哪个立体图形？',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildOptionsArea() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              '选择正确的立体图形',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            // 选项网格
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.0,
              ),
              itemCount: puzzleData.options.length,
              itemBuilder: (context, index) {
                final option = puzzleData.options[index];
                return _buildOptionCard(option, index);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard(String option, int index) {
    final isSelected = selectedOption == option;
    final isExcluded = excludedOption == option;
    final isEnabled = !isExcluded;

    return GestureDetector(
      onTap: isEnabled ? () => onOptionSelected?.call(option) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isExcluded 
              ? Colors.red[50]
              : isSelected 
                  ? const Color(0xFF6C5CE7).withValues(alpha: 0.1)
                  : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isExcluded
                ? Colors.red[300]!
                : isSelected 
                    ? const Color(0xFF6C5CE7)
                    : Colors.grey[300]!,
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            // 3D图形显示
            Center(
              child: _build3DShape(option, isEnabled),
            ),
            
            // 选项标签
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isExcluded 
                      ? Colors.red[400]
                      : isSelected 
                          ? const Color(0xFF6C5CE7)
                          : Colors.grey[400],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
            
            // 排除标记
            if (isExcluded)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.red[400],
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            
            // 选中标记
            if (isSelected && !isExcluded)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  decoration: const BoxDecoration(
                    color: Color(0xFF6C5CE7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedShape(String shapeId) {
    // 根据shapeId显示不同的展开图
    // 这里使用简化的图形表示，实际项目中可以使用SVG或自定义绘制
    return CustomPaint(
      painter: ExpandedShapePainter(shapeId),
      child: Container(),
    );
  }

  Widget _build3DShape(String shapeId, bool isEnabled) {
    // 根据shapeId显示不同的3D图形
    // 这里使用简化的立体图形表示
    return Opacity(
      opacity: isEnabled ? 1.0 : 0.5,
      child: CustomPaint(
        painter: Shape3DPainter(shapeId),
        child: SizedBox(
          width: 80,
          height: 80,
        ),
      ),
    );
  }
}

/// 展开图绘制器
class ExpandedShapePainter extends CustomPainter {
  final String shapeId;

  ExpandedShapePainter(this.shapeId);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF6C5CE7)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = const Color(0xFF6C5CE7).withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // 根据shapeId绘制不同的展开图
    switch (shapeId.toLowerCase()) {
      case 'cube_expanded':
        _drawCubeExpanded(canvas, size, paint, fillPaint);
        break;
      case 'pyramid_expanded':
        _drawPyramidExpanded(canvas, size, paint, fillPaint);
        break;
      case 'prism_expanded':
        _drawPrismExpanded(canvas, size, paint, fillPaint);
        break;
      default:
        _drawDefaultExpanded(canvas, size, paint, fillPaint);
    }
  }

  void _drawCubeExpanded(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final squareSize = size.width * 0.15;

    // 绘制十字形展开图
    final squares = [
      Rect.fromCenter(center: Offset(centerX, centerY - squareSize), width: squareSize, height: squareSize),
      Rect.fromCenter(center: Offset(centerX - squareSize, centerY), width: squareSize, height: squareSize),
      Rect.fromCenter(center: Offset(centerX, centerY), width: squareSize, height: squareSize),
      Rect.fromCenter(center: Offset(centerX + squareSize, centerY), width: squareSize, height: squareSize),
      Rect.fromCenter(center: Offset(centerX, centerY + squareSize), width: squareSize, height: squareSize),
      Rect.fromCenter(center: Offset(centerX, centerY + squareSize * 2), width: squareSize, height: squareSize),
    ];

    for (final square in squares) {
      canvas.drawRect(square, fillPaint);
      canvas.drawRect(square, paint);
    }
  }

  void _drawPyramidExpanded(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final triangleSize = size.width * 0.2;

    // 绘制三角锥展开图（一个正方形底面 + 四个三角形）
    final baseRect = Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: triangleSize,
      height: triangleSize,
    );
    canvas.drawRect(baseRect, fillPaint);
    canvas.drawRect(baseRect, paint);

    // 四个三角形面
    final triangles = [
      [Offset(centerX, centerY - triangleSize/2), Offset(centerX - triangleSize/2, centerY - triangleSize), Offset(centerX + triangleSize/2, centerY - triangleSize)],
      [Offset(centerX + triangleSize/2, centerY), Offset(centerX + triangleSize, centerY - triangleSize/2), Offset(centerX + triangleSize, centerY + triangleSize/2)],
      [Offset(centerX, centerY + triangleSize/2), Offset(centerX - triangleSize/2, centerY + triangleSize), Offset(centerX + triangleSize/2, centerY + triangleSize)],
      [Offset(centerX - triangleSize/2, centerY), Offset(centerX - triangleSize, centerY - triangleSize/2), Offset(centerX - triangleSize, centerY + triangleSize/2)],
    ];

    for (final triangle in triangles) {
      final path = Path()
        ..moveTo(triangle[0].dx, triangle[0].dy)
        ..lineTo(triangle[1].dx, triangle[1].dy)
        ..lineTo(triangle[2].dx, triangle[2].dy)
        ..close();
      canvas.drawPath(path, fillPaint);
      canvas.drawPath(path, paint);
    }
  }

  void _drawPrismExpanded(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    // 绘制三角柱展开图
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final width = size.width * 0.6;
    final height = size.height * 0.3;

    // 三个矩形面
    final rect1 = Rect.fromLTWH(centerX - width/2, centerY - height/2, width/3, height);
    final rect2 = Rect.fromLTWH(centerX - width/6, centerY - height/2, width/3, height);
    final rect3 = Rect.fromLTWH(centerX + width/6, centerY - height/2, width/3, height);

    canvas.drawRect(rect1, fillPaint);
    canvas.drawRect(rect1, paint);
    canvas.drawRect(rect2, fillPaint);
    canvas.drawRect(rect2, paint);
    canvas.drawRect(rect3, fillPaint);
    canvas.drawRect(rect3, paint);

    // 两个三角形底面
    final triangle1 = Path()
      ..moveTo(centerX - width/2, centerY - height/2)
      ..lineTo(centerX, centerY - height/2 - height/3)
      ..lineTo(centerX + width/2, centerY - height/2)
      ..close();
    
    final triangle2 = Path()
      ..moveTo(centerX - width/2, centerY + height/2)
      ..lineTo(centerX, centerY + height/2 + height/3)
      ..lineTo(centerX + width/2, centerY + height/2)
      ..close();

    canvas.drawPath(triangle1, fillPaint);
    canvas.drawPath(triangle1, paint);
    canvas.drawPath(triangle2, fillPaint);
    canvas.drawPath(triangle2, paint);
  }

  void _drawDefaultExpanded(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    // 默认绘制一个简单的十字形
    _drawCubeExpanded(canvas, size, paint, fillPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 3D图形绘制器
class Shape3DPainter extends CustomPainter {
  final String shapeId;

  Shape3DPainter(this.shapeId);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF6C5CE7)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = const Color(0xFF6C5CE7).withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    // 根据shapeId绘制不同的3D图形
    switch (shapeId.toLowerCase()) {
      case 'cube_3d':
        _drawCube3D(canvas, size, paint, fillPaint);
        break;
      case 'pyramid_3d':
        _drawPyramid3D(canvas, size, paint, fillPaint);
        break;
      case 'prism_3d':
        _drawPrism3D(canvas, size, paint, fillPaint);
        break;
      default:
        _drawDefaultShape3D(canvas, size, paint, fillPaint);
    }
  }

  void _drawCube3D(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final cubeSize = size.width * 0.4;
    final depth = cubeSize * 0.3;

    // 前面
    final frontRect = Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: cubeSize,
      height: cubeSize,
    );

    // 后面（偏移显示立体效果）
    final backRect = Rect.fromCenter(
      center: Offset(centerX - depth, centerY - depth),
      width: cubeSize,
      height: cubeSize,
    );

    // 绘制后面
    canvas.drawRect(backRect, fillPaint);
    canvas.drawRect(backRect, paint);

    // 连接线
    canvas.drawLine(frontRect.topLeft, backRect.topLeft, paint);
    canvas.drawLine(frontRect.topRight, backRect.topRight, paint);
    canvas.drawLine(frontRect.bottomLeft, backRect.bottomLeft, paint);
    canvas.drawLine(frontRect.bottomRight, backRect.bottomRight, paint);

    // 绘制前面
    canvas.drawRect(frontRect, fillPaint);
    canvas.drawRect(frontRect, paint);
  }

  void _drawPyramid3D(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final baseSize = size.width * 0.5;
    final height = size.height * 0.4;

    // 底面四个角
    final bottomLeft = Offset(centerX - baseSize/2, centerY + height/2);
    final bottomRight = Offset(centerX + baseSize/2, centerY + height/2);
    final topLeft = Offset(centerX - baseSize/4, centerY + height/4);
    final topRight = Offset(centerX + baseSize/4, centerY + height/4);
    
    // 顶点
    final apex = Offset(centerX, centerY - height/2);

    // 底面
    final basePath = Path()
      ..moveTo(bottomLeft.dx, bottomLeft.dy)
      ..lineTo(bottomRight.dx, bottomRight.dy)
      ..lineTo(topRight.dx, topRight.dy)
      ..lineTo(topLeft.dx, topLeft.dy)
      ..close();
    
    canvas.drawPath(basePath, fillPaint);
    canvas.drawPath(basePath, paint);

    // 侧面
    canvas.drawLine(bottomLeft, apex, paint);
    canvas.drawLine(bottomRight, apex, paint);
    canvas.drawLine(topLeft, apex, paint);
    canvas.drawLine(topRight, apex, paint);

    // 可见的三角形面
    final frontFace = Path()
      ..moveTo(bottomLeft.dx, bottomLeft.dy)
      ..lineTo(bottomRight.dx, bottomRight.dy)
      ..lineTo(apex.dx, apex.dy)
      ..close();
    
    canvas.drawPath(frontFace, fillPaint);
    canvas.drawPath(frontFace, paint);
  }

  void _drawPrism3D(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final width = size.width * 0.4;
    final height = size.height * 0.5;
    final depth = width * 0.3;

    // 前面三角形
    final frontTriangle = Path()
      ..moveTo(centerX - width/2, centerY + height/2)
      ..lineTo(centerX + width/2, centerY + height/2)
      ..lineTo(centerX, centerY - height/2)
      ..close();

    // 后面三角形
    final backTriangle = Path()
      ..moveTo(centerX - width/2 - depth, centerY + height/2 - depth)
      ..lineTo(centerX + width/2 - depth, centerY + height/2 - depth)
      ..lineTo(centerX - depth, centerY - height/2 - depth)
      ..close();

    // 绘制后面
    canvas.drawPath(backTriangle, fillPaint);
    canvas.drawPath(backTriangle, paint);

    // 连接线
    canvas.drawLine(Offset(centerX - width/2, centerY + height/2), 
                   Offset(centerX - width/2 - depth, centerY + height/2 - depth), paint);
    canvas.drawLine(Offset(centerX + width/2, centerY + height/2), 
                   Offset(centerX + width/2 - depth, centerY + height/2 - depth), paint);
    canvas.drawLine(Offset(centerX, centerY - height/2), 
                   Offset(centerX - depth, centerY - height/2 - depth), paint);

    // 绘制前面
    canvas.drawPath(frontTriangle, fillPaint);
    canvas.drawPath(frontTriangle, paint);
  }

  void _drawDefaultShape3D(Canvas canvas, Size size, Paint paint, Paint fillPaint) {
    // 默认绘制立方体
    _drawCube3D(canvas, size, paint, fillPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 
import 'package:flutter/material.dart';
import '../../../core/theme/ux_theme_config.dart';

/// 语言选择器组件
class LanguageSelectorWidget extends StatelessWidget {
  final String currentLanguage;
  final bool isChanging;
  final ValueChanged<String> onLanguageChanged;

  const LanguageSelectorWidget({
    super.key,
    required this.currentLanguage,
    this.isChanging = false,
    required this.onLanguageChanged,
  });

  static const List<Map<String, String>> _supportedLanguages = [
    {'code': 'zh_CN', 'name': '简体中文', 'nativeName': '简体中文', 'flag': '🇨🇳'},
    {'code': 'zh_TW', 'name': '繁體中文', 'nativeName': '繁體中文', 'flag': '🇹🇼'},
    {
      'code': 'en_US',
      'name': 'English',
      'nativeName': 'English',
      'flag': '🇺🇸',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final currentLangData = _supportedLanguages.firstWhere(
      (lang) => lang['code'] == currentLanguage,
      orElse: () => _supportedLanguages.first,
    );

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(Icons.language, color: theme.colorScheme.primary),
        ),
        title: const Text(
          '界面语言',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: isChanging
            ? Row(
                children: [
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text('正在切换...'),
                ],
              )
            : Row(
                children: [
                  Text(
                    currentLangData['flag']!,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Text(currentLangData['nativeName']!),
                ],
              ),
        trailing: isChanging ? null : const Icon(Icons.chevron_right),
        onTap: isChanging ? null : () => _showLanguageDialog(context),
      ),
    );
  }

  /// 显示语言选择对话框
  void _showLanguageDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.language, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            const Text('选择语言'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _supportedLanguages.map((lang) {
            final isSelected = lang['code'] == currentLanguage;

            return ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                  border: isSelected
                      ? Border.all(color: theme.colorScheme.primary)
                      : null,
                ),
                child: Center(
                  child: Text(
                    lang['flag']!,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
              title: Text(
                lang['nativeName']!,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? theme.colorScheme.primary : null,
                ),
              ),
              subtitle: lang['name'] != lang['nativeName']
                  ? Text(
                      lang['name']!,
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    )
                  : null,
              trailing: isSelected
                  ? Icon(Icons.check_circle, color: theme.colorScheme.primary)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                if (!isSelected) {
                  onLanguageChanged(lang['code']!);
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
}

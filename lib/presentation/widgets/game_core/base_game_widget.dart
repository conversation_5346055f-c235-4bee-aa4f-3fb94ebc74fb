import 'package:flutter/material.dart';
import '../../../core/utils/game_animations.dart';
import '../../../core/theme/ux_theme_config.dart';

/// 通用游戏组件基类
/// 
/// 提供所有游戏类型的通用功能：
/// - 统一的动画管理
/// - 标准的生命周期
/// - 一致的用户交互
/// - 通用的错误处理
abstract class BaseGameWidget extends StatefulWidget {
  /// 谜题数据
  final Map<String, dynamic> puzzleData;
  
  /// 答案提交回调
  final Function(dynamic) onAnswerSubmitted;
  
  /// 提示请求回调
  final VoidCallback? onHintRequested;
  
  /// 重新开始回调
  final VoidCallback? onRestart;
  
  /// 暂停游戏回调
  final VoidCallback? onPause;

  const BaseGameWidget({
    super.key,
    required this.puzzleData,
    required this.onAnswerSubmitted,
    this.onHintRequested,
    this.onRestart,
    this.onPause,
  });
}

/// 通用游戏组件状态基类
/// 
/// 提供动画管理、状态控制和通用UI逻辑
abstract class BaseGameWidgetState<T extends BaseGameWidget> 
    extends State<T> with TickerProviderStateMixin {
  
  // =============================================================================
  // 动画控制器
  // =============================================================================
  
  /// 页面进入动画控制器
  late AnimationController _introController;
  
  /// 反馈动画控制器 (成功/失败)
  late AnimationController _feedbackController;
  
  /// 提示动画控制器
  late AnimationController _hintController;
  
  // =============================================================================
  // 动画对象
  // =============================================================================
  
  /// 页面淡入动画
  late Animation<double> _fadeInAnimation;
  
  /// 页面滑入动画
  late Animation<Offset> _slideInAnimation;
  
  /// 反馈缩放动画
  late Animation<double> _feedbackScaleAnimation;
  

  
  // =============================================================================
  // 状态变量
  // =============================================================================
  
  /// 游戏是否已初始化
  bool _isInitialized = false;
  
  /// 游戏是否暂停
  bool _isPaused = false;
  
  /// 当前是否显示提示
  bool _isShowingHint = false;
  
  /// 当前是否显示反馈
  bool _isShowingFeedback = false;
  
  // =============================================================================
  // 生命周期方法
  // =============================================================================
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startIntroAnimation();
    
    // 延迟初始化游戏特定内容
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initializeGameSpecific();
      setState(() {
        _isInitialized = true;
      });
    });
  }
  
  @override
  void dispose() {
    _introController.dispose();
    _feedbackController.dispose();
    _hintController.dispose();
    disposeGameSpecific();
    super.dispose();
  }
  
  // =============================================================================
  // 动画初始化
  // =============================================================================
  
  /// 初始化动画控制器和动画对象
  void _initializeAnimations() {
    // 页面进入动画
    _introController = AnimationController(
      duration: GameAnimations.normal,
      vsync: this,
    );
    
    // 反馈动画
    _feedbackController = AnimationController(
      duration: GameAnimations.celebration,
      vsync: this,
    );
    
    // 提示动画
    _hintController = AnimationController(
      duration: GameAnimations.slow,
      vsync: this,
    );
    
    // 创建动画对象
    _fadeInAnimation = GameAnimations.fadeIn(_introController);
    _slideInAnimation = GameAnimations.slideInFromBottom(_introController);
    _feedbackScaleAnimation = GameAnimations.scaleIn(_feedbackController);
  }
  
  /// 开始页面进入动画
  void _startIntroAnimation() {
    GameAnimations.delayedStart(_introController, const Duration(milliseconds: 100));
  }
  
  // =============================================================================
  // 通用动画方法
  // =============================================================================
  
  /// 播放成功反馈动画
  void playSuccessAnimation() {
    if (_isShowingFeedback) return;
    
    setState(() {
      _isShowingFeedback = true;
    });
    
    _feedbackController.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isShowingFeedback = false;
          });
          _feedbackController.reset();
        }
      });
    });
  }
  
  /// 播放失败反馈动画
  void playFailureAnimation() {
    if (_isShowingFeedback) return;
    
    setState(() {
      _isShowingFeedback = true;
    });
    
    // 使用摇摆动画表示错误
    _feedbackController.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _isShowingFeedback = false;
          });
          _feedbackController.reset();
        }
      });
    });
  }
  
  /// 播放提示动画
  void playHintAnimation() {
    if (_isShowingHint) return;
    
    setState(() {
      _isShowingHint = true;
    });
    
    GameAnimations.startRepeating(_hintController);
    
    // 3秒后停止提示动画
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        GameAnimations.stopRepeating(_hintController, reset: true);
        setState(() {
          _isShowingHint = false;
        });
      }
    });
  }
  
  // =============================================================================
  // 游戏控制方法
  // =============================================================================
  
  /// 暂停游戏
  void pauseGame() {
    if (_isPaused) return;
    
    setState(() {
      _isPaused = true;
    });
    
    onGamePaused();
  }
  
  /// 恢复游戏
  void resumeGame() {
    if (!_isPaused) return;
    
    setState(() {
      _isPaused = false;
    });
    
    onGameResumed();
  }
  
  /// 重启游戏
  void restartGame() {
    _feedbackController.reset();
    _hintController.reset();
    
    setState(() {
      _isShowingFeedback = false;
      _isShowingHint = false;
      _isPaused = false;
    });
    
    onGameRestarted();
  }
  
  // =============================================================================
  // 用户交互处理
  // =============================================================================
  
  /// 处理答案提交
  void handleAnswerSubmission(dynamic answer) {
    if (_isPaused || _isShowingFeedback) return;
    
    // 调用父组件的回调
    widget.onAnswerSubmitted(answer);
  }
  
  /// 处理提示请求
  void handleHintRequest() {
    if (_isPaused || _isShowingHint) return;
    
    playHintAnimation();
    widget.onHintRequested?.call();
  }
  
  /// 处理重新开始请求
  void handleRestartRequest() {
    restartGame();
    widget.onRestart?.call();
  }
  
  /// 处理暂停请求
  void handlePauseRequest() {
    pauseGame();
    widget.onPause?.call();
  }
  
  // =============================================================================
  // 抽象方法 - 子类必须实现
  // =============================================================================
  
  /// 初始化游戏特定内容
  /// 
  /// 在通用初始化完成后调用，用于初始化游戏特定的状态和资源
  void initializeGameSpecific();
  
  /// 释放游戏特定资源
  /// 
  /// 在dispose时调用，用于释放游戏特定的资源
  void disposeGameSpecific();
  
  /// 构建游戏特定内容
  /// 
  /// 子类实现具体的游戏UI
  Widget buildGameContent(BuildContext context);
  
  /// 游戏暂停时的处理
  /// 
  /// 子类可以重写此方法来处理游戏暂停时的特定逻辑
  void onGamePaused() {}
  
  /// 游戏恢复时的处理
  /// 
  /// 子类可以重写此方法来处理游戏恢复时的特定逻辑
  void onGameResumed() {}
  
  /// 游戏重启时的处理
  /// 
  /// 子类可以重写此方法来处理游戏重启时的特定逻辑
  void onGameRestarted() {}
  
  // =============================================================================
  // 通用UI构建
  // =============================================================================
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _introController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideInAnimation,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              padding: EdgeInsets.all(UXThemeConfig.paddingM),
              child: _isInitialized
                  ? _buildGameContainer()
                  : _buildLoadingIndicator(),
            ),
          ),
        );
      },
    );
  }
  
  /// 构建游戏容器
  Widget _buildGameContainer() {
    return Stack(
      children: [
        // 主要游戏内容
        buildGameContent(context),
        
        // 暂停覆盖层
        if (_isPaused) _buildPauseOverlay(),
        
        // 反馈覆盖层
        if (_isShowingFeedback) _buildFeedbackOverlay(),
      ],
    );
  }
  
  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
  
  /// 构建暂停覆盖层
  Widget _buildPauseOverlay() {
    return Container(
              color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(UXThemeConfig.paddingL),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '游戏暂停',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              SizedBox(height: UXThemeConfig.paddingM),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ElevatedButton(
                    onPressed: resumeGame,
                    child: const Text('继续'),
                  ),
                  SizedBox(width: UXThemeConfig.paddingM),
                  ElevatedButton(
                    onPressed: handleRestartRequest,
                    child: const Text('重新开始'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建反馈覆盖层
  Widget _buildFeedbackOverlay() {
    return AnimatedBuilder(
      animation: _feedbackController,
      builder: (context, child) {
        return ScaleTransition(
          scale: _feedbackScaleAnimation,
          child: Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: const Center(
              child: Icon(
                Icons.check_circle,
                size: 100,
                color: Colors.green,
              ),
            ),
          ),
        );
      },
    );
  }
  
  // =============================================================================
  // Getter 方法
  // =============================================================================
  
  /// 获取游戏是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 获取游戏是否暂停
  bool get isPaused => _isPaused;
  
  /// 获取是否正在显示提示
  bool get isShowingHint => _isShowingHint;
  
  /// 获取是否正在显示反馈
  bool get isShowingFeedback => _isShowingFeedback;
  
  /// 获取页面进入动画控制器
  AnimationController get introController => _introController;
  
  /// 获取反馈动画控制器
  AnimationController get feedbackController => _feedbackController;
  
  /// 获取提示动画控制器
  AnimationController get hintController => _hintController;
} 
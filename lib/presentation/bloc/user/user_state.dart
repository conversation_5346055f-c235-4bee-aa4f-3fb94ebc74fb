import 'package:equatable/equatable.dart';
import '../../../domain/entities/user_profile_entity.dart';

/// 用户状态基类
abstract class UserState extends Equatable {
  const UserState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class UserInitial extends UserState {
  const UserInitial();
}

/// 加载中状态
class UserLoading extends UserState {
  const UserLoading();
}

/// 用户列表已加载状态
class UsersLoaded extends UserState {
  final List<UserProfileEntity> users;
  final UserProfileEntity? currentUser;
  final bool isNicknameAvailable;

  const UsersLoaded({
    required this.users,
    this.currentUser,
    this.isNicknameAvailable = true,
  });

  @override
  List<Object?> get props => [users, currentUser, isNicknameAvailable];

  /// 复制状态并更新部分字段
  UsersLoaded copyWith({
    List<UserProfileEntity>? users,
    UserProfileEntity? currentUser,
    bool? currentUserChanged,
    bool? isNicknameAvailable,
  }) {
    return UsersLoaded(
      users: users ?? this.users,
      currentUser: currentUserChanged == true ? currentUser : (currentUser ?? this.currentUser),
      isNicknameAvailable: isNicknameAvailable ?? this.isNicknameAvailable,
    );
  }

  /// 检查是否可以创建新用户
  bool get canCreateNewUser => users.length < 4;

  /// 检查昵称是否已存在
  bool isNicknameExists(String nickname) {
    return users.any((user) => user.nickname.toLowerCase() == nickname.toLowerCase());
  }
}

/// 用户已选择状态
class UserSelected extends UserState {
  final UserProfileEntity user;

  const UserSelected(this.user);

  @override
  List<Object> get props => [user];
}

/// 用户创建中状态
class UserCreating extends UserState {
  const UserCreating();
}

/// 用户创建成功状态
class UserCreated extends UserState {
  final UserProfileEntity user;

  const UserCreated(this.user);

  @override
  List<Object> get props => [user];
}

/// 用户更新中状态
class UserUpdating extends UserState {
  const UserUpdating();
}

/// 用户更新成功状态
class UserUpdated extends UserState {
  final UserProfileEntity user;

  const UserUpdated(this.user);

  @override
  List<Object> get props => [user];
}

/// 用户删除中状态
class UserDeleting extends UserState {
  const UserDeleting();
}

/// 用户删除成功状态
class UserDeleted extends UserState {
  final String deletedUserId;

  const UserDeleted(this.deletedUserId);

  @override
  List<Object> get props => [deletedUserId];
}

/// 昵称检查中状态
class CheckingNicknameAvailability extends UserState {
  const CheckingNicknameAvailability();
}

/// 昵称可用性检查完成状态
class NicknameAvailabilityChecked extends UserState {
  final String nickname;
  final bool isAvailable;

  const NicknameAvailabilityChecked({
    required this.nickname,
    required this.isAvailable,
  });

  @override
  List<Object> get props => [nickname, isAvailable];
}

/// 用户错误状态
class UserError extends UserState {
  final String message;
  final String? errorCode;

  const UserError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// 用户操作成功状态（通用）
class UserOperationSuccess extends UserState {
  final String message;
  final UserProfileEntity? user;

  const UserOperationSuccess({
    required this.message,
    this.user,
  });

  @override
  List<Object?> get props => [message, user];
} 
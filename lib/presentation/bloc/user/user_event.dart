import 'package:equatable/equatable.dart';

/// 用户相关事件基类
abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

/// 加载所有用户事件
class LoadUsersEvent extends UserEvent {
  const LoadUsersEvent();
}

/// 选择用户事件
class SelectUserEvent extends UserEvent {
  final String userId;

  const SelectUserEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 创建用户事件
class CreateUserEvent extends UserEvent {
  final String nickname;
  final String avatarId;

  const CreateUserEvent({
    required this.nickname,
    required this.avatarId,
  });

  @override
  List<Object> get props => [nickname, avatarId];
}

/// 更新用户事件
class UpdateUserEvent extends UserEvent {
  final String userId;
  final String? nickname;
  final String? avatarId;

  const UpdateUserEvent({
    required this.userId,
    this.nickname,
    this.avatarId,
  });

  @override
  List<Object?> get props => [userId, nickname, avatarId];
}

/// 删除用户事件
class DeleteUserEvent extends UserEvent {
  final String userId;

  const DeleteUserEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 切换用户事件
class SwitchUserEvent extends UserEvent {
  final String userId;

  const SwitchUserEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 检查昵称可用性事件
class CheckNicknameAvailabilityEvent extends UserEvent {
  final String nickname;

  const CheckNicknameAvailabilityEvent(this.nickname);

  @override
  List<Object> get props => [nickname];
}

/// 清除用户错误事件
class ClearUserErrorEvent extends UserEvent {
  const ClearUserErrorEvent();
} 
import 'package:equatable/equatable.dart';
import '../../../domain/entities/user_profile_entity.dart';

/// 设置状态基类
abstract class SettingsState extends Equatable {
  const SettingsState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class SettingsInitial extends SettingsState {
  const SettingsInitial();
}

/// 加载中状态
class SettingsLoading extends SettingsState {
  const SettingsLoading();
}

/// 设置已加载状态
class SettingsLoaded extends SettingsState {
  final UserSettingsEntity settings;
  final bool hasUnsavedChanges;

  const SettingsLoaded({
    required this.settings,
    this.hasUnsavedChanges = false,
  });

  @override
  List<Object> get props => [settings, hasUnsavedChanges];

  /// 复制状态并更新设置
  SettingsLoaded copyWith({
    UserSettingsEntity? settings,
    bool? hasUnsavedChanges,
  }) {
    return SettingsLoaded(
      settings: settings ?? this.settings,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
    );
  }

  /// 获取音乐音量百分比
  int get musicVolumePercent => (settings.musicVolume * 100).round();

  /// 获取音效音量百分比
  int get sfxVolumePercent => (settings.sfxVolume * 100).round();

  /// 获取语言显示名称
  String get languageDisplayName {
    switch (settings.language) {
      case 'zh_CN':
        return '简体中文';
      case 'zh_TW':
        return '繁體中文';
      case 'en_US':
        return 'English';
      default:
        return '简体中文';
    }
  }

  /// 获取每日时间限制显示文本
  String get dailyTimeLimitText {
    final hours = settings.dailyTimeLimitMinutes ~/ 60;
    final minutes = settings.dailyTimeLimitMinutes % 60;
    
    if (hours > 0 && minutes > 0) {
      return '$hours小时$minutes分钟';
    } else if (hours > 0) {
      return '$hours小时';
    } else {
      return '$minutes分钟';
    }
  }

  /// 检查是否有禁用的时间段
  bool get hasDisabledTimeSlots => settings.disabledTimeSlots.isNotEmpty;

  /// 获取禁用时间段的显示文本
  String get disabledTimeSlotsText {
    if (settings.disabledTimeSlots.isEmpty) {
      return '无限制';
    }
    return settings.disabledTimeSlots.join(', ');
  }
}

/// 设置保存中状态
class SettingsSaving extends SettingsState {
  final UserSettingsEntity settings;

  const SettingsSaving(this.settings);

  @override
  List<Object> get props => [settings];
}

/// 设置保存成功状态
class SettingsSaved extends SettingsState {
  final UserSettingsEntity settings;
  final String message;

  const SettingsSaved({
    required this.settings,
    this.message = '设置已保存',
  });

  @override
  List<Object> get props => [settings, message];
}

/// 设置重置中状态
class SettingsResetting extends SettingsState {
  const SettingsResetting();
}

/// 设置已重置状态
class SettingsReset extends SettingsState {
  final UserSettingsEntity settings;

  const SettingsReset(this.settings);

  @override
  List<Object> get props => [settings];
}

/// 语言切换中状态
class LanguageChanging extends SettingsState {
  final String newLanguage;
  final UserSettingsEntity settings;

  const LanguageChanging({
    required this.newLanguage,
    required this.settings,
  });

  @override
  List<Object> get props => [newLanguage, settings];
}

/// 语言已切换状态
class LanguageChanged extends SettingsState {
  final String language;
  final UserSettingsEntity settings;

  const LanguageChanged({
    required this.language,
    required this.settings,
  });

  @override
  List<Object> get props => [language, settings];
}

/// 音量调整状态
class VolumeAdjusting extends SettingsState {
  final UserSettingsEntity settings;
  final String volumeType; // 'music' 或 'sfx'
  final double volume;

  const VolumeAdjusting({
    required this.settings,
    required this.volumeType,
    required this.volume,
  });

  @override
  List<Object> get props => [settings, volumeType, volume];
}

/// 时间限制设置状态
class TimeLimitSetting extends SettingsState {
  final UserSettingsEntity settings;
  final int minutes;

  const TimeLimitSetting({
    required this.settings,
    required this.minutes,
  });

  @override
  List<Object> get props => [settings, minutes];
}

/// 设置错误状态
class SettingsError extends SettingsState {
  final String message;
  final String? errorCode;
  final UserSettingsEntity? settings;

  const SettingsError({
    required this.message,
    this.errorCode,
    this.settings,
  });

  @override
  List<Object?> get props => [message, errorCode, settings];

  /// 是否为网络错误
  bool get isNetworkError => errorCode == 'NETWORK_ERROR';

  /// 是否为权限错误
  bool get isPermissionError => errorCode == 'PERMISSION_ERROR';

  /// 是否为数据错误
  bool get isDataError => errorCode == 'DATA_ERROR';

  /// 获取错误的用户友好提示
  String get userFriendlyMessage {
    switch (errorCode) {
      case 'NETWORK_ERROR':
        return '网络连接失败，请检查网络设置';
      case 'PERMISSION_ERROR':
        return '权限不足，无法保存设置';
      case 'DATA_ERROR':
        return '数据格式错误，请重试';
      case 'STORAGE_FULL':
        return '存储空间不足，请清理后重试';
      default:
        return message;
    }
  }
} 
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'settings_event.dart';
import 'settings_state.dart';
import '../../../domain/entities/user_profile_entity.dart';
import '../../../domain/repositories/user_repository.dart';

/// 设置页面BLoC
class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  SettingsBloc({
    required UserRepository userRepository,
  })  : _userRepository = userRepository,
        super(const SettingsInitial()) {
    on<LoadSettingsEvent>(_onLoadSettings);
    on<UpdateMusicVolumeEvent>(_onUpdateMusicVolume);
    on<UpdateSfxVolumeEvent>(_onUpdateSfxVolume);
    on<ToggleVibrationEvent>(_onToggleVibration);
    on<ToggleAnimationsEvent>(_onToggleAnimations);
    on<ChangeLanguageEvent>(_onChangeLanguage);
    on<UpdateDailyTimeLimitEvent>(_onUpdateDailyTimeLimit);
    on<UpdateDisabledTimeSlotsEvent>(_onUpdateDisabledTimeSlots);
    on<ResetSettingsEvent>(_onResetSettings);
    on<SaveSettingsEvent>(_onSaveSettings);
    on<ClearSettingsErrorEvent>(_onClearSettingsError);
  }

  /// 加载设置
  Future<void> _onLoadSettings(
    LoadSettingsEvent event,
    Emitter<SettingsState> emit,
  ) async {
    try {
      emit(const SettingsLoading());

      final userResult = await _userRepository.getUserById(event.userId);
      final user = userResult.getOrThrow();
      if (user == null) {
        emit(const SettingsError(
          message: '用户不存在',
          errorCode: 'USER_NOT_FOUND',
        ));
        return;
      }

      emit(SettingsLoaded(settings: user.settings));
      _logger.i('Settings loaded for user: ${event.userId}');
    } catch (e, stackTrace) {
      _logger.e('Failed to load settings', error: e, stackTrace: stackTrace);
      emit(SettingsError(
        message: '加载设置失败: ${e.toString()}',
        errorCode: 'LOAD_ERROR',
      ));
    }
  }

  /// 更新音乐音量
  void _onUpdateMusicVolume(
    UpdateMusicVolumeEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      final updatedSettings = currentState.settings.copyWith(
        musicVolume: event.volume,
      );
      
      emit(VolumeAdjusting(
        settings: updatedSettings,
        volumeType: 'music',
        volume: event.volume,
      ));

      // 延迟一下再更新到正常状态，让用户看到音量调整效果
      Future.delayed(const Duration(milliseconds: 300), () {
        if (!isClosed) {
          emit(currentState.copyWith(
            settings: updatedSettings,
            hasUnsavedChanges: true,
          ));
        }
      });

      _logger.d('Music volume updated to: ${event.volume}');
    }
  }

  /// 更新音效音量
  void _onUpdateSfxVolume(
    UpdateSfxVolumeEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      final updatedSettings = currentState.settings.copyWith(
        sfxVolume: event.volume,
      );

      emit(VolumeAdjusting(
        settings: updatedSettings,
        volumeType: 'sfx',
        volume: event.volume,
      ));

      // 延迟一下再更新到正常状态
      Future.delayed(const Duration(milliseconds: 300), () {
        if (!isClosed) {
          emit(currentState.copyWith(
            settings: updatedSettings,
            hasUnsavedChanges: true,
          ));
        }
      });

      _logger.d('SFX volume updated to: ${event.volume}');
    }
  }

  /// 切换震动开关
  void _onToggleVibration(
    ToggleVibrationEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      final updatedSettings = currentState.settings.copyWith(
        enableVibration: event.enabled,
      );

      emit(currentState.copyWith(
        settings: updatedSettings,
        hasUnsavedChanges: true,
      ));

      _logger.d('Vibration ${event.enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// 切换动画开关
  void _onToggleAnimations(
    ToggleAnimationsEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      final updatedSettings = currentState.settings.copyWith(
        enableAnimations: event.enabled,
      );

      emit(currentState.copyWith(
        settings: updatedSettings,
        hasUnsavedChanges: true,
      ));

      _logger.d('Animations ${event.enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// 切换语言
  Future<void> _onChangeLanguage(
    ChangeLanguageEvent event,
    Emitter<SettingsState> emit,
  ) async {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      emit(LanguageChanging(
        newLanguage: event.language,
        settings: currentState.settings,
      ));

      // 模拟语言切换的延迟
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedSettings = currentState.settings.copyWith(
        language: event.language,
      );

      emit(LanguageChanged(
        language: event.language,
        settings: updatedSettings,
      ));

      // 延迟后更新到正常状态
      await Future.delayed(const Duration(milliseconds: 1000));
      
      if (!isClosed) {
        emit(currentState.copyWith(
          settings: updatedSettings,
          hasUnsavedChanges: true,
        ));
      }

      _logger.i('Language changed to: ${event.language}');
    }
  }

  /// 更新每日时间限制
  void _onUpdateDailyTimeLimit(
    UpdateDailyTimeLimitEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      emit(TimeLimitSetting(
        settings: currentState.settings,
        minutes: event.minutes,
      ));

      final updatedSettings = currentState.settings.copyWith(
        dailyTimeLimitMinutes: event.minutes,
      );

      // 延迟一下再更新到正常状态
      Future.delayed(const Duration(milliseconds: 500), () {
        if (!isClosed) {
          emit(currentState.copyWith(
            settings: updatedSettings,
            hasUnsavedChanges: true,
          ));
        }
      });

      _logger.d('Daily time limit updated to: ${event.minutes} minutes');
    }
  }

  /// 更新禁用时间段
  void _onUpdateDisabledTimeSlots(
    UpdateDisabledTimeSlotsEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      final updatedSettings = currentState.settings.copyWith(
        disabledTimeSlots: event.timeSlots,
      );

      emit(currentState.copyWith(
        settings: updatedSettings,
        hasUnsavedChanges: true,
      ));

      _logger.d('Disabled time slots updated: ${event.timeSlots}');
    }
  }

  /// 重置设置
  Future<void> _onResetSettings(
    ResetSettingsEvent event,
    Emitter<SettingsState> emit,
  ) async {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      emit(const SettingsResetting());

      // 模拟重置的延迟
      await Future.delayed(const Duration(milliseconds: 800));

      final defaultSettings = UserSettingsEntity.defaultSettings();
      
      emit(SettingsReset(defaultSettings));

      // 延迟后更新到正常状态
      await Future.delayed(const Duration(milliseconds: 1000));
      
      if (!isClosed) {
        emit(SettingsLoaded(
          settings: defaultSettings,
          hasUnsavedChanges: true,
        ));
      }

      _logger.i('Settings reset to default');
    }
  }

  /// 保存设置
  Future<void> _onSaveSettings(
    SaveSettingsEvent event,
    Emitter<SettingsState> emit,
  ) async {
    try {
      emit(SettingsSaving(event.settings));

      await _userRepository.updateUserSettings(
        userId: event.userId,
        settings: event.settings,
      );

      emit(SettingsSaved(settings: event.settings));

      // 延迟后更新到正常状态
      await Future.delayed(const Duration(milliseconds: 1500));
      
      if (!isClosed) {
        emit(SettingsLoaded(
          settings: event.settings,
          hasUnsavedChanges: false,
        ));
      }

      _logger.i('Settings saved for user: ${event.userId}');
    } catch (e, stackTrace) {
      _logger.e('Failed to save settings', error: e, stackTrace: stackTrace);
      
      String errorCode = 'SAVE_ERROR';
      if (e.toString().contains('permission')) {
        errorCode = 'PERMISSION_ERROR';
      } else if (e.toString().contains('network')) {
        errorCode = 'NETWORK_ERROR';
      } else if (e.toString().contains('storage')) {
        errorCode = 'STORAGE_FULL';
      }

      emit(SettingsError(
        message: '保存设置失败: ${e.toString()}',
        errorCode: errorCode,
        settings: event.settings,
      ));
    }
  }

  /// 清除设置错误
  void _onClearSettingsError(
    ClearSettingsErrorEvent event,
    Emitter<SettingsState> emit,
  ) {
    final currentState = state;
    if (currentState is SettingsError && currentState.settings != null) {
      emit(SettingsLoaded(
        settings: currentState.settings!,
        hasUnsavedChanges: true,
      ));
    } else {
      emit(const SettingsInitial());
    }
  }

  /// 获取当前设置（如果有的话）
  UserSettingsEntity? get currentSettings {
    final currentState = state;
    if (currentState is SettingsLoaded) {
      return currentState.settings;
    } else if (currentState is SettingsError && currentState.settings != null) {
      return currentState.settings;
    }
    return null;
  }

  /// 检查是否有未保存的更改
  bool get hasUnsavedChanges {
    final currentState = state;
    return currentState is SettingsLoaded && currentState.hasUnsavedChanges;
  }
} 
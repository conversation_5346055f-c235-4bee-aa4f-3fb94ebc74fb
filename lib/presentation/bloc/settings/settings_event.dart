import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../domain/entities/user_profile_entity.dart';

/// 设置相关事件基类
abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

/// 加载设置事件
class LoadSettingsEvent extends SettingsEvent {
  final String userId;

  const LoadSettingsEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 更新音乐音量事件
class UpdateMusicVolumeEvent extends SettingsEvent {
  final double volume;

  const UpdateMusicVolumeEvent(this.volume);

  @override
  List<Object> get props => [volume];
}

/// 更新音效音量事件
class UpdateSfxVolumeEvent extends SettingsEvent {
  final double volume;

  const UpdateSfxVolumeEvent(this.volume);

  @override
  List<Object> get props => [volume];
}

/// 切换震动开关事件
class ToggleVibrationEvent extends SettingsEvent {
  final bool enabled;

  const ToggleVibrationEvent(this.enabled);

  @override
  List<Object> get props => [enabled];
}

/// 切换动画开关事件
class ToggleAnimationsEvent extends SettingsEvent {
  final bool enabled;

  const ToggleAnimationsEvent(this.enabled);

  @override
  List<Object> get props => [enabled];
}

/// 切换语言事件
class ChangeLanguageEvent extends SettingsEvent {
  final String language;

  const ChangeLanguageEvent(this.language);

  @override
  List<Object> get props => [language];
}

/// 更新每日时间限制事件
class UpdateDailyTimeLimitEvent extends SettingsEvent {
  final int minutes;

  const UpdateDailyTimeLimitEvent(this.minutes);

  @override
  List<Object> get props => [minutes];
}

/// 更新禁用时间段事件
class UpdateDisabledTimeSlotsEvent extends SettingsEvent {
  final List<String> timeSlots;

  const UpdateDisabledTimeSlotsEvent(this.timeSlots);

  @override
  List<Object> get props => [timeSlots];
}

/// 重置设置到默认值事件
class ResetSettingsEvent extends SettingsEvent {
  const ResetSettingsEvent();
}

/// 保存设置事件
class SaveSettingsEvent extends SettingsEvent {
  final String userId;
  final UserSettingsEntity settings;

  const SaveSettingsEvent({
    required this.userId,
    required this.settings,
  });

  @override
  List<Object> get props => [userId, settings];
}

/// 清除设置错误事件
class ClearSettingsErrorEvent extends SettingsEvent {
  const ClearSettingsErrorEvent();
}

// ================================
// 主题相关事件
// ================================

/// 切换主题模式事件
class ChangeThemeModeEvent extends SettingsEvent {
  final ThemeMode themeMode;

  const ChangeThemeModeEvent(this.themeMode);

  @override
  List<Object> get props => [themeMode];
}

/// 切换主题世界事件
class ChangeThemeWorldEvent extends SettingsEvent {
  final String worldId;

  const ChangeThemeWorldEvent(this.worldId);

  @override
  List<Object> get props => [worldId];
}

/// 切换高对比度模式事件
class ToggleHighContrastEvent extends SettingsEvent {
  final bool enabled;

  const ToggleHighContrastEvent(this.enabled);

  @override
  List<Object> get props => [enabled];
}

/// 切换动画过渡事件
class ToggleThemeAnimationsEvent extends SettingsEvent {
  final bool enabled;

  const ToggleThemeAnimationsEvent(this.enabled);

  @override
  List<Object> get props => [enabled];
}

/// 重置主题设置事件
class ResetThemeSettingsEvent extends SettingsEvent {
  const ResetThemeSettingsEvent();
} 
import 'package:equatable/equatable.dart';
import '../../../domain/usecases/generate_hint_usecase.dart';

/// 提示系统状态基类
abstract class HintState extends Equatable {
  const HintState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class HintInitial extends HintState {
  const HintInitial();
}

/// 检查提示可用性中状态
class HintAvailabilityChecking extends HintState {
  const HintAvailabilityChecking();
}

/// 提示可用性已检查状态
class HintAvailabilityChecked extends HintState {
  final bool isAvailable;
  final int remainingHints;
  final int cooldownSeconds;
  final String? reason;

  const HintAvailabilityChecked({
    required this.isAvailable,
    required this.remainingHints,
    required this.cooldownSeconds,
    this.reason,
  });

  @override
  List<Object?> get props => [
    isAvailable,
    remainingHints,
    cooldownSeconds,
    reason,
  ];

  /// 是否在冷却期
  bool get isInCooldown => cooldownSeconds > 0;

  /// 是否还有剩余提示
  bool get hasRemainingHints => remainingHints > 0;
}

/// 生成提示中状态
class HintGenerating extends HintState {
  const HintGenerating();
}

/// 提示已生成状态
class HintGenerated extends HintState {
  final HintResult hintResult;

  const HintGenerated(this.hintResult);

  @override
  List<Object> get props => [hintResult];

  /// 获取提示文本
  String get hintText => hintResult.hintText;

  /// 获取提示级别
  int get hintLevel => hintResult.hintLevel;

  /// 获取提示类型
  String get hintType => hintResult.hintType;

  /// 获取剩余提示次数（暂时返回固定值）
  int get remainingHints => 2; // 从HintAvailabilityChecked状态获取

  /// 获取冷却时间（暂时返回固定值）
  int get cooldownSeconds => 30; // 从配置获取

  /// 是否为最后一个提示
  bool get isLastHint => hintResult.isLastHint;
}

/// 提示显示中状态
class HintShowing extends HintState {
  final String hintText;
  final int hintLevel;
  final String hintType;
  final int remainingHints;
  final bool isLastHint;

  const HintShowing({
    required this.hintText,
    required this.hintLevel,
    required this.hintType,
    required this.remainingHints,
    required this.isLastHint,
  });

  @override
  List<Object> get props => [
    hintText,
    hintLevel,
    hintType,
    remainingHints,
    isLastHint,
  ];

  /// 获取提示级别描述
  String get hintLevelDescription {
    switch (hintLevel) {
      case 1:
        return '轻度提示';
      case 2:
        return '中度提示';
      case 3:
        return '强度提示';
      default:
        return '提示';
    }
  }

  /// 获取提示类型描述
  String get hintTypeDescription {
    switch (hintType) {
      case 'text':
        return '文字提示';
      case 'highlight':
        return '高亮提示';
      case 'exclude':
        return '排除提示';
      case 'direction':
        return '方向提示';
      default:
        return '提示';
    }
  }
}

/// 提示隐藏状态
class HintHidden extends HintState {
  const HintHidden();
}

/// 提示冷却中状态
class HintCooldown extends HintState {
  final int remainingSeconds;
  final int totalCooldownSeconds;

  const HintCooldown({
    required this.remainingSeconds,
    required this.totalCooldownSeconds,
  });

  @override
  List<Object> get props => [remainingSeconds, totalCooldownSeconds];

  /// 获取冷却进度（0.0 - 1.0）
  double get cooldownProgress {
    if (totalCooldownSeconds == 0) return 1.0;
    return (totalCooldownSeconds - remainingSeconds) / totalCooldownSeconds;
  }

  /// 获取格式化的剩余时间
  String get formattedRemainingTime {
    final minutes = remainingSeconds ~/ 60;
    final seconds = remainingSeconds % 60;
    if (minutes > 0) {
      return '$minutes分$seconds秒';
    } else {
      return '$seconds秒';
    }
  }
}

/// 提示不可用状态
class HintUnavailable extends HintState {
  final String reason;
  final int remainingHints;
  final int cooldownSeconds;

  const HintUnavailable({
    required this.reason,
    required this.remainingHints,
    required this.cooldownSeconds,
  });

  @override
  List<Object> get props => [reason, remainingHints, cooldownSeconds];
}

/// 提示错误状态
class HintError extends HintState {
  final String message;
  final String? errorCode;

  const HintError({required this.message, this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

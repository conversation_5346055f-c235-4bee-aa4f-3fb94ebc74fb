import 'package:equatable/equatable.dart';

/// 提示系统相关事件基类
abstract class HintEvent extends Equatable {
  const HintEvent();

  @override
  List<Object?> get props => [];
}

/// 请求提示事件
class RequestHintEvent extends HintEvent {
  final String levelId;
  final String userId;
  final int currentHintLevel;
  final Map<String, dynamic>? currentGameState;

  const RequestHintEvent({
    required this.levelId,
    required this.userId,
    required this.currentHintLevel,
    this.currentGameState,
  });

  @override
  List<Object?> get props => [
    levelId,
    userId,
    currentHintLevel,
    currentGameState,
  ];
}

/// 显示提示事件
class ShowHintEvent extends HintEvent {
  final String hintText;
  final int hintLevel;

  const ShowHintEvent({required this.hintText, required this.hintLevel});

  @override
  List<Object> get props => [hintText, hintLevel];
}

/// 隐藏提示事件
class HideHintEvent extends HintEvent {
  const HideHintEvent();
}

/// 重置提示状态事件
class ResetHintEvent extends HintEvent {
  const ResetHintEvent();
}

/// 检查提示可用性事件
class CheckHintAvailabilityEvent extends HintEvent {
  final String levelId;
  final String userId;

  const CheckHintAvailabilityEvent({
    required this.levelId,
    required this.userId,
  });

  @override
  List<Object> get props => [levelId, userId];
}

/// 清除提示错误事件
class ClearHintErrorEvent extends HintEvent {
  const ClearHintErrorEvent();
}

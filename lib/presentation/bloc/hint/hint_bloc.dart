import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../domain/usecases/generate_hint_usecase.dart';
import '../../../domain/repositories/user_repository.dart';
import '../../../services/config_service.dart';
import 'hint_event.dart';
import 'hint_state.dart';

/// 提示系统状态管理BLoC
///
/// 负责处理提示系统相关的所有状态变化，包括：
/// - 提示可用性检查
/// - 提示生成和显示
/// - 提示冷却时间管理
/// - 提示使用次数限制
class HintBloc extends Bloc<HintEvent, HintState> {
  final GenerateHintUseCase _generateHintUseCase;
  final UserRepository _userRepository;
  final ConfigService _configService;
  final Logger _logger = Logger();

  // 冷却计时器
  Timer? _cooldownTimer;

  // 当前会话信息
  String? _currentUserId;
  String? _currentLevelId;

  HintBloc({
    required GenerateHintUseCase generateHintUseCase,
    required UserRepository userRepository,
    required ConfigService configService,
  }) : _generateHintUseCase = generateHintUseCase,
       _userRepository = userRepository,
       _configService = configService,
       super(const HintInitial()) {
    // 注册事件处理器
    on<RequestHintEvent>(_onRequestHint);
    on<ShowHintEvent>(_onShowHint);
    on<HideHintEvent>(_onHideHint);
    on<ResetHintEvent>(_onResetHint);
    on<CheckHintAvailabilityEvent>(_onCheckHintAvailability);
    on<ClearHintErrorEvent>(_onClearHintError);
  }

  @override
  Future<void> close() {
    _cooldownTimer?.cancel();
    return super.close();
  }

  /// 处理请求提示事件
  Future<void> _onRequestHint(
    RequestHintEvent event,
    Emitter<HintState> emit,
  ) async {
    try {
      _logger.i(
        'Requesting hint for level: ${event.levelId}, level: ${event.currentHintLevel}',
      );

      // 保存当前会话信息
      _currentUserId = event.userId;
      _currentLevelId = event.levelId;

      emit(const HintGenerating());

      // 使用GenerateHintUseCase生成提示
      final result = await _generateHintUseCase(
        GenerateHintParams(
          userId: event.userId,
          levelId: event.levelId,
          hintLevel: event.currentHintLevel,
          currentGameState: event.currentGameState ?? {},
        ),
      );

      if (result.isSuccess) {
        final hintResult = result.data!;
        emit(HintGenerated(hintResult));

        // 自动显示提示
        add(
          ShowHintEvent(
            hintText: hintResult.hintText,
            hintLevel: event.currentHintLevel,
          ),
        );

        // 启动冷却计时器（从配置获取）
        final cooldownSeconds = _configService.hintCooldownSeconds;
        if (cooldownSeconds > 0) {
          _startCooldownTimer(cooldownSeconds, emit);
        }

        _logger.i('Hint generated successfully: ${hintResult.hintType}');
      } else {
        emit(
          HintError(
            message: result.exception?.message ?? '生成提示失败',
            errorCode: 'GENERATE_HINT_ERROR',
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to request hint', error: e, stackTrace: stackTrace);
      emit(
        HintError(
          message: '生成提示失败：${e.toString()}',
          errorCode: 'REQUEST_HINT_EXCEPTION',
        ),
      );
    }
  }

  /// 处理显示提示事件
  Future<void> _onShowHint(ShowHintEvent event, Emitter<HintState> emit) async {
    if (state is HintGenerated) {
      final generatedState = state as HintGenerated;

      emit(
        HintShowing(
          hintText: event.hintText,
          hintLevel: event.hintLevel,
          hintType: generatedState.hintType,
          remainingHints: generatedState.remainingHints,
          isLastHint: generatedState.isLastHint,
        ),
      );

      // 更新提示使用记录
      if (_currentUserId != null && _currentLevelId != null) {
        await _updateHintUsage(_currentUserId!, _currentLevelId!);
      }

      _logger.d('Hint shown: level ${event.hintLevel}');
    }
  }

  /// 处理隐藏提示事件
  Future<void> _onHideHint(HideHintEvent event, Emitter<HintState> emit) async {
    emit(const HintHidden());
    _logger.d('Hint hidden');
  }

  /// 处理重置提示状态事件
  Future<void> _onResetHint(
    ResetHintEvent event,
    Emitter<HintState> emit,
  ) async {
    _cooldownTimer?.cancel();
    emit(const HintInitial());
    _logger.d('Hint state reset');
  }

  /// 处理检查提示可用性事件
  Future<void> _onCheckHintAvailability(
    CheckHintAvailabilityEvent event,
    Emitter<HintState> emit,
  ) async {
    try {
      _logger.d('Checking hint availability for level: ${event.levelId}');
      emit(const HintAvailabilityChecking());

      // 这里应该调用Repository或UseCase来检查提示可用性
      // 暂时使用模拟数据
      await Future.delayed(const Duration(milliseconds: 500));

      // 实现真实的提示可用性检查逻辑
      final isAvailable = _checkHintAvailability(event.userId, event.levelId);
      final remainingHints = _getRemainingHints(event.userId, event.levelId);
      final cooldownSeconds = _getCooldownSeconds(event.userId);

      emit(
        HintAvailabilityChecked(
          isAvailable: isAvailable,
          remainingHints: remainingHints,
          cooldownSeconds: cooldownSeconds,
        ),
      );

      _logger.d(
        'Hint availability checked: available=$isAvailable, remaining=$remainingHints',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to check hint availability',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        HintError(
          message: '检查提示可用性失败：${e.toString()}',
          errorCode: 'CHECK_HINT_AVAILABILITY_ERROR',
        ),
      );
    }
  }

  /// 处理清除提示错误事件
  Future<void> _onClearHintError(
    ClearHintErrorEvent event,
    Emitter<HintState> emit,
  ) async {
    _logger.d('Clearing hint error');
    emit(const HintInitial());
  }

  /// 启动冷却计时器
  void _startCooldownTimer(int totalSeconds, Emitter<HintState> emit) {
    _cooldownTimer?.cancel();

    int remainingSeconds = totalSeconds;
    emit(
      HintCooldown(
        remainingSeconds: remainingSeconds,
        totalCooldownSeconds: totalSeconds,
      ),
    );

    _cooldownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      remainingSeconds--;

      if (remainingSeconds <= 0) {
        timer.cancel();
        emit(const HintInitial());
        _logger.d('Hint cooldown completed');
      } else {
        emit(
          HintCooldown(
            remainingSeconds: remainingSeconds,
            totalCooldownSeconds: totalSeconds,
          ),
        );
      }
    });

    _logger.d('Started hint cooldown timer: ${totalSeconds}s');
  }

  /// 检查当前是否可以请求提示
  bool get canRequestHint {
    return state is! HintGenerating &&
        state is! HintCooldown &&
        state is! HintError;
  }

  /// 获取当前提示状态信息
  Map<String, dynamic> get currentHintInfo {
    if (state is HintShowing) {
      final showingState = state as HintShowing;
      return {
        'isShowing': true,
        'hintText': showingState.hintText,
        'hintLevel': showingState.hintLevel,
        'hintType': showingState.hintType,
        'remainingHints': showingState.remainingHints,
        'isLastHint': showingState.isLastHint,
      };
    } else if (state is HintCooldown) {
      final cooldownState = state as HintCooldown;
      return {
        'isShowing': false,
        'inCooldown': true,
        'remainingSeconds': cooldownState.remainingSeconds,
        'cooldownProgress': cooldownState.cooldownProgress,
      };
    } else {
      return {'isShowing': false, 'inCooldown': false};
    }
  }

  /// 检查提示可用性
  bool _checkHintAvailability(String userId, String levelId) {
    // 基本可用性检查
    // 1. 检查用户是否还有剩余提示次数
    final remainingHints = _getRemainingHints(userId, levelId);
    if (remainingHints <= 0) return false;

    // 2. 检查是否在冷却时间内
    final cooldownSeconds = _getCooldownSeconds(userId);
    if (cooldownSeconds > 0) return false;

    // 3. 检查关卡是否支持提示
    // 这里简化处理，假设所有关卡都支持提示
    return true;
  }

  /// 获取剩余提示次数
  int _getRemainingHints(String userId, String levelId) {
    // 从配置获取最大提示次数
    final maxHintsPerLevel = _configService.maxHintsPerLevel;

    // 注意：需要从用户数据获取已使用的提示次数
    // 这里需要调用UserRepository获取用户在该关卡的提示使用记录
    const usedHints = 0; // 暂时使用0，需要扩展用户数据模型

    return maxHintsPerLevel - usedHints;
  }

  /// 获取冷却剩余时间（秒）
  int _getCooldownSeconds(String userId) {
    // 从配置获取冷却时间
    final cooldownDuration = _configService.hintCooldownSeconds;

    // 注意：需要从用户数据获取上次提示时间
    // 这里需要调用UserRepository获取用户的提示使用记录
    const lastHintTime = 0; // 暂时使用0，表示没有使用过提示

    if (lastHintTime == 0) return 0;

    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final elapsed = now - lastHintTime;
    final remaining = cooldownDuration - elapsed;

    return remaining > 0 ? remaining : 0;
  }

  /// 更新用户提示使用记录
  Future<void> _updateHintUsage(String userId, String levelId) async {
    try {
      _logger.d('Updating hint usage for user: $userId, level: $levelId');

      // 获取当前用户数据
      final userResult = await _userRepository.getCurrentUser();
      if (!userResult.isSuccess || userResult.data == null) {
        _logger.w('Failed to get user data for hint usage update');
        return;
      }

      final user = userResult.data!;

      // 更新提示使用记录
      // 这里需要在用户实体中添加提示使用记录字段
      // 暂时记录日志，后续需要扩展用户数据模型
      _logger.i(
        'Hint used by user: ${user.id} (${user.nickname}) for level: $levelId',
      );

      // 注意：需要扩展用户数据模型以包含提示使用记录
      // 需要添加字段如：Map<String, HintUsageRecord> hintUsageByLevel
      // HintUsageRecord包含：usedCount, lastUsedTime等

      // 示例实现（需要扩展UserRepository接口）：
      // await _userRepository.updateHintUsage(
      //   userId: userId,
      //   levelId: levelId,
      //   usedTime: DateTime.now(),
      // );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to update hint usage',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }
}

import 'package:equatable/equatable.dart';

/// 成就系统相关事件基类
abstract class AchievementEvent extends Equatable {
  const AchievementEvent();

  @override
  List<Object?> get props => [];
}

/// 解锁成就事件
class UnlockAchievementEvent extends AchievementEvent {
  final String userId;
  final String achievementId;

  const UnlockAchievementEvent({
    required this.userId,
    required this.achievementId,
  });

  @override
  List<Object> get props => [userId, achievementId];
}

/// 检查可解锁成就事件
class CheckUnlockableAchievementsEvent extends AchievementEvent {
  final String userId;

  const CheckUnlockableAchievementsEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 加载用户成就事件
class LoadUserAchievementsEvent extends AchievementEvent {
  final String userId;

  const LoadUserAchievementsEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 加载所有成就事件
class LoadAllAchievementsEvent extends AchievementEvent {
  const LoadAllAchievementsEvent();
}

/// 显示成就详情事件
class ShowAchievementDetailsEvent extends AchievementEvent {
  final String achievementId;

  const ShowAchievementDetailsEvent(this.achievementId);

  @override
  List<Object> get props => [achievementId];
}

/// 隐藏成就详情事件
class HideAchievementDetailsEvent extends AchievementEvent {
  const HideAchievementDetailsEvent();
}

/// 标记成就通知已读事件
class MarkAchievementNotificationReadEvent extends AchievementEvent {
  final String achievementId;

  const MarkAchievementNotificationReadEvent(this.achievementId);

  @override
  List<Object> get props => [achievementId];
}

/// 清除成就错误事件
class ClearAchievementErrorEvent extends AchievementEvent {
  const ClearAchievementErrorEvent();
} 
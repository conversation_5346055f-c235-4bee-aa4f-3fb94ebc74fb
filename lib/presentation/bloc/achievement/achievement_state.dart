import 'package:equatable/equatable.dart';
import '../../../domain/usecases/unlock_achievement_usecase.dart';
import '../../../services/achievement_service.dart';

/// 成就系统状态基类
abstract class AchievementState extends Equatable {
  const AchievementState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class AchievementInitial extends AchievementState {
  const AchievementInitial();
}

/// 加载成就中状态
class AchievementLoading extends AchievementState {
  const AchievementLoading();
}

/// 成就列表已加载状态
class AchievementsLoaded extends AchievementState {
  final List<AchievementInfo> allAchievements;
  final List<AchievementInfo> userAchievements;
  final List<AchievementInfo> unlockedAchievements;
  final List<AchievementInfo> lockedAchievements;

  const AchievementsLoaded({
    required this.allAchievements,
    required this.userAchievements,
    required this.unlockedAchievements,
    required this.lockedAchievements,
  });

  @override
  List<Object> get props => [
    allAchievements,
    userAchievements,
    unlockedAchievements,
    lockedAchievements,
  ];

  /// 获取成就完成度百分比
  double get completionPercentage {
    if (allAchievements.isEmpty) return 0.0;
    return unlockedAchievements.length / allAchievements.length;
  }

  /// 获取总的成就点数
  int get totalAchievementPoints {
    return unlockedAchievements.fold(
      0,
      (sum, achievement) => sum + achievement.rewardPoints,
    );
  }

  /// 按稀有度分组的成就
  Map<AchievementRarity, List<AchievementInfo>> get achievementsByRarity {
    final Map<AchievementRarity, List<AchievementInfo>> grouped = {};
    for (final achievement in allAchievements) {
      grouped.putIfAbsent(achievement.rarity, () => []).add(achievement);
    }
    return grouped;
  }

  /// 获取指定稀有度的解锁成就数量
  int getUnlockedCountByRarity(AchievementRarity rarity) {
    return unlockedAchievements.where((a) => a.rarity == rarity).length;
  }
}

/// 解锁成就中状态
class AchievementUnlocking extends AchievementState {
  final String achievementId;

  const AchievementUnlocking(this.achievementId);

  @override
  List<Object> get props => [achievementId];
}

/// 成就解锁成功状态
class AchievementUnlocked extends AchievementState {
  final UnlockAchievementResult unlockResult;

  const AchievementUnlocked(this.unlockResult);

  @override
  List<Object> get props => [unlockResult];

  /// 获取解锁的成就名称
  String get achievementName => unlockResult.achievement;

  /// 获取奖励点数
  int get rewardPoints => unlockResult.totalPointsEarned;

  /// 是否有连锁成就
  bool get hasChainedAchievements =>
      unlockResult.triggeredChainAchievements.isNotEmpty;

  /// 获取连锁成就列表
  List<String> get chainedAchievements =>
      unlockResult.triggeredChainAchievements;
}

/// 检查可解锁成就中状态
class CheckingUnlockableAchievements extends AchievementState {
  const CheckingUnlockableAchievements();
}

/// 可解锁成就检查完成状态
class UnlockableAchievementsChecked extends AchievementState {
  final List<String> unlockableAchievementIds;
  final int totalUnlockableCount;

  const UnlockableAchievementsChecked({
    required this.unlockableAchievementIds,
    required this.totalUnlockableCount,
  });

  @override
  List<Object> get props => [unlockableAchievementIds, totalUnlockableCount];

  /// 是否有可解锁的成就
  bool get hasUnlockableAchievements => unlockableAchievementIds.isNotEmpty;
}

/// 成就详情显示状态
class AchievementDetailsShowing extends AchievementState {
  final AchievementInfo achievement;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final double progress;

  const AchievementDetailsShowing({
    required this.achievement,
    required this.isUnlocked,
    this.unlockedAt,
    required this.progress,
  });

  @override
  List<Object?> get props => [achievement, isUnlocked, unlockedAt, progress];

  /// 获取进度百分比文本
  String get progressText {
    if (isUnlocked) return '已解锁';
    return '${(progress * 100).toInt()}%';
  }

  /// 获取解锁时间文本
  String? get unlockedTimeText {
    if (unlockedAt == null) return null;
    final now = DateTime.now();
    final difference = now.difference(unlockedAt!);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前解锁';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前解锁';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前解锁';
    } else {
      return '刚刚解锁';
    }
  }
}

/// 成就详情隐藏状态
class AchievementDetailsHidden extends AchievementState {
  const AchievementDetailsHidden();
}

/// 成就通知状态
class AchievementNotification extends AchievementState {
  final List<AchievementInfo> newAchievements;
  final int totalRewardPoints;
  final bool isRead;

  const AchievementNotification({
    required this.newAchievements,
    required this.totalRewardPoints,
    this.isRead = false,
  });

  @override
  List<Object> get props => [newAchievements, totalRewardPoints, isRead];

  /// 是否有多个成就
  bool get hasMultipleAchievements => newAchievements.length > 1;

  /// 获取主要成就（第一个）
  AchievementInfo get primaryAchievement => newAchievements.first;

  /// 获取通知标题
  String get notificationTitle {
    if (hasMultipleAchievements) {
      return '解锁了 ${newAchievements.length} 个成就！';
    } else {
      return '解锁成就：${primaryAchievement.title}';
    }
  }

  /// 获取通知内容
  String get notificationContent {
    if (hasMultipleAchievements) {
      return '获得了 $totalRewardPoints 点成就点数';
    } else {
      return primaryAchievement.description;
    }
  }
}

/// 成就错误状态
class AchievementError extends AchievementState {
  final String message;
  final String? errorCode;

  const AchievementError({required this.message, this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

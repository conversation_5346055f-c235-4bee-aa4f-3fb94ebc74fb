import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../domain/usecases/unlock_achievement_usecase.dart';
import '../../../domain/repositories/user_repository.dart';
import '../../../domain/entities/user_profile_entity.dart';
import '../../../services/achievement_service.dart';
import '../../../core/exceptions/app_exceptions.dart';
import 'achievement_event.dart';
import 'achievement_state.dart';

/// 成就系统状态管理BLoC
///
/// 负责处理成就系统相关的所有状态变化，包括：
/// - 成就解锁和验证
/// - 成就列表加载和管理
/// - 成就进度跟踪
/// - 成就通知和详情显示
class AchievementBloc extends Bloc<AchievementEvent, AchievementState> {
  final UnlockAchievementUseCase _unlockAchievementUseCase;
  final UserRepository _userRepository;
  final AchievementService _achievementService;
  final Logger _logger = Logger();

  AchievementBloc({
    required UnlockAchievementUseCase unlockAchievementUseCase,
    required UserRepository userRepository,
    required AchievementService achievementService,
  }) : _unlockAchievementUseCase = unlockAchievementUseCase,
       _userRepository = userRepository,
       _achievementService = achievementService,
       super(const AchievementInitial()) {
    // 注册事件处理器
    on<UnlockAchievementEvent>(_onUnlockAchievement);
    on<CheckUnlockableAchievementsEvent>(_onCheckUnlockableAchievements);
    on<LoadUserAchievementsEvent>(_onLoadUserAchievements);
    on<LoadAllAchievementsEvent>(_onLoadAllAchievements);
    on<ShowAchievementDetailsEvent>(_onShowAchievementDetails);
    on<HideAchievementDetailsEvent>(_onHideAchievementDetails);
    on<MarkAchievementNotificationReadEvent>(
      _onMarkAchievementNotificationRead,
    );
    on<ClearAchievementErrorEvent>(_onClearAchievementError);
  }

  /// 处理解锁成就事件
  Future<void> _onUnlockAchievement(
    UnlockAchievementEvent event,
    Emitter<AchievementState> emit,
  ) async {
    try {
      _logger.i(
        'Unlocking achievement: ${event.achievementId} for user: ${event.userId}',
      );
      emit(AchievementUnlocking(event.achievementId));

      // 使用UnlockAchievementUseCase解锁成就
      final result = await _unlockAchievementUseCase(
        UnlockAchievementParams(
          userId: event.userId,
          achievementId: event.achievementId,
        ),
      );

      if (result.isSuccess) {
        final unlockResult = result.data!;
        emit(AchievementUnlocked(unlockResult));

        // 显示成就通知
        final achievementInfo = _achievementService.getAchievementById(
          unlockResult.achievementId,
        );
        if (achievementInfo != null) {
          _showAchievementNotification(
            [achievementInfo],
            unlockResult.totalPointsEarned,
            emit,
          );
        }

        _logger.i('Achievement unlocked successfully: ${event.achievementId}');
      } else {
        emit(
          AchievementError(
            message: '解锁成就失败',
            errorCode: 'UNLOCK_ACHIEVEMENT_ERROR',
          ),
        );
      }
    } on AchievementAlreadyUnlockedException catch (e, stackTrace) {
      _logger.w(
        'Achievement already unlocked',
        error: e,
        stackTrace: stackTrace,
      );
      emit(AchievementError(message: e.message, errorCode: e.errorCode));
    } on AchievementNotFoundException catch (e, stackTrace) {
      _logger.e('Achievement not found', error: e, stackTrace: stackTrace);
      emit(AchievementError(message: e.message, errorCode: e.errorCode));
    } on UserNotFoundException catch (e, stackTrace) {
      _logger.e('User not found', error: e, stackTrace: stackTrace);
      emit(AchievementError(message: e.message, errorCode: e.errorCode));
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to unlock achievement',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        AchievementError(
          message: '解锁成就失败：${e.toString()}',
          errorCode: 'UNLOCK_ACHIEVEMENT_EXCEPTION',
        ),
      );
    }
  }

  /// 处理检查可解锁成就事件
  Future<void> _onCheckUnlockableAchievements(
    CheckUnlockableAchievementsEvent event,
    Emitter<AchievementState> emit,
  ) async {
    try {
      _logger.i('Checking unlockable achievements for user: ${event.userId}');
      emit(const CheckingUnlockableAchievements());

      // 从Repository获取用户数据
      final userResult = await _userRepository.getUserById(event.userId);
      final userNullable = userResult.getOrThrow();
      if (userNullable == null) {
        emit(
          const AchievementError(message: '用户不存在', errorCode: 'USER_NOT_FOUND'),
        );
        return;
      }
      final user = userNullable;

      // 构建完整的用户统计数据
      final userStats = <String, dynamic>{
        // 基础完成统计
        'completedPuzzles': user.levelProgress.values
            .where((progress) => progress.completed)
            .length,
        'totalStars': user.totalStars,
        'totalScore': user.totalScore,
        'totalPoints': user.totalPoints,

        // 技能相关统计
        'skillPoints': user.skillPoints,
        'skillLevels': user.skillPoints.map(
          (skill, points) => MapEntry(skill, (points ~/ 100) + 1),
        ),

        // 时间相关统计
        'totalPlayTimeMinutes': user.totalPlayTimeMinutes,
        'fastestTime': _calculateFastestTime(user.levelProgress),

        // 成就相关统计
        'perfectScores': _calculatePerfectScores(user.levelProgress),
        'puzzlesCompletedWithoutHints': _calculateNoHintCompletions(
          user.levelProgress,
        ),
        'consecutiveCompleted': _calculateConsecutiveCompletions(
          user.levelProgress,
        ),

        // 其他统计
        'averageScore': _calculateAverageScore(user.levelProgress),
        'totalAttempts': _calculateTotalAttempts(user.levelProgress),
        'completedLevels': user.levelProgress.keys
            .where((levelId) => user.levelProgress[levelId]?.completed == true)
            .toList(),
      };

      // 获取可解锁的成就
      final unlockableIds = _achievementService.getUnlockableAchievements(
        user.unlockedAchievements,
        userStats,
      );

      emit(
        UnlockableAchievementsChecked(
          unlockableAchievementIds: unlockableIds,
          totalUnlockableCount: unlockableIds.length,
        ),
      );

      _logger.i('Found ${unlockableIds.length} unlockable achievements');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to check unlockable achievements',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        AchievementError(
          message: '检查可解锁成就失败：${e.toString()}',
          errorCode: 'CHECK_UNLOCKABLE_ACHIEVEMENTS_EXCEPTION',
        ),
      );
    }
  }

  /// 处理加载用户成就事件
  Future<void> _onLoadUserAchievements(
    LoadUserAchievementsEvent event,
    Emitter<AchievementState> emit,
  ) async {
    try {
      _logger.i('Loading user achievements for: ${event.userId}');
      emit(const AchievementLoading());

      // 从Repository加载用户数据
      final userResult = await _userRepository.getUserById(event.userId);
      final userNullable = userResult.getOrThrow();
      if (userNullable == null) {
        emit(
          const AchievementError(message: '用户不存在', errorCode: 'USER_NOT_FOUND'),
        );
        return;
      }
      final user = userNullable;

      // 获取所有成就
      final allAchievements = _achievementService.getAllAchievements();

      // 获取用户已解锁的成就（带解锁时间）
      final unlockedAchievements = _achievementService
          .getUserUnlockedAchievements(user.unlockedAchievements)
          .map(
            (achievement) => achievement.copyWith(
              unlockedAt:
                  achievement.unlockedAt ?? DateTime.now(), // 使用成就的解锁时间或当前时间
            ),
          )
          .toList();

      // 获取用户未解锁的成就
      final lockedAchievements = _achievementService.getUserLockedAchievements(
        user.unlockedAchievements,
      );

      final userAchievements = unlockedAchievements;

      emit(
        AchievementsLoaded(
          allAchievements: allAchievements,
          userAchievements: userAchievements,
          unlockedAchievements: unlockedAchievements,
          lockedAchievements: lockedAchievements,
        ),
      );

      _logger.i(
        'User achievements loaded: ${unlockedAchievements.length}/${allAchievements.length}',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to load user achievements',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        AchievementError(
          message: '加载用户成就失败：${e.toString()}',
          errorCode: 'LOAD_USER_ACHIEVEMENTS_ERROR',
        ),
      );
    }
  }

  /// 处理加载所有成就事件
  Future<void> _onLoadAllAchievements(
    LoadAllAchievementsEvent event,
    Emitter<AchievementState> emit,
  ) async {
    try {
      _logger.i('Loading all achievements');
      emit(const AchievementLoading());

      // 从AchievementService获取所有成就
      final allAchievements = _achievementService.getAllAchievements();

      emit(
        AchievementsLoaded(
          allAchievements: allAchievements,
          userAchievements: [],
          unlockedAchievements: [],
          lockedAchievements: allAchievements,
        ),
      );

      _logger.i('All achievements loaded: ${allAchievements.length}');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to load all achievements',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        AchievementError(
          message: '加载成就列表失败：${e.toString()}',
          errorCode: 'LOAD_ALL_ACHIEVEMENTS_ERROR',
        ),
      );
    }
  }

  /// 处理显示成就详情事件
  Future<void> _onShowAchievementDetails(
    ShowAchievementDetailsEvent event,
    Emitter<AchievementState> emit,
  ) async {
    try {
      _logger.d('Showing achievement details: ${event.achievementId}');

      // 从AchievementService获取成就详情
      final achievement = _achievementService.getAchievementById(
        event.achievementId,
      );
      if (achievement == null) {
        throw AchievementNotFoundException(achievementId: event.achievementId);
      }

      // 获取用户数据来判断解锁状态和进度
      final userResult = await _userRepository.getCurrentUser();
      bool isUnlocked = false;
      double progress = 0.0;

      if (userResult.isSuccess && userResult.data != null) {
        final user = userResult.data!;
        isUnlocked = user.unlockedAchievements.contains(event.achievementId);

        // 计算成就进度
        if (isUnlocked) {
          progress = 1.0;
        } else {
          // 构建用户统计数据来计算进度
          final userStats = _buildUserStats(user);
          progress = _calculateAchievementProgress(achievement, userStats);
        }
      }

      emit(
        AchievementDetailsShowing(
          achievement: achievement,
          isUnlocked: isUnlocked,
          unlockedAt: achievement.unlockedAt,
          progress: progress,
        ),
      );

      _logger.d('Achievement details shown: ${achievement.title}');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to show achievement details',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        AchievementError(
          message: '显示成就详情失败：${e.toString()}',
          errorCode: 'SHOW_ACHIEVEMENT_DETAILS_ERROR',
        ),
      );
    }
  }

  /// 处理隐藏成就详情事件
  Future<void> _onHideAchievementDetails(
    HideAchievementDetailsEvent event,
    Emitter<AchievementState> emit,
  ) async {
    _logger.d('Hiding achievement details');
    emit(const AchievementDetailsHidden());
  }

  /// 处理标记成就通知已读事件
  Future<void> _onMarkAchievementNotificationRead(
    MarkAchievementNotificationReadEvent event,
    Emitter<AchievementState> emit,
  ) async {
    if (state is AchievementNotification) {
      final notificationState = state as AchievementNotification;
      emit(
        AchievementNotification(
          newAchievements: notificationState.newAchievements,
          totalRewardPoints: notificationState.totalRewardPoints,
          isRead: true,
        ),
      );
      _logger.d(
        'Achievement notification marked as read: ${event.achievementId}',
      );
    }
  }

  /// 处理清除成就错误事件
  Future<void> _onClearAchievementError(
    ClearAchievementErrorEvent event,
    Emitter<AchievementState> emit,
  ) async {
    _logger.d('Clearing achievement error');
    emit(const AchievementInitial());
  }

  /// 显示成就通知
  void _showAchievementNotification(
    List<AchievementInfo> achievements,
    int totalRewardPoints,
    Emitter<AchievementState> emit,
  ) {
    emit(
      AchievementNotification(
        newAchievements: achievements,
        totalRewardPoints: totalRewardPoints,
        isRead: false,
      ),
    );
  }

  /// 获取当前成就统计信息
  Map<String, dynamic> get achievementStats {
    if (state is AchievementsLoaded) {
      final loadedState = state as AchievementsLoaded;
      return {
        'total': loadedState.allAchievements.length,
        'unlocked': loadedState.unlockedAchievements.length,
        'locked': loadedState.lockedAchievements.length,
        'completionPercentage': loadedState.completionPercentage,
        'totalPoints': loadedState.totalAchievementPoints,
      };
    }
    return {
      'total': 0,
      'unlocked': 0,
      'locked': 0,
      'completionPercentage': 0.0,
      'totalPoints': 0,
    };
  }

  /// 计算最快完成时间（秒）
  int _calculateFastestTime(Map<String, LevelProgressEntity> levelProgress) {
    int fastestTime = 999; // 默认很大的值

    for (final progress in levelProgress.values) {
      if (progress.completed && progress.bestTimeSeconds > 0) {
        if (progress.bestTimeSeconds < fastestTime) {
          fastestTime = progress.bestTimeSeconds;
        }
      }
    }

    return fastestTime == 999 ? 0 : fastestTime;
  }

  /// 计算完美分数的数量
  int _calculatePerfectScores(Map<String, LevelProgressEntity> levelProgress) {
    int perfectCount = 0;

    for (final progress in levelProgress.values) {
      if (progress.completed && progress.bestScore >= 100) {
        perfectCount++;
      }
    }

    return perfectCount;
  }

  /// 计算无提示完成的数量
  int _calculateNoHintCompletions(
    Map<String, LevelProgressEntity> levelProgress,
  ) {
    int noHintCount = 0;

    for (final progress in levelProgress.values) {
      if (progress.completed && progress.hintsUsed == 0) {
        noHintCount++;
      }
    }

    return noHintCount;
  }

  /// 计算连续完成的数量
  int _calculateConsecutiveCompletions(
    Map<String, LevelProgressEntity> levelProgress,
  ) {
    // 这里简化处理，返回已完成的关卡数
    // 实际应该根据完成时间顺序计算连续性
    return levelProgress.values.where((progress) => progress.completed).length;
  }

  /// 计算平均分数
  double _calculateAverageScore(
    Map<String, LevelProgressEntity> levelProgress,
  ) {
    final completedLevels = levelProgress.values
        .where((progress) => progress.completed)
        .toList();

    if (completedLevels.isEmpty) return 0.0;

    final totalScore = completedLevels
        .map((progress) => progress.bestScore)
        .reduce((a, b) => a + b);

    return totalScore / completedLevels.length;
  }

  /// 计算总尝试次数
  int _calculateTotalAttempts(Map<String, LevelProgressEntity> levelProgress) {
    return levelProgress.values
        .map((progress) => progress.attempts)
        .reduce((a, b) => a + b);
  }

  /// 构建用户统计数据（复用之前的逻辑）
  Map<String, dynamic> _buildUserStats(UserProfileEntity user) {
    return {
      // 基础完成统计
      'completedPuzzles': user.levelProgress.values
          .where((progress) => progress.completed)
          .length,
      'totalStars': user.totalStars,
      'totalScore': user.totalScore,
      'totalPoints': user.totalPoints,

      // 技能相关统计
      'skillPoints': user.skillPoints,
      'skillLevels': user.skillPoints.map(
        (skill, points) => MapEntry(skill, (points ~/ 100) + 1),
      ),

      // 时间相关统计
      'totalPlayTimeMinutes': user.totalPlayTimeMinutes,
      'fastestTime': _calculateFastestTime(user.levelProgress),

      // 成就相关统计
      'perfectScores': _calculatePerfectScores(user.levelProgress),
      'puzzlesCompletedWithoutHints': _calculateNoHintCompletions(
        user.levelProgress,
      ),
      'consecutiveCompleted': _calculateConsecutiveCompletions(
        user.levelProgress,
      ),

      // 其他统计
      'averageScore': _calculateAverageScore(user.levelProgress),
      'totalAttempts': _calculateTotalAttempts(user.levelProgress),
      'completedLevels': user.levelProgress.keys
          .where((levelId) => user.levelProgress[levelId]?.completed == true)
          .toList(),
    };
  }

  /// 计算成就进度（0.0 - 1.0）
  double _calculateAchievementProgress(
    AchievementInfo achievement,
    Map<String, dynamic> userStats,
  ) {
    // 如果成就已经可以解锁，返回1.0
    if (_achievementService.canUnlockAchievement(achievement.id, userStats)) {
      return 1.0;
    }

    // 根据成就要求计算进度
    if (achievement.requirements.isEmpty) return 0.0;

    double totalProgress = 0.0;
    for (final requirement in achievement.requirements) {
      totalProgress += _calculateRequirementProgress(requirement, userStats);
    }

    // 返回平均进度
    return (totalProgress / achievement.requirements.length).clamp(0.0, 1.0);
  }

  /// 计算单个要求的进度
  double _calculateRequirementProgress(
    String requirement,
    Map<String, dynamic> userStats,
  ) {
    switch (requirement) {
      // 完成数量要求
      case 'complete_any_puzzle':
        final completed = userStats['completedPuzzles'] as int? ?? 0;
        return completed > 0 ? 1.0 : 0.0;

      case 'complete_10_puzzles':
        final completed = userStats['completedPuzzles'] as int? ?? 0;
        return (completed / 10).clamp(0.0, 1.0);

      case 'complete_50_puzzles':
        final completed = userStats['completedPuzzles'] as int? ?? 0;
        return (completed / 50).clamp(0.0, 1.0);

      case 'complete_100_puzzles':
        final completed = userStats['completedPuzzles'] as int? ?? 0;
        return (completed / 100).clamp(0.0, 1.0);

      // 速度要求
      case 'complete_puzzle_under_30s':
        final fastestTime = userStats['fastestTime'] as int? ?? 999;
        return fastestTime <= 30 ? 1.0 : (30.0 / fastestTime).clamp(0.0, 1.0);

      case 'complete_puzzle_under_15s':
        final fastestTime = userStats['fastestTime'] as int? ?? 999;
        return fastestTime <= 15 ? 1.0 : (15.0 / fastestTime).clamp(0.0, 1.0);

      // 技能等级要求
      case 'pattern_skill_level_5':
        final skillLevels = userStats['skillLevels'] as Map<String, int>? ?? {};
        final level = skillLevels['pattern'] ?? 1;
        return (level / 5).clamp(0.0, 1.0);

      case 'spatial_skill_level_5':
        final skillLevels = userStats['skillLevels'] as Map<String, int>? ?? {};
        final level = skillLevels['spatial'] ?? 1;
        return (level / 5).clamp(0.0, 1.0);

      // 默认情况
      default:
        return 0.0;
    }
  }
}

import 'package:equatable/equatable.dart';
import '../../../domain/entities/puzzle_entity.dart';
import '../../../domain/entities/user_profile_entity.dart';
import '../../../domain/usecases/play_puzzle_usecase.dart';

/// 谜题游戏状态基类
abstract class PuzzleState extends Equatable {
  const PuzzleState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class PuzzleInitial extends PuzzleState {
  const PuzzleInitial();
}

/// 加载谜题列表中状态
class PuzzleListLoading extends PuzzleState {
  const PuzzleListLoading();
}

/// 谜题列表已加载状态
class PuzzleListLoaded extends PuzzleState {
  final List<PuzzleEntity> puzzles;
  final List<PuzzleEntity> recommendedPuzzles;

  const PuzzleListLoaded({
    required this.puzzles,
    this.recommendedPuzzles = const [],
  });

  @override
  List<Object> get props => [puzzles, recommendedPuzzles];
}

/// 谜题游戏加载中状态
class PuzzleGameLoading extends PuzzleState {
  const PuzzleGameLoading();
}

/// 谜题游戏已加载状态（准备开始）
class PuzzleGameLoaded extends PuzzleState {
  final PuzzleGameResult gameResult;

  const PuzzleGameLoaded(this.gameResult);

  @override
  List<Object> get props => [gameResult];

  /// 获取谜题
  PuzzleEntity get puzzle => gameResult.puzzle;

  /// 获取用户
  UserProfileEntity get user => gameResult.user;

  /// 是否可以恢复游戏
  bool get canResume => gameResult.canResume;
}

/// 谜题游戏进行中状态
class PuzzleGameInProgress extends PuzzleState {
  final PuzzleEntity puzzle;
  final UserProfileEntity user;
  final int currentTimeSeconds;
  final int attempts;
  final Map<String, dynamic>? currentGameState;
  final bool isPaused;

  const PuzzleGameInProgress({
    required this.puzzle,
    required this.user,
    required this.currentTimeSeconds,
    this.attempts = 1,
    this.currentGameState,
    this.isPaused = false,
  });

  @override
  List<Object?> get props => [
        puzzle,
        user,
        currentTimeSeconds,
        attempts,
        currentGameState,
        isPaused,
      ];

  /// 复制状态并更新部分字段
  PuzzleGameInProgress copyWith({
    int? currentTimeSeconds,
    int? attempts,
    Map<String, dynamic>? currentGameState,
    bool? isPaused,
  }) {
    return PuzzleGameInProgress(
      puzzle: puzzle,
      user: user,
      currentTimeSeconds: currentTimeSeconds ?? this.currentTimeSeconds,
      attempts: attempts ?? this.attempts,
      currentGameState: currentGameState ?? this.currentGameState,
      isPaused: isPaused ?? this.isPaused,
    );
  }

  /// 获取格式化的时间显示
  String get formattedTime {
    final minutes = currentTimeSeconds ~/ 60;
    final seconds = currentTimeSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// 谜题游戏暂停状态
class PuzzleGamePaused extends PuzzleState {
  final PuzzleGameInProgress gameState;

  const PuzzleGamePaused(this.gameState);

  @override
  List<Object> get props => [gameState];
}

/// 答案提交中状态
class PuzzleAnswerSubmitting extends PuzzleState {
  final PuzzleGameInProgress gameState;
  final dynamic userAnswer;

  const PuzzleAnswerSubmitting({
    required this.gameState,
    required this.userAnswer,
  });

  @override
  List<Object?> get props => [gameState, userAnswer];
}

/// 答案正确状态
class PuzzleAnswerCorrect extends PuzzleState {
  final PuzzleEntity puzzle;
  final UserProfileEntity user;
  final int finalScore;
  final int timeSeconds;
  final int hintsUsed;
  final int starRating;
  final List<String> newAchievements;
  final int skillPointsEarned;
  final bool isNewRecord;

  const PuzzleAnswerCorrect({
    required this.puzzle,
    required this.user,
    required this.finalScore,
    required this.timeSeconds,
    required this.hintsUsed,
    required this.starRating,
    required this.newAchievements,
    required this.skillPointsEarned,
    required this.isNewRecord,
  });

  @override
  List<Object> get props => [
        puzzle,
        user,
        finalScore,
        timeSeconds,
        hintsUsed,
        starRating,
        newAchievements,
        skillPointsEarned,
        isNewRecord,
      ];

  /// 获取格式化的时间显示
  String get formattedTime {
    final minutes = timeSeconds ~/ 60;
    final seconds = timeSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 是否为完美完成
  bool get isPerfect => starRating == 3 && hintsUsed == 0;
}

/// 答案错误状态
class PuzzleAnswerIncorrect extends PuzzleState {
  final PuzzleGameInProgress gameState;
  final dynamic userAnswer;
  final String? feedback;

  const PuzzleAnswerIncorrect({
    required this.gameState,
    required this.userAnswer,
    this.feedback,
  });

  @override
  List<Object?> get props => [gameState, userAnswer, feedback];
}

/// 谜题游戏结束状态
class PuzzleGameCompleted extends PuzzleState {
  final PuzzleEntity puzzle;
  final UserProfileEntity updatedUser;
  final bool wasSuccessful;

  const PuzzleGameCompleted({
    required this.puzzle,
    required this.updatedUser,
    required this.wasSuccessful,
  });

  @override
  List<Object> get props => [puzzle, updatedUser, wasSuccessful];
}

/// 谜题错误状态
class PuzzleError extends PuzzleState {
  final String message;
  final String? errorCode;

  const PuzzleError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
} 
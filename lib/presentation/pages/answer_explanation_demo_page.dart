import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../data/models/puzzle.dart';
import '../../domain/services/answer_explanation_factory.dart';
import '../widgets/universal_answer_explanation_widget.dart';

/// 答案解析演示页面
/// 
/// 展示通用答案解析系统的功能
class AnswerExplanationDemoPage extends StatefulWidget {
  const AnswerExplanationDemoPage({super.key});

  @override
  State<AnswerExplanationDemoPage> createState() => _AnswerExplanationDemoPageState();
}

class _AnswerExplanationDemoPageState extends State<AnswerExplanationDemoPage> {
  int _selectedGameType = 0;
  bool _isCorrect = true;
  String _userAnswer = 'A';

  final List<String> _gameTypes = [
    '图形推理',
    '空间想象', 
    '数字逻辑',
    '编程启蒙',
    '镜像对称',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('答案解析系统演示'),
        backgroundColor: UXThemeConfig.primaryBlue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),
          
          // 解析展示区域
          Expanded(
            child: Container(
              margin: EdgeInsets.all(UXThemeConfig.paddingL),
              child: _buildExplanationDemo(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingL),
      decoration: BoxDecoration(
        color: UXThemeConfig.backgroundSecondary,
        border: Border(
          bottom: BorderSide(
            color: UXThemeConfig.borderPrimary,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '演示设置',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 游戏类型选择
          Row(
            children: [
              Text(
                '游戏类型：',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(width: UXThemeConfig.paddingS),
              Expanded(
                child: DropdownButton<int>(
                  value: _selectedGameType,
                  isExpanded: true,
                  items: _gameTypes.asMap().entries.map((entry) {
                    return DropdownMenuItem<int>(
                      value: entry.key,
                      child: Text(entry.value),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedGameType = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 答案状态切换
          Row(
            children: [
              Text(
                '答案状态：',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(width: UXThemeConfig.paddingS),
              Switch(
                value: _isCorrect,
                onChanged: (value) {
                  setState(() {
                    _isCorrect = value;
                  });
                },
                activeColor: UXThemeConfig.successGreen,
              ),
              SizedBox(width: UXThemeConfig.paddingS),
              Text(
                _isCorrect ? '正确' : '错误',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _isCorrect 
                      ? UXThemeConfig.successGreen
                      : UXThemeConfig.errorRed,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 用户答案输入
          Row(
            children: [
              Text(
                '用户答案：',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(width: UXThemeConfig.paddingS),
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: '输入用户答案',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: UXThemeConfig.paddingM,
                      vertical: UXThemeConfig.paddingS,
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _userAnswer = value;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建解析演示
  Widget _buildExplanationDemo() {
    final explanation = _createDemoExplanation();
    
    return UniversalAnswerExplanationWidget(
      explanation: explanation,
      isCorrect: _isCorrect,
      userAnswer: _userAnswer.isNotEmpty ? _userAnswer : null,
      onClose: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('关闭解析')),
        );
      },
      onRetry: _isCorrect ? null : () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('重试题目')),
        );
      },
      onNext: _isCorrect ? () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('下一题')),
        );
      } : null,
    );
  }

  /// 创建演示用的解析数据
  UniversalAnswerExplanation _createDemoExplanation() {
    switch (_selectedGameType) {
      case 0: // 图形推理
        return AnswerExplanationFactory.createGraphicPatternExplanation(
          correctAnswer: 'A',
          options: ['A', 'B', 'C', 'D'],
          pattern: '旋转90度',
        );
      
      case 1: // 空间想象
        return AnswerExplanationFactory.createSpatialVisualizationExplanation(
          correctAnswer: 'B',
          options: ['A', 'B', 'C', 'D'],
          expandedShape: 'cube_net_1',
        );
      
      case 2: // 数字逻辑
        return AnswerExplanationFactory.createNumericLogicExplanation(
          constraints: {
            'rows': '每行不重复',
            'columns': '每列不重复',
            'blocks': '每个2x2块不重复',
          },
          availableItems: ['🔴', '🔵', '🟡', '🟢'],
        );
      
      case 3: // 编程启蒙
        return AnswerExplanationFactory.createCodingExplanation(
          solution: ['forward', 'turn_right', 'forward', 'forward'],
          startPosition: {'x': 0, 'y': 0},
          endPosition: {'x': 2, 'y': 1},
        );
      
      case 4: // 镜像对称
        return AnswerExplanationFactory.createMirrorSymmetryExplanation(
          correctAnswer: 'C',
          options: ['A', 'B', 'C', 'D'],
          mirrorDirection: 'vertical',
          originalImage: 'triangle_pattern',
        );
      
      default:
        return AnswerExplanationFactory.createGraphicPatternExplanation(
          correctAnswer: 'A',
          options: ['A', 'B', 'C', 'D'],
          pattern: '默认模式',
        );
    }
  }
}

import '../../domain/repositories/user_repository.dart';
import '../../domain/entities/user_profile_entity.dart';
import '../models/user_profile.dart';
import '../../services/user_service.dart';
import '../../core/utils/result.dart';
import '../../core/exceptions/app_exceptions.dart';
import 'package:logger/logger.dart';

/// 用户仓库具体实现
/// 负责将UserService的操作转换为Domain层的接口
class UserRepositoryImpl implements UserRepository {
  final UserService _userService;
  final Logger _logger = Logger();

  UserRepositoryImpl(this._userService);

  @override
  Future<Result<UserProfileEntity?>> getCurrentUser() async {
    try {
      final userModel = await _userService.getCurrentUser();
      return Result.success(userModel?.toDomain());
    } catch (e, stackTrace) {
      _logger.e('Failed to get current user', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'GET_CURRENT_USER_FAILED',
        message: 'Failed to get current user',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<UserProfileEntity?>> getUserById(String userId) async {
    try {
      final userModel = _userService.getUserById(userId);
      return Result.success(userModel?.toDomain());
    } catch (e, stackTrace) {
      _logger.e('Failed to get user by ID: $userId', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'GET_USER_BY_ID_FAILED',
        message: 'Failed to get user by ID: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<List<UserProfileEntity>>> getAllUsers() async {
    try {
      final userModels = _userService.getAllUsers();
      final entities = userModels.map((model) => model.toDomain()).toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e('Failed to get all users', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'GET_ALL_USERS_FAILED',
        message: 'Failed to get all users',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<UserProfileEntity>> createUser({
    required String nickname,
    required String avatarId,
  }) async {
    try {
      final userModel = await _userService.createUser(
        nickname: nickname,
        avatarId: avatarId,
      );
      return Result.success(userModel.toDomain());
    } catch (e, stackTrace) {
      _logger.e('Failed to create user', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'CREATE_USER_FAILED',
        message: 'Failed to create user: $nickname',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> updateUser(UserProfileEntity user) async {
    try {
      final userModel = UserProfile.fromDomain(user);
      await _userService.updateUser(userModel);
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to update user', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'UPDATE_USER_FAILED',
        message: 'Failed to update user: ${user.id}',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> deleteUser(String userId) async {
    try {
      await _userService.deleteUser(userId);
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to delete user: $userId', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'DELETE_USER_FAILED',
        message: 'Failed to delete user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> setCurrentUser(String userId) async {
    try {
      await _userService.setCurrentUser(userId);
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to set current user: $userId', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'SET_CURRENT_USER_FAILED',
        message: 'Failed to set current user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<bool>> isNicknameExists(String nickname) async {
    try {
      final exists = await _userService.isNicknameExists(nickname);
      return Result.success(exists);
    } catch (e, stackTrace) {
      _logger.e('Failed to check nickname existence', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'CHECK_NICKNAME_FAILED',
        message: 'Failed to check nickname existence: $nickname',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> updateLevelProgress({
    required String userId,
    required String levelId,
    required int score,
    required int timeSeconds,
    required int hintsUsed,
  }) async {
    try {
      await _userService.updateLevelProgress(
        userId: userId,
        levelId: levelId,
        score: score,
        timeSeconds: timeSeconds,
        hintsUsed: hintsUsed,
      );
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to update level progress', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'UPDATE_LEVEL_PROGRESS_FAILED',
        message: 'Failed to update level progress for user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> unlockAchievement({
    required String userId,
    required String achievementId,
  }) async {
    try {
      await _userService.unlockAchievement(
        userId: userId,
        achievementId: achievementId,
      );
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to unlock achievement', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'UNLOCK_ACHIEVEMENT_FAILED',
        message: 'Failed to unlock achievement: $achievementId for user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> updateUserSettings({
    required String userId,
    required UserSettingsEntity settings,
  }) async {
    try {
      final settingsModel = UserSettings.fromDomain(settings);
      await _userService.updateUserSettings(
        userId: userId,
        settings: settingsModel,
      );
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to update user settings', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'UPDATE_USER_SETTINGS_FAILED',
        message: 'Failed to update user settings for user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> addPlayTime({
    required String userId,
    required int minutes,
  }) async {
    try {
      await _userService.addPlayTime(
        userId: userId,
        minutes: minutes,
      );
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to add play time', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'ADD_PLAY_TIME_FAILED',
        message: 'Failed to add play time for user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> getUserStats(String userId) async {
    try {
      final stats = _userService.getUserStats(userId);
      return Result.success(stats);
    } catch (e, stackTrace) {
      _logger.e('Failed to get user stats', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'GET_USER_STATS_FAILED',
        message: 'Failed to get user stats for user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<void>> resetUserProgress(String userId) async {
    try {
      await _userService.resetUserProgress(userId);
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e('Failed to reset user progress', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'RESET_USER_PROGRESS_FAILED',
        message: 'Failed to reset user progress for user: $userId',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<UserProfileEntity?>> getUserByNickname(String nickname) async {
    try {
      final userModel = _userService.getUserByNickname(nickname);
      return Result.success(userModel?.toDomain());
    } catch (e, stackTrace) {
      _logger.e('Failed to get user by nickname: $nickname', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'GET_USER_BY_NICKNAME_FAILED',
        message: 'Failed to get user by nickname: $nickname',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<int>> getUserCount() async {
    try {
      final count = _userService.getUserCount();
      return Result.success(count);
    } catch (e, stackTrace) {
      _logger.e('Failed to get user count', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'GET_USER_COUNT_FAILED',
        message: 'Failed to get user count',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  Future<Result<List<UserProfileEntity>>> searchUsers({
    String? nickname,
    String? avatarId,
    int? minLevel,
    int? maxLevel,
  }) async {
    try {
      final userModels = _userService.searchUsers(
        nickname: nickname,
        avatarId: avatarId,
        minLevel: minLevel,
        maxLevel: maxLevel,
      );
      final entities = userModels.map((model) => model.toDomain()).toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e('Failed to search users', error: e, stackTrace: stackTrace);
      return Result.failure(UserException(
        errorCode: 'SEARCH_USERS_FAILED',
        message: 'Failed to search users',
        originalError: e,
        stackTrace: stackTrace,
      ));
    }
  }
} 
import '../../domain/repositories/puzzle_repository.dart';
import '../../domain/entities/puzzle_entity.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/result.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../domain/value_objects/puzzle_types.dart' as domain_types;
import '../models/puzzle.dart';
import '../../services/puzzle_engine.dart';
import 'package:logger/logger.dart';

/// 谜题仓库具体实现
/// 负责将PuzzleEngine的操作转换为Domain层的接口
class PuzzleRepositoryImpl
    implements PuzzleRepository, PuzzleValidationRepository {
  final PuzzleEngine _puzzleEngine;
  final Logger _logger = Logger();

  PuzzleRepositoryImpl(this._puzzleEngine);

  @override
  Future<Result<void>> initialize() async {
    try {
      await _puzzleEngine.initialize();
      return const Result.success(null);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to initialize puzzle repository',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'PUZZLE_REPOSITORY_INIT_FAILED',
          message: 'Failed to initialize puzzle repository',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<PuzzleEntity?>> getPuzzleById(String levelId) async {
    try {
      final puzzleModel = await _puzzleEngine.loadPuzzle(levelId);
      return Result.success(puzzleModel?.toDomain());
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get puzzle by ID: $levelId',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_PUZZLE_BY_ID_FAILED',
          message: 'Failed to get puzzle by ID: $levelId',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<List<PuzzleEntity>>> getPuzzlesByType(PuzzleType type) async {
    try {
      final allPuzzles = await _puzzleEngine.getAllPuzzles();
      final filteredPuzzles = allPuzzles
          .where((puzzle) => puzzle.puzzleType == type)
          .toList();
      final entities = filteredPuzzles
          .map((puzzle) => puzzle.toDomain())
          .toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get puzzles by type: $type',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_PUZZLES_BY_TYPE_FAILED',
          message: 'Failed to get puzzles by type: $type',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<List<PuzzleEntity>>> getPuzzlesByDifficulty(
    DifficultyLevel difficulty,
  ) async {
    try {
      final allPuzzles = await _puzzleEngine.getAllPuzzles();
      final filteredPuzzles = allPuzzles
          .where((puzzle) => puzzle.difficulty == difficulty)
          .toList();
      final entities = filteredPuzzles
          .map((puzzle) => puzzle.toDomain())
          .toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get puzzles by difficulty: $difficulty',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_PUZZLES_BY_DIFFICULTY_FAILED',
          message: 'Failed to get puzzles by difficulty: $difficulty',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<List<PuzzleEntity>>> getPuzzlesByThemeWorld(
    ThemeWorld themeWorld,
  ) async {
    try {
      final allPuzzles = await _puzzleEngine.getAllPuzzles();
      final filteredPuzzles = allPuzzles
          .where((puzzle) => puzzle.themeWorld == themeWorld)
          .toList();
      final entities = filteredPuzzles
          .map((puzzle) => puzzle.toDomain())
          .toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get puzzles by theme world: $themeWorld',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_PUZZLES_BY_THEME_WORLD_FAILED',
          message: 'Failed to get puzzles by theme world: $themeWorld',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<List<PuzzleEntity>>> getAllPuzzles() async {
    try {
      final allPuzzles = await _puzzleEngine.getAllPuzzles();
      final entities = allPuzzles.map((puzzle) => puzzle.toDomain()).toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e('Failed to get all puzzles', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_ALL_PUZZLES_FAILED',
          message: 'Failed to get all puzzles',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<List<PuzzleEntity>>> getRecommendedPuzzles({
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
    int limit = 10,
  }) async {
    try {
      final recommendedPuzzles = await _puzzleEngine.getRecommendedPuzzles(
        userSkillPoints: userSkillPoints,
        completedLevels: completedLevels,
        limit: limit,
      );
      final entities = recommendedPuzzles
          .map((puzzle) => puzzle.toDomain())
          .toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get recommended puzzles',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_RECOMMENDED_PUZZLES_FAILED',
          message: 'Failed to get recommended puzzles',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<PuzzleEntity?>> getNextPuzzle({
    required String currentLevelId,
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
  }) async {
    try {
      final nextPuzzle = await _puzzleEngine.getNextPuzzle(
        currentLevelId: currentLevelId,
        userSkillPoints: userSkillPoints,
        completedLevels: completedLevels,
      );
      return Result.success(nextPuzzle?.toDomain());
    } catch (e, stackTrace) {
      _logger.e('Failed to get next puzzle', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_NEXT_PUZZLE_FAILED',
          message: 'Failed to get next puzzle',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<bool>> validateAnswer(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  ) async {
    try {
      _logger.d('PuzzleRepository received answer: $userAnswer');
      _logger.d('PuzzleRepository userAnswer type: ${userAnswer.runtimeType}');
      final puzzleModel = Puzzle.fromDomain(puzzle);
      _logger.d('PuzzleRepository converted puzzleModel: ${puzzleModel.levelId}');
      final isValid = _puzzleEngine.validateAnswer(puzzleModel, userAnswer);
      return Result.success(isValid);
    } catch (e, stackTrace) {
      _logger.e('Failed to validate answer', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'VALIDATE_ANSWER_FAILED',
          message: 'Failed to validate answer',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> getAnswerExplanation(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  ) async {
    try {
      final puzzleModel = Puzzle.fromDomain(puzzle);
      final explanation = _puzzleEngine.getAnswerExplanation(
        puzzleModel,
        userAnswer,
      );

      if (explanation != null) {
        return Result.success(explanation.toJson());
      } else {
        return Result.failure(
          PuzzleException(
            errorCode: 'NO_EXPLANATION_AVAILABLE',
            message: 'No explanation available for this puzzle',
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get answer explanation',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_EXPLANATION_FAILED',
          message: 'Failed to get answer explanation',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> generateHint(
    PuzzleEntity puzzle,
    int hintLevel,
  ) async {
    try {
      final puzzleModel = Puzzle.fromDomain(puzzle);
      final hint = _puzzleEngine.generateHint(
        puzzleModel,
        hintLevel: hintLevel,
      );
      return Result.success(hint);
    } catch (e, stackTrace) {
      _logger.e('Failed to generate hint', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'GENERATE_HINT_FAILED',
          message: 'Failed to generate hint',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<List<PuzzleEntity>>> searchPuzzles({
    String? keyword,
    PuzzleType? type,
    DifficultyLevel? difficulty,
    ThemeWorld? themeWorld,
    List<String>? tags,
  }) async {
    try {
      final allPuzzles = await _puzzleEngine.getAllPuzzles();

      var filteredPuzzles = allPuzzles.where((puzzle) {
        // 关键词搜索
        if (keyword != null && keyword.isNotEmpty) {
          final keywordLower = keyword.toLowerCase();
          if (!puzzle.prompt.toLowerCase().contains(keywordLower) &&
              !puzzle.levelId.toLowerCase().contains(keywordLower)) {
            return false;
          }
        }

        // 类型过滤
        if (type != null && puzzle.puzzleType != type) {
          return false;
        }

        // 难度过滤
        if (difficulty != null && puzzle.difficulty != difficulty) {
          return false;
        }

        // 主题世界过滤
        if (themeWorld != null && puzzle.themeWorld != themeWorld) {
          return false;
        }

        // 标签过滤
        if (tags != null && tags.isNotEmpty) {
          final hasMatchingTag = tags.any((tag) => puzzle.tags.contains(tag));
          if (!hasMatchingTag) {
            return false;
          }
        }

        return true;
      }).toList();

      final entities = filteredPuzzles
          .map((puzzle) => puzzle.toDomain())
          .toList();
      return Result.success(entities);
    } catch (e, stackTrace) {
      _logger.e('Failed to search puzzles', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'SEARCH_PUZZLES_FAILED',
          message: 'Failed to search puzzles',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> getPuzzleStats() async {
    try {
      final allPuzzles = await _puzzleEngine.getAllPuzzles();

      final stats = <String, dynamic>{
        'totalPuzzles': allPuzzles.length,
        'puzzlesByType': <String, int>{},
        'puzzlesByDifficulty': <String, int>{},
        'puzzlesByThemeWorld': <String, int>{},
      };

      // 按类型统计
      for (final puzzle in allPuzzles) {
        final typeKey = puzzle.puzzleType.id;
        stats['puzzlesByType'][typeKey] =
            (stats['puzzlesByType'][typeKey] as int? ?? 0) + 1;
      }

      // 按难度统计
      for (final puzzle in allPuzzles) {
        final difficultyKey = puzzle.difficulty.id;
        stats['puzzlesByDifficulty'][difficultyKey] =
            (stats['puzzlesByDifficulty'][difficultyKey] as int? ?? 0) + 1;
      }

      // 按主题世界统计
      for (final puzzle in allPuzzles) {
        final themeKey = puzzle.themeWorld?.id ?? 'unknown';
        stats['puzzlesByThemeWorld'][themeKey] =
            (stats['puzzlesByThemeWorld'][themeKey] as int? ?? 0) + 1;
      }

      return Result.success(stats);
    } catch (e, stackTrace) {
      _logger.e('Failed to get puzzle stats', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_PUZZLE_STATS_FAILED',
          message: 'Failed to get puzzle stats',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // ==================== PuzzleValidationRepository 实现 ====================

  @override
  Future<Result<List<bool>>> validateAnswers(
    List<({PuzzleEntity puzzle, dynamic userAnswer})> validations,
  ) async {
    try {
      final results = <bool>[];

      for (final validation in validations) {
        final puzzleModel = Puzzle.fromDomain(validation.puzzle);
        final isValid = _puzzleEngine.validateAnswer(
          puzzleModel,
          validation.userAnswer,
        );
        results.add(isValid);
      }

      return Result.success(results);
    } catch (e, stackTrace) {
      _logger.e('Failed to validate answers', error: e, stackTrace: stackTrace);
      return Result.failure(
        PuzzleException(
          errorCode: 'VALIDATE_ANSWERS_FAILED',
          message: 'Failed to validate answers',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<bool>> validateAnswerFormat(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  ) async {
    try {
      // 基本格式验证
      switch (puzzle.puzzleType) {
        case domain_types.PuzzleType.graphicPattern3x3:
        case domain_types.PuzzleType.spatialVisualization:
        case domain_types.PuzzleType.mirrorSymmetry:
          // 这些类型期望字符串答案
          return Result.success(userAnswer is String && userAnswer.isNotEmpty);

        case domain_types.PuzzleType.numericLogic:
          // 期望字符串列表（4x4网格）
          if (userAnswer is! List<String?>) return Result.success(false);
          final grid = userAnswer;
          return Result.success(grid.length == 16);

        case domain_types.PuzzleType.introToCoding:
          // 期望字符串列表（指令序列）
          if (userAnswer is! List<String>) return Result.success(false);
          final commands = userAnswer;
          return Result.success(commands.isNotEmpty);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to validate answer format',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'VALIDATE_FORMAT_FAILED',
          message: 'Failed to validate answer format',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  Future<Result<double>> getAnswerSimilarity(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  ) async {
    try {
      // 获取正确答案进行比较
      final puzzleModel = Puzzle.fromDomain(puzzle);
      dynamic correctAnswer;

      switch (puzzle.puzzleType) {
        case domain_types.PuzzleType.graphicPattern3x3:
          final data = GraphicPatternData.fromJson(puzzleModel.data);
          correctAnswer = data.answer;
          break;
        case domain_types.PuzzleType.spatialVisualization:
          final data = SpatialVisualizationData.fromJson(puzzleModel.data);
          correctAnswer = data.answer;
          break;
        case domain_types.PuzzleType.mirrorSymmetry:
          final data = MirrorSymmetryData.fromJson(puzzleModel.data);
          correctAnswer = data.answer;
          break;
        case domain_types.PuzzleType.numericLogic:
          // 对于数字逻辑，我们检查是否是有效解
          final data = NumericLogicData.fromJson(puzzleModel.data);
          final testData = NumericLogicData(
            grid: userAnswer as List<String?>,
            availableItems: data.availableItems,
            constraints: data.constraints,
          );
          return Result.success(testData.isValidSolution() ? 1.0 : 0.0);
        case domain_types.PuzzleType.introToCoding:
          // 对于编程启蒙，我们检查指令是否有效
          final data = CodingData.fromJson(puzzleModel.data);
          final isValid = data.validateCommandSequence(
            userAnswer as List<String>,
          );
          return Result.success(isValid ? 1.0 : 0.0);
      }

      // 简单的相似度计算
      switch (puzzle.puzzleType) {
        case domain_types.PuzzleType.graphicPattern3x3:
        case domain_types.PuzzleType.spatialVisualization:
        case domain_types.PuzzleType.mirrorSymmetry:
          // 字符串完全匹配
          if (userAnswer is String && correctAnswer is String) {
            return Result.success(userAnswer == correctAnswer ? 1.0 : 0.0);
          }
          return Result.success(0.0);

        case domain_types.PuzzleType.numericLogic:
        case domain_types.PuzzleType.introToCoding:
          // 这些情况已在上面处理
          return Result.success(0.0);
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get answer similarity',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'GET_SIMILARITY_FAILED',
          message: 'Failed to get answer similarity',
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}

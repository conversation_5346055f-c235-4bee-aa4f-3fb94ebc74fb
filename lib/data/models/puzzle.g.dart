// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'puzzle.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PuzzleAdapter extends TypeAdapter<Puzzle> {
  @override
  final int typeId = 4;

  @override
  Puzzle read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Puzzle(
      levelId: fields[0] as String,
      schemaVersion: fields[1] as String,
      author: fields[2] as String,
      tags: (fields[3] as List).cast<String>(),
      puzzleType: fields[4] as PuzzleType,
      difficulty: fields[5] as DifficultyLevel,
      prompt: fields[6] as String,
      data: (fields[7] as Map).cast<String, dynamic>(),
      themeWorld: fields[8] as ThemeWorld?,
      orderInWorld: fields[9] as int?,
      maxHints: fields[10] as int?,
      correctAnswer: fields[11] as dynamic,
    );
  }

  @override
  void write(BinaryWriter writer, Puzzle obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.levelId)
      ..writeByte(1)
      ..write(obj.schemaVersion)
      ..writeByte(2)
      ..write(obj.author)
      ..writeByte(3)
      ..write(obj.tags)
      ..writeByte(4)
      ..write(obj.puzzleType)
      ..writeByte(5)
      ..write(obj.difficulty)
      ..writeByte(6)
      ..write(obj.prompt)
      ..writeByte(7)
      ..write(obj.data)
      ..writeByte(8)
      ..write(obj.themeWorld)
      ..writeByte(9)
      ..write(obj.orderInWorld)
      ..writeByte(10)
      ..write(obj.maxHints)
      ..writeByte(11)
      ..write(obj.correctAnswer);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PuzzleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GraphicPatternDataAdapter extends TypeAdapter<GraphicPatternData> {
  @override
  final int typeId = 5;

  @override
  GraphicPatternData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GraphicPatternData(
      grid: (fields[0] as List).cast<String?>(),
      options: (fields[1] as List).cast<String>(),
      answer: fields[2] as String,
      explanation: fields[3] as UniversalAnswerExplanation?,
    );
  }

  @override
  void write(BinaryWriter writer, GraphicPatternData obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.grid)
      ..writeByte(1)
      ..write(obj.options)
      ..writeByte(2)
      ..write(obj.answer)
      ..writeByte(3)
      ..write(obj.explanation);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GraphicPatternDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Puzzle _$PuzzleFromJson(Map<String, dynamic> json) => Puzzle(
      levelId: json['levelId'] as String,
      schemaVersion: json['schemaVersion'] as String,
      author: json['author'] as String,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      puzzleType: _puzzleTypeFromJson(json['puzzleType'] as String),
      difficulty: $enumDecode(_$DifficultyLevelEnumMap, json['difficulty']),
      prompt: json['prompt'] as String,
      data: json['data'] as Map<String, dynamic>,
      themeWorld: $enumDecodeNullable(_$ThemeWorldEnumMap, json['themeWorld']),
      orderInWorld: (json['orderInWorld'] as num?)?.toInt(),
      maxHints: (json['maxHints'] as num?)?.toInt(),
      correctAnswer: json['correctAnswer'],
    );

Map<String, dynamic> _$PuzzleToJson(Puzzle instance) => <String, dynamic>{
      'levelId': instance.levelId,
      'schemaVersion': instance.schemaVersion,
      'author': instance.author,
      'tags': instance.tags,
      'puzzleType': _puzzleTypeToJson(instance.puzzleType),
      'difficulty': _$DifficultyLevelEnumMap[instance.difficulty]!,
      'prompt': instance.prompt,
      'data': instance.data,
      'themeWorld': _$ThemeWorldEnumMap[instance.themeWorld],
      'orderInWorld': instance.orderInWorld,
      'maxHints': instance.maxHints,
      'correctAnswer': instance.correctAnswer,
    };

const _$DifficultyLevelEnumMap = {
  DifficultyLevel.easy: 'easy',
  DifficultyLevel.medium: 'medium',
  DifficultyLevel.hard: 'hard',
  DifficultyLevel.expert: 'expert',
};

const _$ThemeWorldEnumMap = {
  ThemeWorld.forest: 'forest',
  ThemeWorld.ocean: 'ocean',
  ThemeWorld.space: 'space',
};

GraphicPatternData _$GraphicPatternDataFromJson(Map<String, dynamic> json) =>
    GraphicPatternData(
      grid: (json['grid'] as List<dynamic>).map((e) => e as String?).toList(),
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
      answer: json['answer'] as String,
      explanation: json['explanation'] == null
          ? null
          : UniversalAnswerExplanation.fromJson(
              json['explanation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GraphicPatternDataToJson(GraphicPatternData instance) =>
    <String, dynamic>{
      'grid': instance.grid,
      'options': instance.options,
      'answer': instance.answer,
      'explanation': instance.explanation,
    };

SpatialVisualizationData _$SpatialVisualizationDataFromJson(
        Map<String, dynamic> json) =>
    SpatialVisualizationData(
      expandedShape: json['expandedShape'] as String,
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
      answer: json['answer'] as String,
      explanation: json['explanation'] == null
          ? null
          : UniversalAnswerExplanation.fromJson(
              json['explanation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SpatialVisualizationDataToJson(
        SpatialVisualizationData instance) =>
    <String, dynamic>{
      'expandedShape': instance.expandedShape,
      'options': instance.options,
      'answer': instance.answer,
      'explanation': instance.explanation,
    };

NumericLogicData _$NumericLogicDataFromJson(Map<String, dynamic> json) =>
    NumericLogicData(
      grid: (json['grid'] as List<dynamic>).map((e) => e as String?).toList(),
      availableItems: (json['availableItems'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      constraints: json['constraints'] as Map<String, dynamic>,
      explanation: json['explanation'] == null
          ? null
          : UniversalAnswerExplanation.fromJson(
              json['explanation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$NumericLogicDataToJson(NumericLogicData instance) =>
    <String, dynamic>{
      'grid': instance.grid,
      'availableItems': instance.availableItems,
      'constraints': instance.constraints,
      'explanation': instance.explanation,
    };

CodingData _$CodingDataFromJson(Map<String, dynamic> json) => CodingData(
      maze: json['maze'] as Map<String, dynamic>,
      startPosition: Map<String, int>.from(json['startPosition'] as Map),
      endPosition: Map<String, int>.from(json['endPosition'] as Map),
      availableCommands: (json['availableCommands'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      maxCommands: (json['maxCommands'] as num).toInt(),
      explanation: json['explanation'] == null
          ? null
          : UniversalAnswerExplanation.fromJson(
              json['explanation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CodingDataToJson(CodingData instance) =>
    <String, dynamic>{
      'maze': instance.maze,
      'startPosition': instance.startPosition,
      'endPosition': instance.endPosition,
      'availableCommands': instance.availableCommands,
      'maxCommands': instance.maxCommands,
      'explanation': instance.explanation,
    };

PuzzleResult _$PuzzleResultFromJson(Map<String, dynamic> json) => PuzzleResult(
      levelId: json['levelId'] as String,
      completed: json['completed'] as bool,
      score: (json['score'] as num).toInt(),
      timeSeconds: (json['timeSeconds'] as num).toInt(),
      hintsUsed: (json['hintsUsed'] as num).toInt(),
      attempts: (json['attempts'] as num).toInt(),
      completedAt: DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$PuzzleResultToJson(PuzzleResult instance) =>
    <String, dynamic>{
      'levelId': instance.levelId,
      'completed': instance.completed,
      'score': instance.score,
      'timeSeconds': instance.timeSeconds,
      'hintsUsed': instance.hintsUsed,
      'attempts': instance.attempts,
      'completedAt': instance.completedAt.toIso8601String(),
    };

MirrorSymmetryData _$MirrorSymmetryDataFromJson(Map<String, dynamic> json) =>
    MirrorSymmetryData(
      originalImage: json['originalImage'] as String,
      mirrorDirection: json['mirrorDirection'] as String,
      originalPattern: json['originalPattern'] as Map<String, dynamic>,
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
      answer: json['answer'] as String,
      explanation: json['explanation'] == null
          ? null
          : AnswerExplanation.fromJson(
              json['explanation'] as Map<String, dynamic>),
      universalExplanation: json['universalExplanation'] == null
          ? null
          : UniversalAnswerExplanation.fromJson(
              json['universalExplanation'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MirrorSymmetryDataToJson(MirrorSymmetryData instance) =>
    <String, dynamic>{
      'originalImage': instance.originalImage,
      'mirrorDirection': instance.mirrorDirection,
      'originalPattern': instance.originalPattern,
      'options': instance.options,
      'answer': instance.answer,
      'explanation': instance.explanation,
      'universalExplanation': instance.universalExplanation,
    };

UniversalAnswerExplanation _$UniversalAnswerExplanationFromJson(
        Map<String, dynamic> json) =>
    UniversalAnswerExplanation(
      puzzleType: json['puzzleType'] as String,
      keyPoint: json['keyPoint'] as String,
      verificationText: json['verificationText'] as String?,
      thinkingMethod: json['thinkingMethod'] as String?,
      steps: (json['steps'] as List<dynamic>)
          .map((e) => ExplanationStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      optionExplanations: (json['optionExplanations'] as List<dynamic>)
          .map((e) => OptionExplanation.fromJson(e as Map<String, dynamic>))
          .toList(),
      conclusion: json['conclusion'] as String,
      additionalData: json['additionalData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UniversalAnswerExplanationToJson(
        UniversalAnswerExplanation instance) =>
    <String, dynamic>{
      'puzzleType': instance.puzzleType,
      'keyPoint': instance.keyPoint,
      'verificationText': instance.verificationText,
      'thinkingMethod': instance.thinkingMethod,
      'steps': instance.steps,
      'optionExplanations': instance.optionExplanations,
      'conclusion': instance.conclusion,
      'additionalData': instance.additionalData,
    };

ExplanationStep _$ExplanationStepFromJson(Map<String, dynamic> json) =>
    ExplanationStep(
      stepNumber: (json['stepNumber'] as num).toInt(),
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$ExplanationStepTypeEnumMap, json['type']),
      content: json['content'] as Map<String, dynamic>?,
      highlights: (json['highlights'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      tip: json['tip'] as String?,
    );

Map<String, dynamic> _$ExplanationStepToJson(ExplanationStep instance) =>
    <String, dynamic>{
      'stepNumber': instance.stepNumber,
      'title': instance.title,
      'description': instance.description,
      'type': _$ExplanationStepTypeEnumMap[instance.type]!,
      'content': instance.content,
      'highlights': instance.highlights,
      'tip': instance.tip,
    };

const _$ExplanationStepTypeEnumMap = {
  ExplanationStepType.text: 'text',
  ExplanationStepType.visual: 'visual',
  ExplanationStepType.interactive: 'interactive',
  ExplanationStepType.comparison: 'comparison',
  ExplanationStepType.process: 'process',
  ExplanationStepType.rule: 'rule',
};

AnswerExplanation _$AnswerExplanationFromJson(Map<String, dynamic> json) =>
    AnswerExplanation(
      keyPoint: json['keyPoint'] as String,
      verificationText: json['verificationText'] as String,
      thinkingMethod: json['thinkingMethod'] as String,
      optionExplanations: (json['optionExplanations'] as List<dynamic>)
          .map((e) => OptionExplanation.fromJson(e as Map<String, dynamic>))
          .toList(),
      conclusion: json['conclusion'] as String,
    );

Map<String, dynamic> _$AnswerExplanationToJson(AnswerExplanation instance) =>
    <String, dynamic>{
      'keyPoint': instance.keyPoint,
      'verificationText': instance.verificationText,
      'thinkingMethod': instance.thinkingMethod,
      'optionExplanations': instance.optionExplanations,
      'conclusion': instance.conclusion,
    };

OptionExplanation _$OptionExplanationFromJson(Map<String, dynamic> json) =>
    OptionExplanation(
      optionId: json['optionId'] as String,
      explanation: json['explanation'] as String,
      isCorrect: json['isCorrect'] as bool,
      imagePath: json['imagePath'] as String?,
    );

Map<String, dynamic> _$OptionExplanationToJson(OptionExplanation instance) =>
    <String, dynamic>{
      'optionId': instance.optionId,
      'explanation': instance.explanation,
      'isCorrect': instance.isCorrect,
      'imagePath': instance.imagePath,
    };

import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/puzzle_entity.dart';
import '../converters/puzzle_type_converter.dart';

part 'puzzle.g.dart';

PuzzleType _puzzleTypeFromJson(String id) =>
    PuzzleType.values.firstWhere((e) => e.id == id);

String _puzzleTypeToJson(PuzzleType type) => type.id;

/// 谜题数据模型
@HiveType(typeId: 4)
@JsonSerializable()
class Puzzle with EquatableMixin {
  @HiveField(0)
  final String levelId;

  @HiveField(1)
  final String schemaVersion;

  @HiveField(2)
  final String author;

  @HiveField(3)
  final List<String> tags;

  @HiveField(4)
  @JsonKey(
    fromJson: _puzzleTypeFromJson,
    toJson: _puzzleTypeToJson,
  )
  final PuzzleType puzzleType;

  @HiveField(5)
  final DifficultyLevel difficulty;

  @HiveField(6)
  final String prompt;

  @HiveField(7)
  final Map<String, dynamic> data;

  @HiveField(8)
  final ThemeWorld? themeWorld;

  @HiveField(9)
  final int? orderInWorld;

  @HiveField(10)
  final int? maxHints;

  @HiveField(11)
  final dynamic correctAnswer;

  Puzzle({
    required this.levelId,
    required this.schemaVersion,
    required this.author,
    required this.tags,
    required this.puzzleType,
    required this.difficulty,
    required this.prompt,
    required this.data,
    this.themeWorld,
    this.orderInWorld,
    this.maxHints,
    this.correctAnswer,
  });

  /// 从JSON创建谜题
  factory Puzzle.fromJson(Map<String, dynamic> json) => _$PuzzleFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$PuzzleToJson(this);

  /// 获取谜题的完整标识符
  String get fullId => '${themeWorld?.id ?? 'default'}_$levelId';

  /// 检查是否为特定类型的谜题
  bool isType(PuzzleType type) => puzzleType == type;

  /// 检查是否为特定难度
  bool isDifficulty(DifficultyLevel level) => difficulty == level;

  /// 获取谜题的估计完成时间（分钟）
  int get estimatedTimeMinutes {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 2;
      case DifficultyLevel.medium:
        return 5;
      case DifficultyLevel.hard:
        return 8;
      case DifficultyLevel.expert:
        return 12;
    }
  }

  /// 转换为Domain实体
  PuzzleEntity toDomain() {
    return PuzzleEntity(
      levelId: levelId,
      schemaVersion: schemaVersion,
      author: author,
      tags: List.from(tags),
      puzzleType: PuzzleTypeConverter.coreToDomain(puzzleType),
      difficulty: PuzzleTypeConverter.coreToDomainDifficulty(difficulty),
      prompt: prompt,
      data: Map.from(data),
      themeWorld: PuzzleTypeConverter.coreToDomainTheme(themeWorld),
      orderInWorld: orderInWorld,
      maxHints: maxHints ?? 3,
      correctAnswer: correctAnswer,
    );
  }

  /// 从Domain实体创建
  factory Puzzle.fromDomain(PuzzleEntity entity) {
    return Puzzle(
      levelId: entity.levelId,
      schemaVersion: entity.schemaVersion,
      author: entity.author,
      tags: List.from(entity.tags),
      puzzleType: PuzzleTypeConverter.domainToCore(entity.puzzleType),
      difficulty: PuzzleTypeConverter.domainToCoreDifficulty(entity.difficulty),
      prompt: entity.prompt,
      data: Map.from(entity.data),
      themeWorld: PuzzleTypeConverter.domainToCoreTheme(entity.themeWorld),
      orderInWorld: entity.orderInWorld,
      maxHints: entity.maxHints ?? 3,
      correctAnswer: entity.correctAnswer,
    );
  }

  @override
  List<Object?> get props => [
    levelId,
    schemaVersion,
    author,
    tags,
    puzzleType,
    difficulty,
    prompt,
    data,
    themeWorld,
    orderInWorld,
    maxHints,
    correctAnswer,
  ];
}

/// 图形推理谜题数据
@HiveType(typeId: 5)
@JsonSerializable()
class GraphicPatternData with EquatableMixin {
  @HiveField(0)
  final List<String?> grid; // 3x3网格，null表示空格

  @HiveField(1)
  final List<String> options; // 选项

  @HiveField(2)
  final String answer; // 正确答案

  @HiveField(3)
  final UniversalAnswerExplanation? explanation; // 答案解析（可选）

  GraphicPatternData({
    required this.grid,
    required this.options,
    required this.answer,
    this.explanation,
  });

  factory GraphicPatternData.fromJson(Map<String, dynamic> json) =>
      _$GraphicPatternDataFromJson(json);

  Map<String, dynamic> toJson() => _$GraphicPatternDataToJson(this);

  /// 获取空格的索引
  int? get emptyIndex {
    for (int i = 0; i < grid.length; i++) {
      if (grid[i] == null) return i;
    }
    return null;
  }

  /// 检查答案是否正确
  bool isCorrectAnswer(String userAnswer) => userAnswer == answer;

  @override
  List<Object?> get props => [grid, options, answer, explanation];
}

/// 空间想象谜题数据
@JsonSerializable()
class SpatialVisualizationData with EquatableMixin {
  final String expandedShape; // 展开图ID
  final List<String> options; // 3D模型选项
  final String answer; // 正确答案
  final UniversalAnswerExplanation? explanation; // 答案解析（可选）

  SpatialVisualizationData({
    required this.expandedShape,
    required this.options,
    required this.answer,
    this.explanation,
  });

  factory SpatialVisualizationData.fromJson(Map<String, dynamic> json) =>
      _$SpatialVisualizationDataFromJson(json);

  Map<String, dynamic> toJson() => _$SpatialVisualizationDataToJson(this);

  /// 检查答案是否正确
  bool isCorrectAnswer(String userAnswer) => userAnswer == answer;

  @override
  List<Object?> get props => [expandedShape, options, answer, explanation];
}

/// 数字逻辑谜题数据
@JsonSerializable()
class NumericLogicData with EquatableMixin {
  final List<String?> grid; // 4x4网格
  final List<String> availableItems; // 可用图标
  final Map<String, dynamic> constraints; // 约束条件
  final UniversalAnswerExplanation? explanation; // 答案解析（可选）

  NumericLogicData({
    required this.grid,
    required this.availableItems,
    required this.constraints,
    this.explanation,
  });

  factory NumericLogicData.fromJson(Map<String, dynamic> json) =>
      _$NumericLogicDataFromJson(json);

  Map<String, dynamic> toJson() => _$NumericLogicDataToJson(this);

  /// 获取空格的索引列表
  List<int> get emptyIndices {
    final indices = <int>[];
    for (int i = 0; i < grid.length; i++) {
      if (grid[i] == null) indices.add(i);
    }
    return indices;
  }

  /// 检查当前状态是否完成且正确
  bool isValidSolution() {
    // 检查是否所有格子都已填充
    if (grid.contains(null)) return false;

    // 检查行约束
    for (int row = 0; row < 4; row++) {
      final rowItems = <String>[];
      for (int col = 0; col < 4; col++) {
        final item = grid[row * 4 + col];
        if (item != null) rowItems.add(item);
      }
      if (rowItems.length != rowItems.toSet().length) return false;
    }

    // 检查列约束
    for (int col = 0; col < 4; col++) {
      final colItems = <String>[];
      for (int row = 0; row < 4; row++) {
        final item = grid[row * 4 + col];
        if (item != null) colItems.add(item);
      }
      if (colItems.length != colItems.toSet().length) return false;
    }

    // 检查2x2宫格约束
    for (int boxRow = 0; boxRow < 2; boxRow++) {
      for (int boxCol = 0; boxCol < 2; boxCol++) {
        final boxItems = <String>[];
        for (int r = 0; r < 2; r++) {
          for (int c = 0; c < 2; c++) {
            final row = boxRow * 2 + r;
            final col = boxCol * 2 + c;
            final item = grid[row * 4 + col];
            if (item != null) boxItems.add(item);
          }
        }
        if (boxItems.length != boxItems.toSet().length) return false;
      }
    }

    return true;
  }

  @override
  List<Object?> get props => [grid, availableItems, constraints, explanation];
}

/// 编程启蒙谜题数据
@JsonSerializable()
class CodingData with EquatableMixin {
  final Map<String, dynamic> maze; // 迷宫布局
  final Map<String, int> startPosition; // 起始位置 {x, y}
  final Map<String, int> endPosition; // 结束位置 {x, y}
  final List<String> availableCommands; // 可用指令
  final int maxCommands; // 最大指令数
  final UniversalAnswerExplanation? explanation; // 答案解析（可选）

  CodingData({
    required this.maze,
    required this.startPosition,
    required this.endPosition,
    required this.availableCommands,
    required this.maxCommands,
    this.explanation,
  });

  factory CodingData.fromJson(Map<String, dynamic> json) =>
      _$CodingDataFromJson(json);

  Map<String, dynamic> toJson() => _$CodingDataToJson(this);

  /// 验证指令序列是否能到达终点
  bool validateCommandSequence(List<String> commands) {
    if (commands.length > maxCommands) return false;

    int x = startPosition['x']!;
    int y = startPosition['y']!;
    int direction = 0; // 0: 上, 1: 右, 2: 下, 3: 左

    for (final command in commands) {
      switch (command) {
        case 'forward':
          switch (direction) {
            case 0:
              y--;
              break; // 上
            case 1:
              x++;
              break; // 右
            case 2:
              y++;
              break; // 下
            case 3:
              x--;
              break; // 左
          }
          break;
        case 'turn_left':
          direction = (direction - 1) % 4;
          break;
        case 'turn_right':
          direction = (direction + 1) % 4;
          break;
      }

      // 检查是否撞墙或越界
      if (!_isValidPosition(x, y)) return false;
    }

    // 检查是否到达终点
    return x == endPosition['x'] && y == endPosition['y'];
  }

  bool _isValidPosition(int x, int y) {
    // 这里需要根据maze的具体结构来实现
    // 简化实现，假设maze是一个2D数组
    final mazeGrid = maze['grid'] as List<List<int>>;
    if (y < 0 || y >= mazeGrid.length) return false;
    if (x < 0 || x >= mazeGrid[y].length) return false;
    return mazeGrid[y][x] == 0; // 0表示可通行，1表示墙
  }

  @override
  List<Object?> get props => [
    maze,
    startPosition,
    endPosition,
    availableCommands,
    maxCommands,
    explanation,
  ];
}

/// 谜题结果数据
@JsonSerializable()
class PuzzleResult with EquatableMixin {
  final String levelId;
  final bool completed;
  final int score; // 星数 (0-3)
  final int timeSeconds;
  final int hintsUsed;
  final int attempts;
  final DateTime completedAt;

  PuzzleResult({
    required this.levelId,
    required this.completed,
    required this.score,
    required this.timeSeconds,
    required this.hintsUsed,
    required this.attempts,
    required this.completedAt,
  });

  factory PuzzleResult.fromJson(Map<String, dynamic> json) =>
      _$PuzzleResultFromJson(json);

  Map<String, dynamic> toJson() => _$PuzzleResultToJson(this);

  /// 计算得分
  static int calculateScore({
    required int timeSeconds,
    required int hintsUsed,
    required int targetTimeSeconds,
  }) {
    int score = 3; // 基础分数

    // 根据时间扣分
    if (timeSeconds > targetTimeSeconds * 1.5) {
      score--;
    }
    if (timeSeconds > targetTimeSeconds * 2) {
      score--;
    }

    // 根据提示使用扣分
    if (hintsUsed > 0) {
      score = (score - hintsUsed).clamp(0, 3);
    }

    return score;
  }

  @override
  List<Object?> get props => [
    levelId,
    completed,
    score,
    timeSeconds,
    hintsUsed,
    attempts,
    completedAt,
  ];
}

/// 镜像对称谜题数据
@JsonSerializable()
class MirrorSymmetryData with EquatableMixin {
  final String originalImage; // 原始图像ID
  final String mirrorDirection; // 镜像方向: 'horizontal', 'vertical'
  final Map<String, dynamic> originalPattern; // 原始图案数据
  final List<String> options; // 选项图像ID列表
  final String answer; // 正确答案
  final AnswerExplanation? explanation; // 答案解析（可选，向后兼容）
  final UniversalAnswerExplanation? universalExplanation; // 通用答案解析（新版本）

  MirrorSymmetryData({
    required this.originalImage,
    required this.mirrorDirection,
    required this.originalPattern,
    required this.options,
    required this.answer,
    this.explanation,
    this.universalExplanation,
  });

  factory MirrorSymmetryData.fromJson(Map<String, dynamic> json) =>
      _$MirrorSymmetryDataFromJson(json);

  Map<String, dynamic> toJson() => _$MirrorSymmetryDataToJson(this);

  /// 检查答案是否正确
  bool isCorrectAnswer(String userAnswer) => userAnswer == answer;

  /// 获取原始图案的位置信息
  String get patternPosition =>
      originalPattern['position'] as String? ?? 'center';

  /// 获取原始图案的类型
  String get patternType => originalPattern['type'] as String? ?? 'heart';

  /// 获取原始图案的颜色
  String get patternColor => originalPattern['color'] as String? ?? 'pink';

  /// 获取镜像后的预期位置
  String get expectedMirrorPosition {
    if (mirrorDirection == 'horizontal') {
      // 水平镜像：左右对称
      switch (patternPosition) {
        case 'left':
          return 'right';
        case 'right':
          return 'left';
        case 'center':
        default:
          return 'center';
      }
    } else {
      // 垂直镜像：上下对称
      switch (patternPosition) {
        case 'top':
          return 'bottom';
        case 'bottom':
          return 'top';
        case 'center':
        default:
          return 'center';
      }
    }
  }

  /// 获取有效的解析数据（优先使用通用解析）
  UniversalAnswerExplanation? getEffectiveExplanation() {
    if (universalExplanation != null) {
      return universalExplanation;
    }

    if (explanation != null) {
      return explanation!.toUniversal('mirrorSymmetry');
    }

    return null;
  }

  @override
  List<Object?> get props => [
    originalImage,
    mirrorDirection,
    originalPattern,
    options,
    answer,
    explanation,
    universalExplanation,
  ];
}

/// 通用答案解析数据
///
/// 支持所有游戏类型的答案解析，提供灵活的内容结构
@JsonSerializable()
class UniversalAnswerExplanation with EquatableMixin {
  final String puzzleType; // 游戏类型标识
  final String keyPoint; // 核心要点
  final String? verificationText; // 验证说明（可选）
  final String? thinkingMethod; // 思维方式（可选）
  final List<ExplanationStep> steps; // 解析步骤
  final List<OptionExplanation> optionExplanations; // 选项解释
  final String conclusion; // 结论
  final Map<String, dynamic>? additionalData; // 额外数据（游戏类型特定）

  UniversalAnswerExplanation({
    required this.puzzleType,
    required this.keyPoint,
    this.verificationText,
    this.thinkingMethod,
    required this.steps,
    required this.optionExplanations,
    required this.conclusion,
    this.additionalData,
  });

  factory UniversalAnswerExplanation.fromJson(Map<String, dynamic> json) =>
      _$UniversalAnswerExplanationFromJson(json);

  Map<String, dynamic> toJson() => _$UniversalAnswerExplanationToJson(this);

  @override
  List<Object?> get props => [
    puzzleType,
    keyPoint,
    verificationText,
    thinkingMethod,
    steps,
    optionExplanations,
    conclusion,
    additionalData,
  ];
}

/// 解析步骤数据
///
/// 表示解析过程中的一个步骤，支持多种内容类型
@JsonSerializable()
class ExplanationStep with EquatableMixin {
  final int stepNumber; // 步骤编号
  final String title; // 步骤标题
  final String description; // 步骤描述
  final ExplanationStepType type; // 步骤类型
  final Map<String, dynamic>? content; // 步骤内容（可包含图片、动画等）
  final List<String>? highlights; // 需要高亮的元素
  final String? tip; // 提示信息

  ExplanationStep({
    required this.stepNumber,
    required this.title,
    required this.description,
    required this.type,
    this.content,
    this.highlights,
    this.tip,
  });

  factory ExplanationStep.fromJson(Map<String, dynamic> json) =>
      _$ExplanationStepFromJson(json);

  Map<String, dynamic> toJson() => _$ExplanationStepToJson(this);

  @override
  List<Object?> get props => [
    stepNumber,
    title,
    description,
    type,
    content,
    highlights,
    tip,
  ];
}

/// 解析步骤类型枚举
enum ExplanationStepType {
  @JsonValue('text')
  text, // 纯文本解释

  @JsonValue('visual')
  visual, // 视觉演示（图片、动画）

  @JsonValue('interactive')
  interactive, // 交互式演示

  @JsonValue('comparison')
  comparison, // 对比分析

  @JsonValue('process')
  process, // 过程演示

  @JsonValue('rule')
  rule, // 规则说明
}

/// 答案解析数据（保持向后兼容）
@JsonSerializable()
class AnswerExplanation with EquatableMixin {
  final String keyPoint; // 核心要点
  final String verificationText; // 验证说明
  final String thinkingMethod; // 思维方式
  final List<OptionExplanation> optionExplanations; // 选项解释
  final String conclusion; // 结论

  AnswerExplanation({
    required this.keyPoint,
    required this.verificationText,
    required this.thinkingMethod,
    required this.optionExplanations,
    required this.conclusion,
  });

  factory AnswerExplanation.fromJson(Map<String, dynamic> json) =>
      _$AnswerExplanationFromJson(json);

  Map<String, dynamic> toJson() => _$AnswerExplanationToJson(this);

  /// 转换为通用解析格式
  UniversalAnswerExplanation toUniversal(String puzzleType) {
    return UniversalAnswerExplanation(
      puzzleType: puzzleType,
      keyPoint: keyPoint,
      verificationText: verificationText,
      thinkingMethod: thinkingMethod,
      steps: [
        ExplanationStep(
          stepNumber: 1,
          title: '核心概念',
          description: keyPoint,
          type: ExplanationStepType.rule,
        ),
        ExplanationStep(
          stepNumber: 2,
          title: '验证方法',
          description: verificationText,
          type: ExplanationStepType.process,
        ),
        ExplanationStep(
          stepNumber: 3,
          title: '思维方式',
          description: thinkingMethod,
          type: ExplanationStepType.text,
        ),
      ],
      optionExplanations: optionExplanations,
      conclusion: conclusion,
    );
  }

  @override
  List<Object?> get props => [
    keyPoint,
    verificationText,
    thinkingMethod,
    optionExplanations,
    conclusion,
  ];
}

/// 选项解释数据
@JsonSerializable()
class OptionExplanation with EquatableMixin {
  final String optionId; // 选项ID
  final String explanation; // 解释文本
  final bool isCorrect; // 是否正确
  final String? imagePath; // 可选的图片路径，用于显示"原图→镜像图"对比

  OptionExplanation({
    required this.optionId,
    required this.explanation,
    required this.isCorrect,
    this.imagePath,
  });

  factory OptionExplanation.fromJson(Map<String, dynamic> json) =>
      _$OptionExplanationFromJson(json);

  Map<String, dynamic> toJson() => _$OptionExplanationToJson(this);

  @override
  List<Object?> get props => [optionId, explanation, isCorrect, imagePath];
}

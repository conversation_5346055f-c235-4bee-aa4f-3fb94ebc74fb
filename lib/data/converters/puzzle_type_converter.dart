/// LogicLab Data层 - 谜题类型转换器
/// 
/// 负责在Core层和Domain层的枚举类型之间进行转换
library;

import '../../core/constants/app_constants.dart' as core;
import '../../domain/value_objects/puzzle_types.dart' as domain;

/// 谜题类型转换器
/// 
/// 提供Core层和Domain层枚举类型之间的双向转换
class PuzzleTypeConverter {
  const PuzzleTypeConverter._();

  /// 将Core层的PuzzleType转换为Domain层的PuzzleType
  static domain.PuzzleType coreToDomain(core.PuzzleType coreType) {
    switch (coreType) {
      case core.PuzzleType.graphicPattern3x3:
        return domain.PuzzleType.graphicPattern3x3;
      case core.PuzzleType.spatialVisualization:
        return domain.PuzzleType.spatialVisualization;
      case core.PuzzleType.numericLogic:
        return domain.PuzzleType.numericLogic;
      case core.PuzzleType.introToCoding:
        return domain.PuzzleType.introToCoding;
      case core.PuzzleType.mirrorSymmetry:
        return domain.PuzzleType.mirrorSymmetry;
    }
  }

  /// 将Domain层的PuzzleType转换为Core层的PuzzleType
  static core.PuzzleType domainToCore(domain.PuzzleType domainType) {
    switch (domainType) {
      case domain.PuzzleType.graphicPattern3x3:
        return core.PuzzleType.graphicPattern3x3;
      case domain.PuzzleType.spatialVisualization:
        return core.PuzzleType.spatialVisualization;
      case domain.PuzzleType.numericLogic:
        return core.PuzzleType.numericLogic;
      case domain.PuzzleType.introToCoding:
        return core.PuzzleType.introToCoding;
      case domain.PuzzleType.mirrorSymmetry:
        return core.PuzzleType.mirrorSymmetry;
    }
  }

  /// 将Core层的DifficultyLevel转换为Domain层的DifficultyLevel
  static domain.DifficultyLevel coreToDomainDifficulty(core.DifficultyLevel coreLevel) {
    switch (coreLevel) {
      case core.DifficultyLevel.easy:
        return domain.DifficultyLevel.easy;
      case core.DifficultyLevel.medium:
        return domain.DifficultyLevel.medium;
      case core.DifficultyLevel.hard:
        return domain.DifficultyLevel.hard;
      case core.DifficultyLevel.expert:
        return domain.DifficultyLevel.expert;
    }
  }

  /// 将Domain层的DifficultyLevel转换为Core层的DifficultyLevel
  static core.DifficultyLevel domainToCoreDifficulty(domain.DifficultyLevel domainLevel) {
    switch (domainLevel) {
      case domain.DifficultyLevel.easy:
        return core.DifficultyLevel.easy;
      case domain.DifficultyLevel.medium:
        return core.DifficultyLevel.medium;
      case domain.DifficultyLevel.hard:
        return core.DifficultyLevel.hard;
      case domain.DifficultyLevel.expert:
        return core.DifficultyLevel.expert;
    }
  }

  /// 将Core层的ThemeWorld转换为Domain层的ThemeWorld
  static domain.ThemeWorld? coreToDomainTheme(core.ThemeWorld? coreTheme) {
    if (coreTheme == null) return null;
    
    switch (coreTheme) {
      case core.ThemeWorld.forest:
        return domain.ThemeWorld.forest;
      case core.ThemeWorld.ocean:
        return domain.ThemeWorld.ocean;
      case core.ThemeWorld.space:
        return domain.ThemeWorld.space;
    }
  }

  /// 将Domain层的ThemeWorld转换为Core层的ThemeWorld
  static core.ThemeWorld? domainToCoreTheme(domain.ThemeWorld? domainTheme) {
    if (domainTheme == null) return null;
    
    switch (domainTheme) {
      case domain.ThemeWorld.forest:
        return core.ThemeWorld.forest;
      case domain.ThemeWorld.ocean:
        return core.ThemeWorld.ocean;
      case domain.ThemeWorld.space:
        return core.ThemeWorld.space;
    }
  }

  /// 批量转换谜题类型列表（Core -> Domain）
  static List<domain.PuzzleType> coreListToDomainList(List<core.PuzzleType> coreList) {
    return coreList.map(coreToDomain).toList();
  }

  /// 批量转换谜题类型列表（Domain -> Core）
  static List<core.PuzzleType> domainListToCoreList(List<domain.PuzzleType> domainList) {
    return domainList.map(domainToCore).toList();
  }

  /// 根据字符串ID获取Domain层的PuzzleType
  static domain.PuzzleType? puzzleTypeFromString(String id) {
    return domain.PuzzleType.fromId(id);
  }

  /// 根据字符串ID获取Domain层的DifficultyLevel
  static domain.DifficultyLevel? difficultyFromString(String id) {
    return domain.DifficultyLevel.fromId(id);
  }

  /// 根据字符串ID获取Domain层的ThemeWorld
  static domain.ThemeWorld? themeWorldFromString(String id) {
    return domain.ThemeWorld.fromId(id);
  }

  /// 将Domain层的PuzzleType转换为字符串ID
  static String puzzleTypeToString(domain.PuzzleType type) {
    return type.id;
  }

  /// 将Domain层的DifficultyLevel转换为字符串ID
  static String difficultyToString(domain.DifficultyLevel level) {
    return level.id;
  }

  /// 将Domain层的ThemeWorld转换为字符串ID
  static String? themeWorldToString(domain.ThemeWorld? world) {
    return world?.id;
  }

  /// 验证类型转换的一致性
  static bool validateTypeConsistency() {
    try {
      // 测试PuzzleType转换
      for (final coreType in core.PuzzleType.values) {
        final domainType = coreToDomain(coreType);
        final backToCore = domainToCore(domainType);
        if (backToCore != coreType) return false;
      }

      // 测试DifficultyLevel转换
      for (final coreLevel in core.DifficultyLevel.values) {
        final domainLevel = coreToDomainDifficulty(coreLevel);
        final backToCore = domainToCoreDifficulty(domainLevel);
        if (backToCore != coreLevel) return false;
      }

      // 测试ThemeWorld转换
      for (final coreTheme in core.ThemeWorld.values) {
        final domainTheme = coreToDomainTheme(coreTheme);
        final backToCore = domainToCoreTheme(domainTheme);
        if (backToCore != coreTheme) return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}

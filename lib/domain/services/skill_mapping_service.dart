/// LogicLab Domain层 - 技能映射服务
/// 
/// 提供谜题类型到技能类型的映射逻辑
/// 纯业务逻辑，不依赖任何外部框架
library;

import '../value_objects/puzzle_types.dart';

/// 技能映射服务
/// 
/// 负责处理谜题类型与技能类型之间的映射关系
/// 这是Domain层的核心业务逻辑
class SkillMappingService {
  // 私有构造函数，使用静态方法
  const SkillMappingService._();

  /// 根据谜题类型获取对应的技能类型
  /// 
  /// [puzzleType] 谜题类型枚举
  /// 返回对应的技能类型
  static SkillType getSkillTypeFromPuzzleType(PuzzleType puzzleType) {
    switch (puzzleType) {
      case PuzzleType.numericLogic:
        return SkillType.logic;
        
      case PuzzleType.spatialVisualization:
      case PuzzleType.mirrorSymmetry:
      case PuzzleType.graphicPattern3x3:
        return SkillType.spatial;
        
      case PuzzleType.introToCoding:
        return SkillType.coding;
    }
  }

  /// 根据关卡ID获取技能类型
  /// 
  /// [levelId] 关卡ID，格式如: logic_1, spatial_2, coding_3
  /// 返回对应的技能类型，如果无法识别则返回逻辑技能
  static SkillType getSkillTypeFromLevelId(String levelId) {
    if (levelId.startsWith('logic_') || levelId.startsWith('numeric_')) {
      return SkillType.logic;
    } else if (levelId.startsWith('spatial_') || levelId.startsWith('mirror_')) {
      return SkillType.spatial;
    } else if (levelId.startsWith('coding_')) {
      return SkillType.coding;
    } else {
      // 默认返回逻辑技能
      return SkillType.logic;
    }
  }

  /// 根据技能类型获取相关的谜题类型列表
  /// 
  /// [skillType] 技能类型
  /// 返回该技能类型对应的所有谜题类型
  static List<PuzzleType> getPuzzleTypesForSkill(SkillType skillType) {
    switch (skillType.id) {
      case 'logic':
        return [PuzzleType.numericLogic];
        
      case 'spatial':
        return [
          PuzzleType.spatialVisualization,
          PuzzleType.mirrorSymmetry,
          PuzzleType.graphicPattern3x3,
        ];
        
      case 'coding':
        return [PuzzleType.introToCoding];
        
      default:
        return [];
    }
  }

  /// 检查谜题类型是否属于指定技能类型
  /// 
  /// [puzzleType] 谜题类型
  /// [skillType] 技能类型
  /// 返回是否匹配
  static bool isPuzzleTypeOfSkill(PuzzleType puzzleType, SkillType skillType) {
    final mappedSkillType = getSkillTypeFromPuzzleType(puzzleType);
    return mappedSkillType.id == skillType.id;
  }

  /// 获取技能类型的权重映射
  /// 
  /// 用于计算用户在不同技能类型上的表现权重
  /// 返回技能类型ID到权重的映射
  static Map<String, double> getSkillTypeWeights() {
    return {
      SkillType.logic.id: 1.0,
      SkillType.spatial.id: 1.2, // 空间技能稍微重要一些
      SkillType.coding.id: 1.1,
    };
  }

  /// 根据用户技能点数推荐合适的谜题类型
  /// 
  /// [userSkillPoints] 用户在各技能类型上的点数
  /// [excludeTypes] 要排除的谜题类型
  /// 返回推荐的谜题类型列表，按推荐度排序
  static List<PuzzleType> recommendPuzzleTypes(
    Map<String, int> userSkillPoints, {
    List<PuzzleType> excludeTypes = const [],
  }) {
    final recommendations = <PuzzleType, double>{};
    
    for (final puzzleType in PuzzleType.values) {
      if (excludeTypes.contains(puzzleType)) continue;
      
      final skillType = getSkillTypeFromPuzzleType(puzzleType);
      final userPoints = userSkillPoints[skillType.id] ?? 0;
      final weight = getSkillTypeWeights()[skillType.id] ?? 1.0;
      
      // 计算推荐度：技能点数越低，推荐度越高（帮助用户提升弱项）
      final recommendationScore = weight / (userPoints + 1);
      recommendations[puzzleType] = recommendationScore;
    }
    
    // 按推荐度排序
    final sortedEntries = recommendations.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedEntries.map((entry) => entry.key).toList();
  }

  /// 计算用户的技能平衡度
  /// 
  /// [userSkillPoints] 用户在各技能类型上的点数
  /// 返回0-1之间的平衡度分数，1表示完全平衡
  static double calculateSkillBalance(Map<String, int> userSkillPoints) {
    final skillTypes = SkillType.getAllSkillTypes();
    final points = skillTypes.map((skill) => userSkillPoints[skill.id] ?? 0).toList();
    
    if (points.isEmpty) return 1.0;
    
    final average = points.reduce((a, b) => a + b) / points.length;
    if (average == 0) return 1.0;
    
    // 计算方差
    final variance = points
        .map((point) => (point - average) * (point - average))
        .reduce((a, b) => a + b) / points.length;
    
    // 将方差转换为0-1的平衡度分数
    final maxVariance = average * average; // 最大可能的方差
    final balance = 1.0 - (variance / maxVariance).clamp(0.0, 1.0);
    
    return balance;
  }

  /// 获取用户的主要技能类型
  /// 
  /// [userSkillPoints] 用户在各技能类型上的点数
  /// 返回点数最高的技能类型
  static SkillType? getPrimarySkillType(Map<String, int> userSkillPoints) {
    if (userSkillPoints.isEmpty) return null;
    
    String? maxSkillId;
    int maxPoints = -1;
    
    for (final entry in userSkillPoints.entries) {
      if (entry.value > maxPoints) {
        maxPoints = entry.value;
        maxSkillId = entry.key;
      }
    }
    
    return maxSkillId != null ? SkillType.fromId(maxSkillId) : null;
  }

  /// 获取用户需要提升的技能类型
  /// 
  /// [userSkillPoints] 用户在各技能类型上的点数
  /// 返回点数最低的技能类型
  static SkillType? getWeakestSkillType(Map<String, int> userSkillPoints) {
    final skillTypes = SkillType.getAllSkillTypes();
    
    SkillType? weakestSkill;
    int minPoints = double.maxFinite.toInt();
    
    for (final skillType in skillTypes) {
      final points = userSkillPoints[skillType.id] ?? 0;
      if (points < minPoints) {
        minPoints = points;
        weakestSkill = skillType;
      }
    }
    
    return weakestSkill;
  }
}

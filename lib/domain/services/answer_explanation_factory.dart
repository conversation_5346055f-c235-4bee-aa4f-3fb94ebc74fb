import '../../data/models/puzzle.dart';

/// 答案解析工厂
/// 
/// 为不同游戏类型创建标准化的答案解析数据
class AnswerExplanationFactory {
  
  /// 为图形推理创建解析
  static UniversalAnswerExplanation createGraphicPatternExplanation({
    required String correctAnswer,
    required List<String> options,
    required String pattern,
  }) {
    return UniversalAnswerExplanation(
      puzzleType: 'graphicPattern',
      keyPoint: '观察图形的变化规律，找出缺失部分的正确图案',
      verificationText: '通过分析行列关系验证答案的正确性',
      thinkingMethod: '系统性观察：先看行，再看列，最后找规律',
      steps: [
        ExplanationStep(
          stepNumber: 1,
          title: '观察行变化',
          description: '分析每一行图形的变化规律，寻找共同特征',
          type: ExplanationStepType.visual,
          tip: '注意图形的形状、颜色、方向等属性',
        ),
        ExplanationStep(
          stepNumber: 2,
          title: '观察列变化',
          description: '分析每一列图形的变化规律，确认模式',
          type: ExplanationStepType.visual,
          tip: '列的规律可能与行的规律不同',
        ),
        ExplanationStep(
          stepNumber: 3,
          title: '应用规律',
          description: '将发现的规律应用到空白位置，确定答案',
          type: ExplanationStepType.process,
          tip: '确保答案同时满足行和列的规律',
        ),
      ],
      optionExplanations: _createGraphicPatternOptions(options, correctAnswer, pattern),
      conclusion: '通过系统性分析图形的行列规律，可以准确找出正确答案',
    );
  }

  /// 为空间想象创建解析
  static UniversalAnswerExplanation createSpatialVisualizationExplanation({
    required String correctAnswer,
    required List<String> options,
    required String expandedShape,
  }) {
    return UniversalAnswerExplanation(
      puzzleType: 'spatialVisualization',
      keyPoint: '将2D展开图在脑中折叠成3D立体图形',
      verificationText: '检查每个面的相对位置和连接关系',
      thinkingMethod: '空间想象：标记关键面，想象折叠过程',
      steps: [
        ExplanationStep(
          stepNumber: 1,
          title: '标记关键面',
          description: '在展开图上标记有特殊图案或颜色的面',
          type: ExplanationStepType.interactive,
          tip: '选择最容易识别的面作为参考点',
        ),
        ExplanationStep(
          stepNumber: 2,
          title: '想象折叠',
          description: '按照折叠线将展开图在脑中折叠成立体图形',
          type: ExplanationStepType.process,
          tip: '一步一步慢慢折叠，不要急于求成',
        ),
        ExplanationStep(
          stepNumber: 3,
          title: '验证位置',
          description: '检查关键面在立体图形中的相对位置',
          type: ExplanationStepType.comparison,
          tip: '相邻的面在立体图形中应该保持相邻关系',
        ),
      ],
      optionExplanations: _createSpatialVisualizationOptions(options, correctAnswer),
      conclusion: '通过空间想象和逻辑验证，可以准确判断展开图对应的立体图形',
    );
  }

  /// 为数字逻辑创建解析
  static UniversalAnswerExplanation createNumericLogicExplanation({
    required Map<String, dynamic> constraints,
    required List<String> availableItems,
  }) {
    return UniversalAnswerExplanation(
      puzzleType: 'numericLogic',
      keyPoint: '根据约束条件，使用逻辑推理填充网格',
      verificationText: '确保所有约束条件都得到满足',
      thinkingMethod: '约束满足：分析条件，逐步推理',
      steps: [
        ExplanationStep(
          stepNumber: 1,
          title: '理解约束',
          description: '仔细阅读并理解所有约束条件',
          type: ExplanationStepType.rule,
          tip: '每个约束都是解题的关键信息',
        ),
        ExplanationStep(
          stepNumber: 2,
          title: '寻找突破口',
          description: '找到约束最强的位置，从这里开始推理',
          type: ExplanationStepType.process,
          tip: '通常从只有一种可能的位置开始',
        ),
        ExplanationStep(
          stepNumber: 3,
          title: '逐步推理',
          description: '基于已确定的信息，逐步推导其他位置',
          type: ExplanationStepType.process,
          tip: '每一步都要验证是否违反约束条件',
        ),
        ExplanationStep(
          stepNumber: 4,
          title: '验证答案',
          description: '检查最终答案是否满足所有约束条件',
          type: ExplanationStepType.comparison,
          tip: '完整的解答应该满足每一个约束',
        ),
      ],
      optionExplanations: [], // 数字逻辑通常没有选择题形式
      conclusion: '通过系统性的逻辑推理，可以找到满足所有约束的唯一解',
    );
  }

  /// 为编程启蒙创建解析
  static UniversalAnswerExplanation createCodingExplanation({
    required List<String> solution,
    required Map<String, int> startPosition,
    required Map<String, int> endPosition,
  }) {
    return UniversalAnswerExplanation(
      puzzleType: 'coding',
      keyPoint: '设计最优路径，使用最少的指令到达目标',
      verificationText: '模拟执行指令序列，确认能够到达终点',
      thinkingMethod: '算法思维：分解问题，设计步骤',
      steps: [
        ExplanationStep(
          stepNumber: 1,
          title: '分析路径',
          description: '观察起点和终点，规划可能的移动路径',
          type: ExplanationStepType.visual,
          tip: '寻找最短路径，避开障碍物',
        ),
        ExplanationStep(
          stepNumber: 2,
          title: '设计算法',
          description: '将路径转换为具体的移动指令序列',
          type: ExplanationStepType.process,
          tip: '考虑使用循环来减少指令数量',
        ),
        ExplanationStep(
          stepNumber: 3,
          title: '优化方案',
          description: '检查是否可以用更少的指令完成任务',
          type: ExplanationStepType.comparison,
          tip: '重复的动作可以用循环指令替代',
        ),
        ExplanationStep(
          stepNumber: 4,
          title: '测试执行',
          description: '模拟执行指令序列，验证结果',
          type: ExplanationStepType.interactive,
          tip: '逐步执行每个指令，确认路径正确',
        ),
      ],
      optionExplanations: [], // 编程启蒙通常是构造题
      conclusion: '通过算法思维和逻辑推理，可以设计出高效的解决方案',
      additionalData: {
        'solution': solution,
        'startPosition': startPosition,
        'endPosition': endPosition,
      },
    );
  }

  /// 创建图形推理选项解释
  static List<OptionExplanation> _createGraphicPatternOptions(
    List<String> options,
    String correctAnswer,
    String pattern,
  ) {
    return options.map((option) {
      final isCorrect = option == correctAnswer;
      return OptionExplanation(
        optionId: option,
        explanation: isCorrect
            ? '正确答案：符合$pattern的变化规律'
            : '错误选项：不符合图形的变化规律',
        isCorrect: isCorrect,
      );
    }).toList();
  }

  /// 创建空间想象选项解释
  static List<OptionExplanation> _createSpatialVisualizationOptions(
    List<String> options,
    String correctAnswer,
  ) {
    return options.map((option) {
      final isCorrect = option == correctAnswer;
      return OptionExplanation(
        optionId: option,
        explanation: isCorrect
            ? '正确答案：与展开图完全对应的立体图形'
            : '错误选项：面的相对位置与展开图不符',
        isCorrect: isCorrect,
      );
    }).toList();
  }

  /// 为镜像对称创建现代化解析（替代旧版本）
  static UniversalAnswerExplanation createMirrorSymmetryExplanation({
    required String correctAnswer,
    required List<String> options,
    required String mirrorDirection,
    required String originalImage,
  }) {
    return UniversalAnswerExplanation(
      puzzleType: 'mirrorSymmetry',
      keyPoint: '理解镜像对称的概念，找出正确的镜像图形',
      verificationText: '通过对称轴验证镜像关系的正确性',
      thinkingMethod: '对称思维：确定对称轴，逐点对应',
      steps: [
        ExplanationStep(
          stepNumber: 1,
          title: '确定对称轴',
          description: '根据题目要求确定${mirrorDirection == 'horizontal' ? '水平' : '垂直'}对称轴的位置',
          type: ExplanationStepType.visual,
          tip: '对称轴是镜像变换的基准线',
        ),
        ExplanationStep(
          stepNumber: 2,
          title: '分析关键点',
          description: '选择原图中的关键特征点，分析它们的镜像位置',
          type: ExplanationStepType.process,
          tip: '关注图形的边界、角点和特殊标记',
        ),
        ExplanationStep(
          stepNumber: 3,
          title: '验证对称性',
          description: '检查镜像图形中每个点是否与原图形成正确的对称关系',
          type: ExplanationStepType.comparison,
          tip: '对称点到对称轴的距离应该相等',
        ),
      ],
      optionExplanations: options.map((option) {
        final isCorrect = option == correctAnswer;
        return OptionExplanation(
          optionId: option,
          explanation: isCorrect
              ? '正确答案：完全符合${mirrorDirection == 'horizontal' ? '水平' : '垂直'}镜像对称关系'
              : '错误选项：不符合镜像对称的几何关系',
          isCorrect: isCorrect,
        );
      }).toList(),
      conclusion: '通过理解对称轴和镜像关系，可以准确识别正确的镜像图形',
      additionalData: {
        'mirrorDirection': mirrorDirection,
        'originalImage': originalImage,
      },
    );
  }
}

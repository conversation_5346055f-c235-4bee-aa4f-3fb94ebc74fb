import '../entities/puzzle_entity.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/result.dart';

/// 谜题仓库抽象接口（保持向后兼容）
/// 定义谜题数据操作的契约，不依赖具体实现
/// 使用Result`<T>`模式进行统一的错误处理
abstract class PuzzleRepository {
  /// 初始化谜题数据
  Future<Result<void>> initialize();

  /// 根据ID获取谜题
  Future<Result<PuzzleEntity?>> getPuzzleById(String levelId);

  /// 获取指定类型的谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByType(PuzzleType type);

  /// 获取指定难度的谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByDifficulty(DifficultyLevel difficulty);

  /// 获取指定主题世界的谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByThemeWorld(ThemeWorld themeWorld);

  /// 获取所有谜题
  Future<Result<List<PuzzleEntity>>> getAllPuzzles();

  /// 获取推荐谜题
  Future<Result<List<PuzzleEntity>>> getRecommendedPuzzles({
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
    int limit = 10,
  });

  /// 获取下一个谜题
  Future<Result<PuzzleEntity?>> getNextPuzzle({
    required String currentLevelId,
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
  });

  /// 验证谜题答案
  Future<Result<bool>> validateAnswer(PuzzleEntity puzzle, dynamic userAnswer);

  /// 生成谜题提示
  Future<Result<Map<String, dynamic>>> generateHint(PuzzleEntity puzzle, int hintLevel);

  /// 搜索谜题
  Future<Result<List<PuzzleEntity>>> searchPuzzles({
    String? keyword,
    PuzzleType? type,
    DifficultyLevel? difficulty,
    ThemeWorld? themeWorld,
    List<String>? tags,
  });

  /// 获取谜题统计信息
  Future<Result<Map<String, dynamic>>> getPuzzleStats();
}

/// 谜题查询仓库接口
/// 
/// 按照接口隔离原则，专门处理谜题查询相关操作
abstract class PuzzleQueryRepository {
  /// 根据ID获取谜题
  /// 
  /// [levelId] 谜题ID
  /// 返回 Result`<PuzzleEntity?>`谜题信息，如果不存在则为null
  Future<Result<PuzzleEntity?>> getPuzzleById(String levelId);

  /// 批量获取谜题
  /// 
  /// [levelIds] 谜题ID列表
  /// 返回 Result`<List<PuzzleEntity>>` 谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByIds(List<String> levelIds);

  /// 获取指定类型的谜题列表
  /// 
  /// [type] 谜题类型
  /// 返回 Result`<List<PuzzleEntity>>` 谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByType(PuzzleType type);

  /// 获取指定难度的谜题列表
  /// 
  /// [difficulty] 难度等级
  /// 返回 Result`<List<PuzzleEntity>>` 谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByDifficulty(DifficultyLevel difficulty);

  /// 获取指定主题世界的谜题列表
  /// 
  /// [themeWorld] 主题世界
  /// 返回 Result`<List<PuzzleEntity>>` 谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesByThemeWorld(ThemeWorld themeWorld);

  /// 获取所有谜题
  /// 
  /// 返回 Result`<List<PuzzleEntity>>` 所有谜题列表
  Future<Result<List<PuzzleEntity>>> getAllPuzzles();

  /// 分页获取谜题
  /// 
  /// [page] 页码（从1开始）
  /// [pageSize] 每页数量
  /// 返回 Result`<List<PuzzleEntity>>` 谜题列表
  Future<Result<List<PuzzleEntity>>> getPuzzlesPaginated({
    int page = 1,
    int pageSize = 20,
  });

  /// 搜索谜题
  /// 
  /// [keyword] 关键词
  /// [type] 谜题类型
  /// [difficulty] 难度等级
  /// [themeWorld] 主题世界
  /// [tags] 标签列表
  /// [minDifficulty] 最小难度
  /// [maxDifficulty] 最大难度
  /// 返回 Result`<List<PuzzleEntity>>` 搜索结果
  Future<Result<List<PuzzleEntity>>> searchPuzzles({
    String? keyword,
    PuzzleType? type,
    DifficultyLevel? difficulty,
    ThemeWorld? themeWorld,
    List<String>? tags,
    int? minDifficulty,
    int? maxDifficulty,
  });

  /// 获取谜题统计信息
  /// 
  /// 返回 Result`<Map<String, dynamic>>` 统计信息
  Future<Result<Map<String, dynamic>>> getPuzzleStats();

  /// 获取谜题数量
  /// 
  /// [type] 可选的类型过滤
  /// [difficulty] 可选的难度过滤
  /// 返回 Result`<int>` 谜题数量
  Future<Result<int>> getPuzzleCount({
    PuzzleType? type,
    DifficultyLevel? difficulty,
  });

  /// 检查谜题是否存在
  /// 
  /// [levelId] 谜题ID
  /// 返回 Result`<bool>` 是否存在
  Future<Result<bool>> puzzleExists(String levelId);

  /// 获取谜题标签列表
  /// 
  /// 返回 Result`<List<String>>` 所有使用的标签
  Future<Result<List<String>>> getAllTags();

  /// 获取相关谜题
  /// 
  /// [levelId] 基准谜题ID
  /// [limit] 返回数量限制
  /// 返回 Result`<List<PuzzleEntity>>` 相关谜题列表
  Future<Result<List<PuzzleEntity>>> getRelatedPuzzles(
    String levelId, {
    int limit = 5,
  });
}

/// 谜题推荐仓库接口
/// 
/// 专门处理谜题推荐算法相关操作
abstract class PuzzleRecommendationRepository {
  /// 获取推荐谜题
  /// 
  /// [userSkillPoints] 用户技能点数
  /// [completedLevels] 已完成关卡列表
  /// [limit] 推荐数量限制
  /// 返回 Result`<List<PuzzleEntity>>` 推荐谜题列表
  Future<Result<List<PuzzleEntity>>> getRecommendedPuzzles({
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
    int limit = 10,
  });

  /// 获取下一个谜题
  /// 
  /// [currentLevelId] 当前谜题ID
  /// [userSkillPoints] 用户技能点数
  /// [completedLevels] 已完成关卡列表
  /// 返回 Result`<PuzzleEntity?>` 下一个谜题，如果没有则为null
  Future<Result<PuzzleEntity?>> getNextPuzzle({
    required String currentLevelId,
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
  });

  /// 获取适合的谜题
  /// 
  /// [skillLevel] 技能等级
  /// [preferredTypes] 偏好类型
  /// [excludeCompleted] 是否排除已完成的
  /// [limit] 数量限制
  /// 返回 Result`<List<PuzzleEntity>>` 适合的谜题列表
  Future<Result<List<PuzzleEntity>>> getSuitablePuzzles({
    required int skillLevel,
    List<PuzzleType>? preferredTypes,
    bool excludeCompleted = true,
    int limit = 10,
  });

  /// 获取挑战谜题
  /// 
  /// [userSkillPoints] 用户技能点数
  /// [limit] 数量限制
  /// 返回 `Result<List<PuzzleEntity>>` 挑战谜题列表
  Future<Result<List<PuzzleEntity>>> getChallengePuzzles({
    required Map<String, int> userSkillPoints,
    int limit = 5,
  });

  /// 获取复习谜题
  /// 
  /// [completedLevels] 已完成关卡列表
  /// [daysSinceCompleted] 完成后的天数
  /// [limit] 数量限制
  /// 返回 `Result<List<PuzzleEntity>>` 复习谜题列表
  Future<Result<List<PuzzleEntity>>> getReviewPuzzles({
    required List<String> completedLevels,
    int daysSinceCompleted = 7,
    int limit = 5,
  });

  /// 更新推荐算法参数
  /// 
  /// [userId] 用户ID
  /// [preferences] 用户偏好
  /// 返回 `Result<void>` 更新结果
  Future<Result<void>> updateRecommendationPreferences(
    String userId,
    Map<String, dynamic> preferences,
  );
}

/// 谜题验证仓库接口
/// 
/// 专门处理谜题答案验证和提示生成
abstract class PuzzleValidationRepository {
  /// 验证谜题答案
  /// 
  /// [puzzle] 谜题实体
  /// [userAnswer] 用户答案
  /// 返回 `Result<bool>` 是否正确
  Future<Result<bool>> validateAnswer(PuzzleEntity puzzle, dynamic userAnswer);

  /// 批量验证答案
  /// 
  /// [validations] 验证请求列表
  /// 返回 `Result<List<bool>>` 验证结果列表
  Future<Result<List<bool>>> validateAnswers(
    List<({PuzzleEntity puzzle, dynamic userAnswer})> validations,
  );

  /// 获取答案解析
  /// 
  /// [puzzle] 谜题实体
  /// [userAnswer] 用户答案
  /// 返回 `Result<Map<String, dynamic>>` 答案解析信息
  Future<Result<Map<String, dynamic>>> getAnswerExplanation(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  );

  /// 检查答案格式
  /// 
  /// [puzzle] 谜题实体
  /// [userAnswer] 用户答案
  /// 返回 `Result<bool>` 格式是否正确
  Future<Result<bool>> validateAnswerFormat(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  );

  /// 获取答案相似度
  /// 
  /// [puzzle] 谜题实体
  /// [userAnswer] 用户答案
  /// 返回 `Result<double>` 相似度（0-1）
  Future<Result<double>> getAnswerSimilarity(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  );
}

/// 谜题提示仓库接口
/// 
/// 专门处理谜题提示生成和管理
abstract class PuzzleHintRepository {
  /// 生成谜题提示
  /// 
  /// [puzzle] 谜题实体
  /// [hintLevel] 提示级别（1-3）
  /// 返回 `Result<Map<String, dynamic>>` 提示信息
  Future<Result<Map<String, dynamic>>> generateHint(
    PuzzleEntity puzzle,
    int hintLevel,
  );

  /// 批量生成提示
  /// 
  /// [requests] 提示请求列表
  /// 返回 `Result<List<Map<String, dynamic>>>` 提示信息列表
  Future<Result<List<Map<String, dynamic>>>> generateHints(
    List<({PuzzleEntity puzzle, int hintLevel})> requests,
  );

  /// 获取所有可用提示
  /// 
  /// [puzzle] 谜题实体
  /// 返回 `Result<List<Map<String, dynamic>>>` 所有提示级别的信息
  Future<Result<List<Map<String, dynamic>>>> getAllHints(PuzzleEntity puzzle);

  /// 检查提示是否可用
  /// 
  /// [puzzle] 谜题实体
  /// [hintLevel] 提示级别
  /// 返回 `Result<bool>` 是否可用
  Future<Result<bool>> isHintAvailable(PuzzleEntity puzzle, int hintLevel);

  /// 获取提示使用统计
  /// 
  /// [levelId] 谜题ID
  /// 返回 `Result<Map<String, dynamic>>` 提示使用统计
  Future<Result<Map<String, dynamic>>> getHintUsageStats(String levelId);

  /// 更新提示质量评分
  /// 
  /// [levelId] 谜题ID
  /// [hintLevel] 提示级别
  /// [rating] 评分（1-5）
  /// 返回 `Result<void>` 更新结果
  Future<Result<void>> updateHintRating(
    String levelId,
    int hintLevel,
    int rating,
  );
}

/// 谜题管理仓库接口
/// 
/// 专门处理谜题的创建、更新、删除等管理操作
abstract class PuzzleManagementRepository {
  /// 初始化谜题数据
  /// 
  /// 返回 `Result<void>` 初始化结果
  Future<Result<void>> initialize();

  /// 创建谜题
  /// 
  /// [puzzle] 谜题实体
  /// 返回 `Result<PuzzleEntity>` 创建的谜题
  Future<Result<PuzzleEntity>> createPuzzle(PuzzleEntity puzzle);

  /// 更新谜题
  /// 
  /// [puzzle] 谜题实体
  /// 返回 `Result<PuzzleEntity>` 更新后的谜题
  Future<Result<PuzzleEntity>> updatePuzzle(PuzzleEntity puzzle);

  /// 删除谜题
  /// 
  /// [levelId] 谜题ID
  /// 返回 `Result<void>` 删除结果
  Future<Result<void>> deletePuzzle(String levelId);

  /// 批量创建谜题
  /// 
  /// [puzzles] 谜题列表
  /// 返回 `Result<List<PuzzleEntity>>` 创建的谜题列表
  Future<Result<List<PuzzleEntity>>> createPuzzles(List<PuzzleEntity> puzzles);

  /// 批量更新谜题
  /// 
  /// [puzzles] 谜题列表
  /// 返回 `Result<void>` 更新结果
  Future<Result<void>> updatePuzzles(List<PuzzleEntity> puzzles);

  /// 批量删除谜题
  /// 
  /// [levelIds] 谜题ID列表
  /// 返回 `Result<void>` 删除结果
  Future<Result<void>> deletePuzzles(List<String> levelIds);

  /// 导入谜题数据
  /// 
  /// [data] 导入数据
  /// [format] 数据格式（json, csv等）
  /// 返回 `Result<int>` 导入的谜题数量
  Future<Result<int>> importPuzzles(
    String data, {
    String format = 'json',
  });

  /// 导出谜题数据
  /// 
  /// [levelIds] 要导出的谜题ID列表，为空则导出全部
  /// [format] 导出格式
  /// 返回 `Result<String>` 导出的数据
  Future<Result<String>> exportPuzzles({
    List<String>? levelIds,
    String format = 'json',
  });

  /// 清理无效谜题
  /// 
  /// 返回 `Result<int>` 清理的谜题数量
  Future<Result<int>> cleanupInvalidPuzzles();

  /// 重建谜题索引
  /// 
  /// 返回 `Result<void>` 重建结果
  Future<Result<void>> rebuildIndex();
} 
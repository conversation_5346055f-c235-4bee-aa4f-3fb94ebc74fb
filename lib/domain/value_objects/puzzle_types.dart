/// LogicLab Domain层 - 谜题类型值对象
/// 
/// 定义纯业务逻辑的谜题相关枚举和值对象
/// 不依赖任何外部框架或基础设施层
library;

import 'package:equatable/equatable.dart';

/// 谜题类型枚举
/// 
/// 定义应用支持的所有谜题类型
enum PuzzleType {
  /// 图形推理 - 3x3网格模式识别
  graphicPattern3x3('GRAPHIC_PATTERN_3X3', '图形推理'),
  
  /// 空间想象 - 3D空间变换和折叠
  spatialVisualization('SPATIAL_VISUALIZATION', '空间想象'),
  
  /// 数字逻辑 - 数字约束和逻辑推理
  numericLogic('NUMERIC_LOGIC', '数字逻辑'),
  
  /// 编程启蒙 - 算法思维和指令序列
  introToCoding('INTRO_TO_CODING', '编程启蒙'),
  
  /// 镜像对称 - 几何对称和空间关系
  mirrorSymmetry('MIRROR_SYMMETRY', '镜像对称');

  const PuzzleType(this.id, this.displayName);
  
  /// 唯一标识符
  final String id;
  
  /// 显示名称
  final String displayName;

  /// 根据ID获取谜题类型
  static PuzzleType? fromId(String id) {
    for (final type in PuzzleType.values) {
      if (type.id == id) return type;
    }
    return null;
  }

  /// 获取所有谜题类型的ID列表
  static List<String> getAllIds() {
    return PuzzleType.values.map((type) => type.id).toList();
  }

  /// 获取所有谜题类型的显示名称列表
  static List<String> getAllDisplayNames() {
    return PuzzleType.values.map((type) => type.displayName).toList();
  }
}

/// 难度等级枚举
/// 
/// 定义谜题的难度分级
enum DifficultyLevel {
  /// 简单 - 适合初学者
  easy('easy', '简单', 1),
  
  /// 中等 - 适合有一定基础的用户
  medium('medium', '中等', 2),
  
  /// 困难 - 适合有经验的用户
  hard('hard', '困难', 3),
  
  /// 专家 - 适合高级用户
  expert('expert', '专家', 4);

  const DifficultyLevel(this.id, this.displayName, this.level);
  
  /// 唯一标识符
  final String id;
  
  /// 显示名称
  final String displayName;
  
  /// 数字等级（1-4）
  final int level;

  /// 根据ID获取难度等级
  static DifficultyLevel? fromId(String id) {
    for (final difficulty in DifficultyLevel.values) {
      if (difficulty.id == id) return difficulty;
    }
    return null;
  }

  /// 根据数字等级获取难度等级
  static DifficultyLevel? fromLevel(int level) {
    for (final difficulty in DifficultyLevel.values) {
      if (difficulty.level == level) return difficulty;
    }
    return null;
  }

  /// 获取下一个难度等级
  DifficultyLevel? get next {
    final currentIndex = DifficultyLevel.values.indexOf(this);
    if (currentIndex < DifficultyLevel.values.length - 1) {
      return DifficultyLevel.values[currentIndex + 1];
    }
    return null;
  }

  /// 获取上一个难度等级
  DifficultyLevel? get previous {
    final currentIndex = DifficultyLevel.values.indexOf(this);
    if (currentIndex > 0) {
      return DifficultyLevel.values[currentIndex - 1];
    }
    return null;
  }

  /// 检查是否比指定难度更难
  bool isHarderThan(DifficultyLevel other) {
    return level > other.level;
  }

  /// 检查是否比指定难度更容易
  bool isEasierThan(DifficultyLevel other) {
    return level < other.level;
  }
}

/// 主题世界枚举
/// 
/// 定义游戏的主题世界背景
enum ThemeWorld {
  /// 奇妙森林 - 自然主题
  forest('forest', '奇妙森林', '🌲'),
  
  /// 蔚蓝海洋 - 海洋主题
  ocean('ocean', '蔚蓝海洋', '🌊'),
  
  /// 梦幻太空 - 科幻主题
  space('space', '梦幻太空', '🚀');

  const ThemeWorld(this.id, this.displayName, this.emoji);
  
  /// 唯一标识符
  final String id;
  
  /// 显示名称
  final String displayName;
  
  /// 表情符号
  final String emoji;

  /// 根据ID获取主题世界
  static ThemeWorld? fromId(String id) {
    for (final world in ThemeWorld.values) {
      if (world.id == id) return world;
    }
    return null;
  }

  /// 获取所有主题世界的ID列表
  static List<String> getAllIds() {
    return ThemeWorld.values.map((world) => world.id).toList();
  }

  /// 获取所有主题世界的显示名称列表
  static List<String> getAllDisplayNames() {
    return ThemeWorld.values.map((world) => world.displayName).toList();
  }
}

/// 技能类型值对象
/// 
/// 定义用户技能的分类和映射关系
class SkillType extends Equatable {
  /// 技能类型标识符
  final String id;
  
  /// 技能类型显示名称
  final String displayName;
  
  /// 技能类型描述
  final String description;

  const SkillType({
    required this.id,
    required this.displayName,
    required this.description,
  });

  // 预定义的技能类型
  static const SkillType logic = SkillType(
    id: 'logic',
    displayName: '逻辑推理',
    description: '分析问题、推理判断和逻辑思维能力',
  );

  static const SkillType spatial = SkillType(
    id: 'spatial',
    displayName: '空间想象',
    description: '空间感知、几何变换和立体思维能力',
  );

  static const SkillType coding = SkillType(
    id: 'coding',
    displayName: '编程思维',
    description: '算法思维、序列规划和计算思维能力',
  );

  /// 获取所有技能类型
  static List<SkillType> getAllSkillTypes() {
    return [logic, spatial, coding];
  }

  /// 根据ID获取技能类型
  static SkillType? fromId(String id) {
    for (final skillType in getAllSkillTypes()) {
      if (skillType.id == id) return skillType;
    }
    return null;
  }

  /// 检查技能类型是否有效
  static bool isValidSkillType(String id) {
    return fromId(id) != null;
  }

  @override
  List<Object?> get props => [id, displayName, description];
}

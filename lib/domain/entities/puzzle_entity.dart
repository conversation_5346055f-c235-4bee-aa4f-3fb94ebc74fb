import 'package:equatable/equatable.dart';

import '../value_objects/puzzle_types.dart';
import '../services/skill_mapping_service.dart';

/// 谜题领域实体 - 纯业务逻辑，不依赖任何外部数据源
class PuzzleEntity extends Equatable {
  final String levelId;
  final String schemaVersion;
  final String author;
  final List<String> tags;
  final PuzzleType puzzleType;
  final DifficultyLevel difficulty;
  final String prompt;
  final Map<String, dynamic> data;
  final ThemeWorld? themeWorld;
  final int? orderInWorld;
  final int? maxHints;
  final dynamic correctAnswer;

  const PuzzleEntity({
    required this.levelId,
    required this.schemaVersion,
    required this.author,
    required this.tags,
    required this.puzzleType,
    required this.difficulty,
    required this.prompt,
    required this.data,
    this.themeWorld,
    this.orderInWorld,
    this.maxHints,
    this.correctAnswer,
  });

  /// 获取谜题的完整标识符
  String get fullId => '${themeWorld?.id ?? 'default'}_$levelId';

  /// 检查是否为特定类型的谜题
  bool isType(PuzzleType type) => puzzleType == type;

  /// 检查是否为特定难度
  bool isDifficulty(DifficultyLevel level) => difficulty == level;

  /// 获取谜题的估计完成时间（分钟）
  int get estimatedTimeMinutes {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 2;
      case DifficultyLevel.medium:
        return 5;
      case DifficultyLevel.hard:
        return 8;
      case DifficultyLevel.expert:
        return 12;
    }
  }

  /// 获取基础得分（根据难度）
  int get baseScore {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 100;
      case DifficultyLevel.medium:
        return 200;
      case DifficultyLevel.hard:
        return 300;
      case DifficultyLevel.expert:
        return 500;
    }
  }

  /// 计算最终得分
  int calculateScore({
    required int timeSeconds,
    required int hintsUsed,
    required bool isCorrect,
  }) {
    if (!isCorrect) return 0;

    int score = baseScore;

    // 时间奖励（越快完成得分越高）
    final timeBonus = _calculateTimeBonus(timeSeconds);
    score += timeBonus;

    // 提示惩罚
    final hintPenalty = hintsUsed * 50;
    score = (score - hintPenalty).clamp(0, score);

    return score;
  }

  /// 计算时间奖励
  int _calculateTimeBonus(int timeSeconds) {
    final estimatedSeconds = estimatedTimeMinutes * 60;
    if (timeSeconds <= estimatedSeconds * 0.5) {
      return 100; // 超快完成
    } else if (timeSeconds <= estimatedSeconds * 0.8) {
      return 50; // 快速完成
    } else if (timeSeconds <= estimatedSeconds) {
      return 20; // 正常完成
    }
    return 0; // 超时完成
  }

  /// 获取星级评分（1-3星）
  int getStarRating({
    required int timeSeconds,
    required int hintsUsed,
    required bool isCorrect,
  }) {
    if (!isCorrect) return 0;

    final estimatedSeconds = estimatedTimeMinutes * 60;

    // 3星：快速完成且无提示
    if (timeSeconds <= estimatedSeconds * 0.8 && hintsUsed == 0) {
      return 3;
    }

    // 2星：正常完成或少量提示
    if (timeSeconds <= estimatedSeconds * 1.2 && hintsUsed <= 1) {
      return 2;
    }

    // 1星：完成即可
    return 1;
  }

  /// 检查是否适合当前用户水平
  bool isSuitableForUser({
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
  }) {
    final skillType = SkillMappingService.getSkillTypeFromPuzzleType(
      puzzleType,
    );
    final userLevel = (userSkillPoints[skillType.id] ?? 0) ~/ 100 + 1;

    // 根据用户等级推荐合适的难度
    switch (difficulty) {
      case DifficultyLevel.easy:
        return userLevel <= 3;
      case DifficultyLevel.medium:
        return userLevel >= 2 && userLevel <= 5;
      case DifficultyLevel.hard:
        return userLevel >= 4 && userLevel <= 8;
      case DifficultyLevel.expert:
        return userLevel >= 6;
    }
  }

  @override
  List<Object?> get props => [
    levelId,
    schemaVersion,
    author,
    tags,
    puzzleType,
    difficulty,
    prompt,
    data,
    themeWorld,
    orderInWorld,
    maxHints,
    correctAnswer,
  ];

  /// 转换为Map（用于序列化）
  Map<String, dynamic> toMap() => {
    'levelId': levelId,
    'schemaVersion': schemaVersion,
    'author': author,
    'tags': tags,
    'puzzleType': puzzleType.id,
    'difficulty': difficulty.id,
    'prompt': prompt,
    'data': data,
    'themeWorld': themeWorld?.id,
    'orderInWorld': orderInWorld,
    'maxHints': maxHints,
    'correctAnswer': correctAnswer,
  };
}

/// 游戏结果领域实体
class GameResultEntity extends Equatable {
  final String levelId;
  final bool completed;
  final int score;
  final int timeSeconds;
  final int hintsUsed;
  final int attempts;
  final DateTime completedAt;
  final int starRating;

  const GameResultEntity({
    required this.levelId,
    required this.completed,
    required this.score,
    required this.timeSeconds,
    required this.hintsUsed,
    required this.attempts,
    required this.completedAt,
    required this.starRating,
  });

  /// 是否为完美完成
  bool get isPerfect => completed && starRating == 3 && hintsUsed == 0;

  /// 获取技能点数奖励
  int get skillPointsReward {
    if (!completed) return 0;
    return starRating * 10 + (isPerfect ? 20 : 0);
  }

  /// 获取完成等级描述
  String get completionLevel {
    if (!completed) return '未完成';
    switch (starRating) {
      case 3:
        return isPerfect ? '完美' : '优秀';
      case 2:
        return '良好';
      case 1:
        return '及格';
      default:
        return '未完成';
    }
  }

  @override
  List<Object?> get props => [
    levelId,
    completed,
    score,
    timeSeconds,
    hintsUsed,
    attempts,
    completedAt,
    starRating,
  ];
}

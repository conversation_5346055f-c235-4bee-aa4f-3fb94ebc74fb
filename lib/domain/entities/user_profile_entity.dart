import 'package:equatable/equatable.dart';

/// 用户档案领域实体 - 纯业务逻辑，不依赖任何外部数据源
class UserProfileEntity extends Equatable {
  final String id;
  final String nickname;
  final String avatarId;
  final DateTime createdAt;
  final DateTime lastPlayedAt;
  final Map<String, LevelProgressEntity> levelProgress;
  final List<String> unlockedAchievements;
  final int totalPlayTimeMinutes;
  final Map<String, int> skillPoints;
  final int totalPoints;
  final int totalScore;
  final UserSettingsEntity settings;

  const UserProfileEntity({
    required this.id,
    required this.nickname,
    required this.avatarId,
    required this.createdAt,
    required this.lastPlayedAt,
    required this.levelProgress,
    required this.unlockedAchievements,
    required this.totalPlayTimeMinutes,
    required this.skillPoints,
    required this.totalPoints,
    required this.totalScore,
    required this.settings,
  });

  /// 创建新用户
  factory UserProfileEntity.create({
    required String nickname,
    required String avatarId,
  }) {
    final now = DateTime.now();
    return UserProfileEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      nickname: nickname,
      avatarId: avatarId,
      createdAt: now,
      lastPlayedAt: now,
      levelProgress: {},
      unlockedAchievements: [],
      totalPlayTimeMinutes: 0,
      skillPoints: {
        'pattern': 0,
        'spatial': 0,
        'numeric': 0,
        'coding': 0,
      },
      totalPoints: 0,
      totalScore: 0,
      settings: UserSettingsEntity.defaultSettings(),
    );
  }

  /// 获取总星星数
  int get totalStars {
    return levelProgress.values
        .map((progress) => progress.bestScore)
        .fold(0, (sum, stars) => sum + stars);
  }

  /// 获取完成的关卡数
  int get completedLevels {
    return levelProgress.values
        .where((progress) => progress.completed)
        .length;
  }

  /// 获取特定技能的等级
  int getSkillLevel(String skillType) {
    final points = skillPoints[skillType] ?? 0;
    // 每100点升一级
    return (points / 100).floor() + 1;
  }

  /// 获取当前等级（基于总技能点数）
  int get currentLevel {
    // 每100点升一级
    return (totalPoints / 100).floor() + 1;
  }

  /// 检查是否可以游戏（时间限制检查）
  bool canPlayToday() {
    // 检查今日已游戏时间
    final todayPlayTime = _getTodayPlayTime();
    return todayPlayTime < settings.dailyTimeLimitMinutes;
  }

  /// 获取今日游戏时间（分钟）
  int _getTodayPlayTime() {
    // 这里应该根据实际的游戏会话记录来计算
    // 暂时返回0，实际实现需要游戏会话数据
    return 0;
  }

  /// 复制并更新用户档案
  UserProfileEntity copyWith({
    String? nickname,
    String? avatarId,
    DateTime? lastPlayedAt,
    Map<String, LevelProgressEntity>? levelProgress,
    List<String>? unlockedAchievements,
    int? totalPlayTimeMinutes,
    Map<String, int>? skillPoints,
    int? totalPoints,
    int? totalScore,
    UserSettingsEntity? settings,
  }) {
    return UserProfileEntity(
      id: id,
      nickname: nickname ?? this.nickname,
      avatarId: avatarId ?? this.avatarId,
      createdAt: createdAt,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      levelProgress: levelProgress ?? Map.from(this.levelProgress),
      unlockedAchievements: unlockedAchievements ?? List.from(this.unlockedAchievements),
      totalPlayTimeMinutes: totalPlayTimeMinutes ?? this.totalPlayTimeMinutes,
      skillPoints: skillPoints ?? Map.from(this.skillPoints),
      totalPoints: totalPoints ?? this.totalPoints,
      totalScore: totalScore ?? this.totalScore,
      settings: settings ?? this.settings,
    );
  }

  /// 转换为Map（用于序列化）
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nickname': nickname,
      'avatarId': avatarId,
      'createdAt': createdAt.toIso8601String(),
      'lastPlayedAt': lastPlayedAt.toIso8601String(),
      'levelProgress': levelProgress.map(
        (key, value) => MapEntry(key, value.toMap()),
      ),
      'unlockedAchievements': unlockedAchievements,
      'totalPlayTimeMinutes': totalPlayTimeMinutes,
      'skillPoints': skillPoints,
      'totalPoints': totalPoints,
      'totalScore': totalScore,
      'settings': settings.toMap(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        nickname,
        avatarId,
        createdAt,
        lastPlayedAt,
        levelProgress,
        unlockedAchievements,
        totalPlayTimeMinutes,
        skillPoints,
        totalPoints,
        totalScore,
        settings,
      ];
}

/// 关卡进度领域实体
class LevelProgressEntity extends Equatable {
  final String levelId;
  final int bestScore; // 最高分数
  final int attempts;
  final bool completed;
  final DateTime? firstCompletedAt;
  final DateTime? lastAttemptAt;
  final int bestTimeSeconds;
  final int hintsUsed;
  final int starRating; // 星级评分 (0-3)

  const LevelProgressEntity({
    required this.levelId,
    required this.bestScore,
    required this.attempts,
    required this.completed,
    this.firstCompletedAt,
    this.lastAttemptAt,
    required this.bestTimeSeconds,
    required this.hintsUsed,
    required this.starRating,
  });

  /// 计算完成率（0.0 - 1.0）
  double get completionRate {
    if (!completed) return 0.0;
    return starRating / 3.0; // 基于星级评分
  }

  /// 是否为完美完成（3星且无提示）
  bool get isPerfect {
    return completed && starRating == 3 && hintsUsed == 0;
  }

  /// 更新进度
  LevelProgressEntity updateProgress({
    required int score,
    required int timeSeconds,
    required int hintsUsed,
    int? starRating,
  }) {
    final now = DateTime.now();
    final isFirstCompletion = !completed && score > 0;
    final isBetterScore = score > bestScore;
    final newStarRating = starRating ?? this.starRating;
    
    return LevelProgressEntity(
      levelId: levelId,
      bestScore: isBetterScore ? score : bestScore,
      attempts: attempts + 1,
      completed: completed || score > 0,
      firstCompletedAt: isFirstCompletion ? now : firstCompletedAt,
      lastAttemptAt: now,
      bestTimeSeconds: (isBetterScore || bestTimeSeconds == 0) ? timeSeconds : bestTimeSeconds,
      hintsUsed: (isBetterScore || this.hintsUsed == 0) ? hintsUsed : this.hintsUsed,
      starRating: newStarRating > this.starRating ? newStarRating : this.starRating,
    );
  }

  /// 复制并更新进度
  LevelProgressEntity copyWith({
    int? bestScore,
    int? attempts,
    bool? completed,
    DateTime? firstCompletedAt,
    DateTime? lastAttemptAt,
    int? bestTimeSeconds,
    int? hintsUsed,
    int? starRating,
  }) {
    return LevelProgressEntity(
      levelId: levelId,
      bestScore: bestScore ?? this.bestScore,
      attempts: attempts ?? this.attempts,
      completed: completed ?? this.completed,
      firstCompletedAt: firstCompletedAt ?? this.firstCompletedAt,
      lastAttemptAt: lastAttemptAt ?? this.lastAttemptAt,
      bestTimeSeconds: bestTimeSeconds ?? this.bestTimeSeconds,
      hintsUsed: hintsUsed ?? this.hintsUsed,
      starRating: starRating ?? this.starRating,
    );
  }

  /// 转换为Map（用于序列化）
  Map<String, dynamic> toMap() {
    return {
      'levelId': levelId,
      'bestScore': bestScore,
      'attempts': attempts,
      'completed': completed,
      'firstCompletedAt': firstCompletedAt?.toIso8601String(),
      'lastAttemptAt': lastAttemptAt?.toIso8601String(),
      'bestTimeSeconds': bestTimeSeconds,
      'hintsUsed': hintsUsed,
      'starRating': starRating,
    };
  }

  @override
  List<Object?> get props => [
        levelId,
        bestScore,
        attempts,
        completed,
        firstCompletedAt,
        lastAttemptAt,
        bestTimeSeconds,
        hintsUsed,
        starRating,
      ];
}

/// 用户设置领域实体
class UserSettingsEntity extends Equatable {
  final double musicVolume;
  final double sfxVolume;
  final bool enableVibration;
  final bool enableAnimations;
  final String language;
  final int dailyTimeLimitMinutes;
  final List<String> disabledTimeSlots;

  const UserSettingsEntity({
    required this.musicVolume,
    required this.sfxVolume,
    required this.enableVibration,
    required this.enableAnimations,
    required this.language,
    required this.dailyTimeLimitMinutes,
    required this.disabledTimeSlots,
  });

  /// 默认设置
  factory UserSettingsEntity.defaultSettings() {
    return const UserSettingsEntity(
      musicVolume: 0.7,
      sfxVolume: 0.8,
      enableVibration: true,
      enableAnimations: true,
      language: 'zh_CN',
      dailyTimeLimitMinutes: 60,
      disabledTimeSlots: [],
    );
  }

  /// 检查当前时间是否在禁用时段
  bool isCurrentTimeDisabled() {
    final now = DateTime.now();
    final currentTimeSlot = '${now.hour}:00-${now.hour + 1}:00';
    return disabledTimeSlots.contains(currentTimeSlot);
  }

  /// 复制并更新设置
  UserSettingsEntity copyWith({
    double? musicVolume,
    double? sfxVolume,
    bool? enableVibration,
    bool? enableAnimations,
    String? language,
    int? dailyTimeLimitMinutes,
    List<String>? disabledTimeSlots,
  }) {
    return UserSettingsEntity(
      musicVolume: musicVolume ?? this.musicVolume,
      sfxVolume: sfxVolume ?? this.sfxVolume,
      enableVibration: enableVibration ?? this.enableVibration,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      language: language ?? this.language,
      dailyTimeLimitMinutes: dailyTimeLimitMinutes ?? this.dailyTimeLimitMinutes,
      disabledTimeSlots: disabledTimeSlots ?? List.from(this.disabledTimeSlots),
    );
  }

  /// 转换为Map（用于序列化）
  Map<String, dynamic> toMap() {
    return {
      'musicVolume': musicVolume,
      'sfxVolume': sfxVolume,
      'enableVibration': enableVibration,
      'enableAnimations': enableAnimations,
      'language': language,
      'dailyTimeLimitMinutes': dailyTimeLimitMinutes,
      'disabledTimeSlots': disabledTimeSlots,
    };
  }

  @override
  List<Object?> get props => [
        musicVolume,
        sfxVolume,
        enableVibration,
        enableAnimations,
        language,
        dailyTimeLimitMinutes,
        disabledTimeSlots,
      ];
} 
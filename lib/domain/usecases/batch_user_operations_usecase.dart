import '../entities/user_profile_entity.dart';
import '../../core/utils/result.dart';
import '../../core/utils/validation.dart';
import 'base_usecase.dart';

/// 批量创建用户参数
// ignore: must_be_immutable
class BatchCreateUsersParams extends UseCaseParams {
  final List<({String nickname, String avatarId})> users;

  const BatchCreateUsersParams({
    required this.users,
  });

  @override
  List<Object> get props => [users];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<({String nickname, String avatarId})>('users').validate(users);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<({String nickname, String avatarId})>(
      parameterName: 'users',
      min: 1,
      max: 10,
    ).validate(users);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < users.length; i++) {
      final user = users[i];
      
      final nicknameResult = Validators.nickname().validate(user.nickname);
      if (nicknameResult.isFailure) return nicknameResult;
      
      final avatarIdResult = Validators.avatarId().validate(user.avatarId);
      if (avatarIdResult.isFailure) return avatarIdResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'users': users.map((user) => {
          'nickname': user.nickname,
          'avatarId': user.avatarId,
        }).toList(),
      };
}

/// 批量更新用户参数
// ignore: must_be_immutable
class BatchUpdateUsersParams extends UseCaseParams {
  final List<UserProfileEntity> users;

  const BatchUpdateUsersParams({
    required this.users,
  });

  @override
  List<Object> get props => [users];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<UserProfileEntity>('users').validate(users);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<UserProfileEntity>(
      parameterName: 'users',
      min: 1,
      max: 50,
    ).validate(users);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < users.length; i++) {
      final user = users[i];
      
      final userIdResult = Validators.userId().validate(user.id);
      if (userIdResult.isFailure) return userIdResult;
      
      final nicknameResult = Validators.nickname().validate(user.nickname);
      if (nicknameResult.isFailure) return nicknameResult;
      
      final avatarIdResult = Validators.avatarId().validate(user.avatarId);
      if (avatarIdResult.isFailure) return avatarIdResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'users': users.map((user) => user.toMap()).toList(),
      };
}

/// 批量删除用户参数
// ignore: must_be_immutable
class BatchDeleteUsersParams extends UseCaseParams {
  final List<String> userIds;

  const BatchDeleteUsersParams({
    required this.userIds,
  });

  @override
  List<Object> get props => [userIds];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<String>('userIds').validate(userIds);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<String>(
      parameterName: 'userIds',
      min: 1,
      max: 50,
    ).validate(userIds);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < userIds.length; i++) {
      final userId = userIds[i];
      
      final userIdResult = Validators.userId().validate(userId);
      if (userIdResult.isFailure) return userIdResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'userIds': userIds,
      };
}

/// 批量解锁成就参数
// ignore: must_be_immutable
class BatchUnlockAchievementsParams extends UseCaseParams {
  final List<({String userId, String achievementId})> achievements;

  const BatchUnlockAchievementsParams({
    required this.achievements,
  });

  @override
  List<Object> get props => [achievements];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<({String userId, String achievementId})>('achievements').validate(achievements);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<({String userId, String achievementId})>(
      parameterName: 'achievements',
      min: 1,
      max: 100,
    ).validate(achievements);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < achievements.length; i++) {
      final achievement = achievements[i];
      
      final userIdResult = Validators.userId().validate(achievement.userId);
      if (userIdResult.isFailure) return userIdResult;
      
      final achievementIdResult = Validators.stringNotEmpty('achievement[$i].achievementId').validate(achievement.achievementId);
      if (achievementIdResult.isFailure) return achievementIdResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'achievements': achievements.map((achievement) => {
          'userId': achievement.userId,
          'achievementId': achievement.achievementId,
        }).toList(),
      };
} 
import 'package:logger/logger.dart';

import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/result.dart';
import '../../core/utils/skill_type_mapper.dart';
import '../entities/user_profile_entity.dart';
import '../repositories/user_repository.dart';
import 'base_usecase.dart';

/// 保存进度参数
class SaveProgressParams extends UseCaseParams {
  final String userId;
  final String levelId;
  final bool completed;
  final int score;
  final int timeSeconds;
  final int hintsUsed;
  final int attempts;
  final dynamic userAnswer;

  const SaveProgressParams({
    required this.userId,
    required this.levelId,
    required this.completed,
    required this.score,
    required this.timeSeconds,
    required this.hintsUsed,
    required this.attempts,
    this.userAnswer,
  });

  @override
  List<Object?> get props => [
    userId,
    levelId,
    completed,
    score,
    timeSeconds,
    hintsUsed,
    attempts,
    userAnswer,
  ];

  @override
  Result<void> validate() {
    if (userId.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'INVALID_USER_ID', message: '用户ID不能为空'),
      );
    }
    if (levelId.isEmpty) {
      return Result.failure(
        PuzzleException(errorCode: 'INVALID_LEVEL_ID', message: '关卡ID不能为空'),
      );
    }
    if (score < 0) {
      return Result.failure(
        UserException(errorCode: 'INVALID_SCORE', message: '分数不能为负数'),
      );
    }
    if (timeSeconds <= 0) {
      return Result.failure(
        UserException(errorCode: 'INVALID_TIME', message: '游戏时间必须大于0'),
      );
    }
    return Result.success(null);
  }
}

/// 进度保存结果
class ProgressSaveResult {
  final UserProfileEntity updatedUser;
  final LevelProgressEntity levelProgress;
  final List<String> newAchievements;
  final int skillPointsEarned;
  final bool isNewRecord;

  const ProgressSaveResult({
    required this.updatedUser,
    required this.levelProgress,
    required this.newAchievements,
    required this.skillPointsEarned,
    required this.isNewRecord,
  });
}

class SaveProgressUseCase
    implements UseCase<Result<ProgressSaveResult>, SaveProgressParams> {
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  SaveProgressUseCase(this._userRepository);

  @override
  Future<Result<ProgressSaveResult>> call(SaveProgressParams params) async {
    try {
      _logger.i(
        'Saving progress for user: ${params.userId}, level: ${params.levelId}',
      );

      final validationError = _validateProgressData(params);
      if (validationError != null) {
        return Result.failure(
          UserException(
            errorCode: 'INVALID_PROGRESS_DATA',
            message: validationError,
          ),
        );
      }

      // 1. 验证用户存在
      final userResult = await _userRepository.getUserById(params.userId);
      final userNullable = userResult.getOrThrow();
      if (userNullable == null) {
        return Result.failure(
          UserException(errorCode: 'USER_NOT_FOUND', message: '用户不存在'),
        );
      }
      final user = userNullable;

      // 2. 获取或创建关卡进度
      final existingProgress = user.levelProgress[params.levelId];
      final isFirstCompletion =
          existingProgress == null || !existingProgress.completed;

      // 3. 计算星级评分
      final starRating = _calculateStarRating(params);

      // 4. 更新关卡进度
      final updatedProgress = _updateLevelProgress(
        existingProgress,
        params,
        starRating,
      );
      final isNewRecord = _isNewRecord(existingProgress, updatedProgress);

      // 5. 计算技能点数奖励
      final skillPointsEarned = _calculateSkillPoints(
        params,
        isFirstCompletion,
        isNewRecord,
      );

      // 6. 更新用户技能点数
      final updatedSkillPoints = Map<String, int>.from(user.skillPoints);
      final skillType = SkillTypeMapper.getSkillTypeFromLevelId(params.levelId);
      updatedSkillPoints[skillType] =
          (updatedSkillPoints[skillType] ?? 0) + skillPointsEarned;

      // 7. 更新用户进度
      final updatedLevelProgress = Map<String, LevelProgressEntity>.from(
        user.levelProgress,
      );
      updatedLevelProgress[params.levelId] = updatedProgress;

      // 8. 更新总游戏时间
      final totalPlayTime =
          user.totalPlayTimeMinutes + (params.timeSeconds / 60).ceil();

      // 9. 检查新解锁的成就
      final newAchievements = await _checkNewAchievements(
        user,
        updatedProgress,
        isFirstCompletion,
      );

      // 10. 更新解锁成就列表
      final updatedAchievements = List<String>.from(user.unlockedAchievements);
      updatedAchievements.addAll(newAchievements);

      // 11. 创建更新后的用户实体
      final updatedUser = user.copyWith(
        levelProgress: updatedLevelProgress,
        skillPoints: updatedSkillPoints,
        totalPlayTimeMinutes: totalPlayTime,
        unlockedAchievements: updatedAchievements,
        lastPlayedAt: DateTime.now(),
      );

      // 12. 保存到仓库
      await _userRepository.updateUser(updatedUser);

      // 13. 创建结果
      final result = ProgressSaveResult(
        updatedUser: updatedUser,
        levelProgress: updatedProgress,
        newAchievements: newAchievements,
        skillPointsEarned: skillPointsEarned,
        isNewRecord: isNewRecord,
      );

      _logger.i('Progress saved successfully for level: ${params.levelId}');
      return Result.success(result);
    } catch (e, stackTrace) {
      _logger.e('Failed to save progress', error: e, stackTrace: stackTrace);
      return Result.failure(
        UserException(
          errorCode: 'SAVE_PROGRESS_ERROR',
          message: '保存进度失败：${e.toString()}',
        ),
      );
    }
  }

  /// 验证进度数据
  String? _validateProgressData(SaveProgressParams params) {
    if (params.score < 0) {
      return '分数不能为负数';
    }

    if (params.timeSeconds <= 0) {
      return '游戏时间必须大于0';
    }

    if (params.hintsUsed < 0) {
      return '提示使用次数不能为负数';
    }

    if (params.attempts <= 0) {
      return '尝试次数必须大于0';
    }

    return null; // 验证通过
  }

  /// 计算星级评分
  int _calculateStarRating(SaveProgressParams params) {
    if (!params.completed) return 0;

    // 根据谜题类型和难度的估计时间计算星级
    // 这里使用简化的计算逻辑，实际应该根据具体谜题的难度
    final estimatedTime = _getEstimatedTimeForLevel(params.levelId);

    // 3星：快速完成且无提示
    if (params.timeSeconds <= estimatedTime * 0.8 && params.hintsUsed == 0) {
      return 3;
    }

    // 2星：正常完成或少量提示
    if (params.timeSeconds <= estimatedTime * 1.2 && params.hintsUsed <= 1) {
      return 2;
    }

    // 1星：完成即可
    return 1;
  }

  /// 更新关卡进度
  LevelProgressEntity _updateLevelProgress(
    LevelProgressEntity? existing,
    SaveProgressParams params,
    int starRating,
  ) {
    final now = DateTime.now();

    if (existing == null) {
      // 首次完成
      return LevelProgressEntity(
        levelId: params.levelId,
        bestScore: params.score,
        attempts: params.attempts,
        completed: params.completed,
        firstCompletedAt: params.completed ? now : null,
        lastAttemptAt: now,
        bestTimeSeconds: params.timeSeconds,
        hintsUsed: params.hintsUsed,
        starRating: starRating,
      );
    } else {
      // 更新现有进度
      return existing.copyWith(
        bestScore: params.score > existing.bestScore
            ? params.score
            : existing.bestScore,
        attempts: existing.attempts + params.attempts,
        completed: params.completed || existing.completed,
        firstCompletedAt:
            existing.firstCompletedAt ?? (params.completed ? now : null),
        lastAttemptAt: now,
        bestTimeSeconds:
            params.completed && params.timeSeconds < existing.bestTimeSeconds
            ? params.timeSeconds
            : existing.bestTimeSeconds,
        hintsUsed: params.completed && params.hintsUsed < existing.hintsUsed
            ? params.hintsUsed
            : existing.hintsUsed,
        starRating: starRating > existing.starRating
            ? starRating
            : existing.starRating,
      );
    }
  }

  /// 检查是否创造了新记录
  bool _isNewRecord(
    LevelProgressEntity? existing,
    LevelProgressEntity updated,
  ) {
    if (existing == null) return true; // 首次完成就是新记录

    return updated.bestScore > existing.bestScore ||
        updated.bestTimeSeconds < existing.bestTimeSeconds ||
        updated.starRating > existing.starRating;
  }

  /// 计算技能点数奖励
  int _calculateSkillPoints(
    SaveProgressParams params,
    bool isFirstCompletion,
    bool isNewRecord,
  ) {
    if (!params.completed) return 0;

    int points = 0;

    // 完成奖励
    if (isFirstCompletion) {
      points += 50; // 首次完成奖励
    } else if (isNewRecord) {
      points += 20; // 破纪录奖励
    } else {
      points += 10; // 重复完成奖励
    }

    // 时间奖励
    final estimatedTime = _getEstimatedTimeForLevel(params.levelId);
    if (params.timeSeconds <= estimatedTime * 0.5) {
      points += 30; // 超快完成
    } else if (params.timeSeconds <= estimatedTime * 0.8) {
      points += 15; // 快速完成
    }

    // 无提示奖励
    if (params.hintsUsed == 0) {
      points += 25;
    }

    return points;
  }

  /// 检查新解锁的成就
  Future<List<String>> _checkNewAchievements(
    UserProfileEntity user,
    LevelProgressEntity progress,
    bool isFirstCompletion,
  ) async {
    final newAchievements = <String>[];

    // 首次完成成就
    if (isFirstCompletion && progress.completed) {
      newAchievements.add('first_puzzle_completed');
    }

    // 完美完成成就（3星且无提示）
    if (progress.starRating == 3 && progress.hintsUsed == 0) {
      newAchievements.add('perfect_completion');
    }

    // 连续完成成就
    final completedCount =
        user.levelProgress.values.where((p) => p.completed).length + 1;
    if (completedCount == 5) {
      newAchievements.add('five_puzzles_completed');
    } else if (completedCount == 10) {
      newAchievements.add('ten_puzzles_completed');
    }

    // 移除已有的成就
    newAchievements.removeWhere(
      (achievement) => user.unlockedAchievements.contains(achievement),
    );

    return newAchievements;
  }



  /// 获取关卡的估计完成时间（秒）
  int _getEstimatedTimeForLevel(String levelId) {
    // 根据关卡类型和难度返回估计时间
    // 这里使用简化的逻辑，实际应该从配置文件获取

    if (levelId.contains('easy')) {
      return 60; // 1分钟
    } else if (levelId.contains('medium')) {
      return 120; // 2分钟
    } else if (levelId.contains('hard')) {
      return 300; // 5分钟
    }

    return 120; // 默认2分钟
  }
}

import 'package:logger/logger.dart';

import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/result.dart';
import '../entities/user_profile_entity.dart';
import '../repositories/user_repository.dart';
import 'base_usecase.dart';

/// 解锁成就参数
class UnlockAchievementParams {
  final String userId;
  final String achievementId;
  final Map<String, dynamic>? context;

  const UnlockAchievementParams({
    required this.userId,
    required this.achievementId,
    this.context,
  });

  /// 验证参数
  Result<void> validate() {
    if (userId.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'INVALID_USER_ID', message: '用户ID不能为空'),
      );
    }
    if (achievementId.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'INVALID_ACHIEVEMENT_ID', message: '成就ID不能为空'),
      );
    }
    return Result.success(null);
  }
}

/// 解锁成就结果
class UnlockAchievementResult {
  final UserProfileEntity updatedUser;
  final String achievementId;
  final String achievementName;
  final String achievementDescription;
  final int rewardPoints;
  final bool isNewUnlock;
  final String achievement;
  final int totalPointsEarned;
  final List<String> triggeredChainAchievements;

  const UnlockAchievementResult({
    required this.updatedUser,
    required this.achievementId,
    required this.achievementName,
    required this.achievementDescription,
    required this.rewardPoints,
    required this.isNewUnlock,
    required this.achievement,
    required this.totalPointsEarned,
    required this.triggeredChainAchievements,
  });
}

/// 解锁成就用例
class UnlockAchievementUseCase
    implements
        UseCase<Result<UnlockAchievementResult>, UnlockAchievementParams> {
  final UserRepository _userRepository;
  
  final Logger _logger = Logger();

  UnlockAchievementUseCase({required UserRepository userRepository}) : _userRepository = userRepository;

  @override
  Future<Result<UnlockAchievementResult>> call(
    UnlockAchievementParams params,
  ) async {
    try {
      _logger.i(
        'Unlocking achievement: \${params.achievementId} for user: \${params.userId}',
      );

      // 验证参数
      final validation = params.validate();
      if (validation.isFailure) {
        return Result.failure(validation.exception!);
      }

      // 获取用户信息
      final userResult = await _userRepository.getUserById(params.userId);
      final user = userResult.getOrThrow();
      if (user == null) {
        return Result.failure(
          UserException(errorCode: 'USER_NOT_FOUND', message: '用户不存在'),
        );
      }

      // 检查成就是否已解锁
      final isAlreadyUnlocked = user.unlockedAchievements.contains(
        params.achievementId,
      );
      if (isAlreadyUnlocked) {
        // 返回已解锁的成就信息
        final achievementInfo = _getAchievementInfo(params.achievementId);
        return Result.success(
          UnlockAchievementResult(
            updatedUser: user,
            achievementId: params.achievementId,
            achievementName: achievementInfo['name'],
            achievementDescription: achievementInfo['description'],
            rewardPoints: achievementInfo['points'],
            isNewUnlock: false,
            achievement: achievementInfo['name'],
            totalPointsEarned: user.totalScore,
            triggeredChainAchievements: [],
          ),
        );
      }

      // 验证成就解锁条件
      final conditionCheck = await _checkAchievementConditions(
        user,
        params.achievementId,
        params.context,
      );
      if (conditionCheck.isFailure) {
        return Result.failure(conditionCheck.exception!);
      }

      // 解锁成就
      final achievementInfo = _getAchievementInfo(params.achievementId);
      final updatedAchievements = List<String>.from(user.unlockedAchievements)
        ..add(params.achievementId);

      // 检查是否触发连锁成就
      final chainAchievements = _checkChainAchievements(
        updatedAchievements,
        params.achievementId,
      );

      // 更新用户信息
      final updatedUser = user.copyWith(
        unlockedAchievements: updatedAchievements,
        totalScore: user.totalScore + (achievementInfo['points'] as int? ?? 0),
      );

      await _userRepository.updateUser(updatedUser);

      final result = UnlockAchievementResult(
        updatedUser: updatedUser,
        achievementId: params.achievementId,
        achievementName: achievementInfo['name'],
        achievementDescription: achievementInfo['description'],
        rewardPoints: achievementInfo['points'],
        isNewUnlock: true,
        achievement: achievementInfo['name'],
        totalPointsEarned: updatedUser.totalScore,
        triggeredChainAchievements: chainAchievements,
      );

      _logger.i('Achievement unlocked successfully: \${params.achievementId}');
      return Result.success(result);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to unlock achievement',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        UserException(
          errorCode: 'UNLOCK_ACHIEVEMENT_ERROR',
          message: '解锁成就失败：\${e.toString()}',
        ),
      );
    }
  }

  /// 检查成就解锁条件
  Future<Result<void>> _checkAchievementConditions(
    UserProfileEntity user,
    String achievementId,
    Map<String, dynamic>? context,
  ) async {
    // 这里应该检查具体的成就解锁条件
    // 为了简化，我们假设所有成就都可以解锁

    final achievementInfo = _getAchievementInfo(achievementId);
    if (achievementInfo.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'ACHIEVEMENT_NOT_FOUND', message: '成就不存在'),
      );
    }

    // 可以在这里添加具体的条件检查逻辑
    // 例如：检查用户是否完成了足够的关卡、是否达到了特定的分数等

    return Result.success(null);
  }

  /// 检查连锁成就
  List<String> _checkChainAchievements(
    List<String> unlockedAchievements,
    String newAchievementId,
  ) {
    final chainAchievements = <String>[];

    // 检查是否达到特定数量的成就解锁
    if (unlockedAchievements.length == 5 &&
        !unlockedAchievements.contains('five_achievements_unlocked')) {
      chainAchievements.add('five_achievements_unlocked');
    }

    if (unlockedAchievements.length == 10 &&
        !unlockedAchievements.contains('ten_achievements_unlocked')) {
      chainAchievements.add('ten_achievements_unlocked');
    }

    return chainAchievements;
  }

  /// 获取成就信息
  Map<String, dynamic> _getAchievementInfo(String achievementId) {
    // 这里应该从配置文件或数据库中获取成就信息
    // 为了简化，我们使用硬编码的成就信息

    final achievements = {
      'first_puzzle_completed': {
        'name': '初次尝试',
        'description': '完成第一个谜题',
        'points': 100,
      },
      'perfect_completion': {
        'name': '完美完成',
        'description': '在不使用提示的情况下获得3星评分',
        'points': 200,
      },
      'five_puzzles_completed': {
        'name': '小试牛刀',
        'description': '完成5个谜题',
        'points': 300,
      },
      'ten_puzzles_completed': {
        'name': '渐入佳境',
        'description': '完成10个谜题',
        'points': 500,
      },
      'speed_master': {
        'name': '速度大师',
        'description': '在60秒内完成一个困难谜题',
        'points': 400,
      },
      'pattern_expert': {
        'name': '图形专家',
        'description': '完成10个图形识别谜题',
        'points': 350,
      },
      'spatial_genius': {
        'name': '空间天才',
        'description': '完成10个空间可视化谜题',
        'points': 350,
      },
      'logic_master': {
        'name': '逻辑大师',
        'description': '完成10个数值逻辑谜题',
        'points': 350,
      },
      'coding_wizard': {
        'name': '编程向导',
        'description': '完成10个编程逻辑谜题',
        'points': 350,
      },
      'five_achievements_unlocked': {
        'name': '成就收集者',
        'description': '解锁5个成就',
        'points': 250,
      },
      'ten_achievements_unlocked': {
        'name': '成就大师',
        'description': '解锁10个成就',
        'points': 500,
      },
    };

    return achievements[achievementId] ?? {};
  }
}

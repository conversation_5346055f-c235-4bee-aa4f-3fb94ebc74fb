import 'package:equatable/equatable.dart';

import '../../core/utils/result.dart';
import '../../core/utils/validation.dart';
import '../../core/exceptions/app_exceptions.dart';

/// 基础Use Case抽象类
/// 
/// 所有Use Case都应该继承此类，提供统一的调用接口
/// Type: 返回类型
/// Params: 参数类型
abstract class UseCase<Type, Params> {
  /// 执行用例
  /// 
  /// [params] 用例参数
  /// 返回 Future`&lt;Type&gt;` 执行结果
  Future<Type> call(Params params);
}

/// 无参数Use Case抽象类
abstract class NoParamsUseCase<Type> {
  /// 执行无参数用例
  Future<Type> call();
}

/// Use Case参数基类
/// 
/// 所有Use Case参数都应该继承此类，确保参数的不可变性和可比较性
/// 并提供统一的验证接口
abstract class UseCaseParams extends Equatable {
  const UseCaseParams();

  /// 验证参数
  /// 
  /// 子类应重写此方法提供具体的验证逻辑
  /// 返回 ValidationResult 验证结果
  ValidationResult validate() {
    // 默认实现：无验证，直接通过
    return const Result.success(null);
  }

  /// 验证参数并返回Result
  /// 
  /// 便捷方法，验证参数并将结果转换为Result`&lt;T&gt;`
  Result<T> validateAndReturn<T>(T value) {
    final validationResult = validate();
    return validationResult.fold(
      (_) => Result.success(value),
      (exception) => Result.failure(exception),
    );
  }
}

/// 无参数标识类
class NoParams extends UseCaseParams {
  const NoParams();
  
  @override
  List<Object> get props => [];

  @override
  ValidationResult validate() {
    // 无参数，验证总是通过
    return const Result.success(null);
  }
}

/// 验证用例抽象类
/// 
/// 为需要复杂验证的Use Case提供基础实现
abstract class ValidatedUseCase<Type, Params extends UseCaseParams> 
    implements UseCase<Result<Type>, Params> {
  
  @override
  Future<Result<Type>> call(Params params) async {
    // 1. 参数验证
    final validationResult = params.validate();
    if (validationResult.isFailure) {
      return Result.failure(validationResult.exception!);
    }

    // 2. 执行具体的业务逻辑
    return execute(params);
  }

  /// 执行具体的业务逻辑
  /// 
  /// 子类实现具体的业务逻辑，此时参数已经通过验证
  Future<Result<Type>> execute(Params params);
}

/// 批量验证用例抽象类
/// 
/// 为需要批量验证的Use Case提供基础实现
abstract class BatchValidatedUseCase<Type, Params extends UseCaseParams> 
    extends ValidatedUseCase<Type, Params> {
  
  @override
  Future<Result<Type>> call(Params params) async {
    // 1. 基础参数验证
    final basicValidation = params.validate();
    if (basicValidation.isFailure) {
      return Result.failure(basicValidation.exception!);
    }

    // 2. 批量验证
    final batchValidation = performBatchValidation(params);
    if (batchValidation.isFailure) {
      return Result.failure(batchValidation.exception!);
    }

    // 3. 执行业务逻辑
    return execute(params);
  }

  /// 执行批量验证
  /// 
  /// 子类可以重写此方法提供额外的验证逻辑
  ValidationResult performBatchValidation(Params params) {
    return const Result.success(null);
  }
}

/// 异步验证用例抽象类
/// 
/// 为需要异步验证的Use Case提供基础实现
abstract class AsyncValidatedUseCase<Type, Params extends UseCaseParams> 
    implements UseCase<Result<Type>, Params> {
  
  @override
  Future<Result<Type>> call(Params params) async {
    // 1. 同步参数验证
    final syncValidation = params.validate();
    if (syncValidation.isFailure) {
      return Result.failure(syncValidation.exception!);
    }

    // 2. 异步验证
    final asyncValidation = await performAsyncValidation(params);
    if (asyncValidation.isFailure) {
      return Result.failure(asyncValidation.exception!);
    }

    // 3. 执行业务逻辑
    return execute(params);
  }

  /// 执行异步验证
  /// 
  /// 子类可以重写此方法提供异步验证逻辑（如数据库查询验证）
  Future<ValidationResult> performAsyncValidation(Params params) async {
    return const Result.success(null);
  }

  /// 执行具体的业务逻辑
  Future<Result<Type>> execute(Params params);
}

/// Use Case执行结果包装类（已弃用，建议使用Result`&lt;T&gt;`）
/// 
/// 用于包装Use Case的执行结果，提供成功/失败状态
@Deprecated('Use Result<T> instead')
class UseCaseResult<T> extends Equatable {
  final bool isSuccess;
  final T? data;
  final String? errorMessage;
  final Exception? exception;

  const UseCaseResult._({
    required this.isSuccess,
    this.data,
    this.errorMessage,
    this.exception,
  });

  /// 创建成功结果
  factory UseCaseResult.success(T data) {
    return UseCaseResult._(
      isSuccess: true,
      data: data,
    );
  }

  /// 创建失败结果
  factory UseCaseResult.failure(String errorMessage, [Exception? exception]) {
    return UseCaseResult._(
      isSuccess: false,
      errorMessage: errorMessage,
      exception: exception,
    );
  }

  /// 是否失败
  bool get isFailure => !isSuccess;

  /// 转换为Result`&lt;T&gt;`
  Result<T> toResult() {
    if (isSuccess && data != null) {
      return Result.success(data as T);
    } else {
      // 创建一个通用异常
      final appException = UnknownException(
        details: errorMessage ?? 'Unknown error',
        originalError: exception,
      );
      return Result.failure(appException);
    }
  }

  @override
  List<Object?> get props => [isSuccess, data, errorMessage, exception];
}

/// Use Case工厂类
/// 
/// 提供便捷的Use Case创建方法
class UseCaseFactory {
  UseCaseFactory._();

  /// 创建简单的验证用例
  static ValidatedUseCase<T, P> createValidated<T, P extends UseCaseParams>(
    Future<Result<T>> Function(P params) executor,
  ) {
    return _SimpleValidatedUseCase<T, P>(executor);
  }

  /// 创建批量验证用例
  static BatchValidatedUseCase<T, P> createBatchValidated<T, P extends UseCaseParams>(
    Future<Result<T>> Function(P params) executor,
    ValidationResult Function(P params)? batchValidator,
  ) {
    return _SimpleBatchValidatedUseCase<T, P>(executor, batchValidator);
  }

  /// 创建异步验证用例
  static AsyncValidatedUseCase<T, P> createAsyncValidated<T, P extends UseCaseParams>(
    Future<Result<T>> Function(P params) executor,
    Future<ValidationResult> Function(P params)? asyncValidator,
  ) {
    return _SimpleAsyncValidatedUseCase<T, P>(executor, asyncValidator);
  }
}

/// 简单验证用例实现
class _SimpleValidatedUseCase<T, P extends UseCaseParams> 
    extends ValidatedUseCase<T, P> {
  final Future<Result<T>> Function(P params) _executor;

  _SimpleValidatedUseCase(this._executor);

  @override
  Future<Result<T>> execute(P params) => _executor(params);
}

/// 简单批量验证用例实现
class _SimpleBatchValidatedUseCase<T, P extends UseCaseParams> 
    extends BatchValidatedUseCase<T, P> {
  final Future<Result<T>> Function(P params) _executor;
  final ValidationResult Function(P params)? _batchValidator;

  _SimpleBatchValidatedUseCase(this._executor, this._batchValidator);

  @override
  Future<Result<T>> execute(P params) => _executor(params);

  @override
  ValidationResult performBatchValidation(P params) {
    return _batchValidator?.call(params) ?? super.performBatchValidation(params);
  }
}

/// 简单异步验证用例实现
class _SimpleAsyncValidatedUseCase<T, P extends UseCaseParams> 
    extends AsyncValidatedUseCase<T, P> {
  final Future<Result<T>> Function(P params) _executor;
  final Future<ValidationResult> Function(P params)? _asyncValidator;

  _SimpleAsyncValidatedUseCase(this._executor, this._asyncValidator);

  @override
  Future<Result<T>> execute(P params) => _executor(params);

  @override
  Future<ValidationResult> performAsyncValidation(P params) {
    return _asyncValidator?.call(params) ?? super.performAsyncValidation(params);
  }
}

/// Use Case执行器
/// 
/// 提供Use Case的统一执行接口和通用功能
class UseCaseExecutor {
  UseCaseExecutor._();

  /// 执行Use Case并处理通用逻辑
  static Future<Result<T>> execute<T, P extends UseCaseParams>(
    UseCase<Result<T>, P> useCase,
    P params, {
    void Function(T data)? onSuccess,
    void Function(AppException exception)? onFailure,
    void Function()? onComplete,
  }) async {
    try {
      final result = await useCase.call(params);
      
      result
          .onSuccess((data) => onSuccess?.call(data))
          .onFailure((exception) => onFailure?.call(exception))
          .onComplete((_) => onComplete?.call());
      
      return result;
    } catch (e, stackTrace) {
      final exception = e is AppException 
          ? e 
          : UnknownException(
              details: e.toString(),
              originalError: e,
              stackTrace: stackTrace,
            );
      
      final result = Result<T>.failure(exception);
      onFailure?.call(exception);
      onComplete?.call();
      
      return result;
    }
  }

  /// 批量执行Use Case
  static Future<Result<List<T>>> executeBatch<T, P extends UseCaseParams>(
    List<(UseCase<Result<T>, P>, P)> useCases,
  ) async {
    final results = <Result<T>>[];
    
    for (final (useCase, params) in useCases) {
      final result = await useCase.call(params);
      results.add(result);
      
      // 如果有任何一个失败，立即返回失败结果
      if (result.isFailure) {
        return Result.failure(result.exception!);
      }
    }
    
    // 提取所有成功的数据
    final data = results.map((r) => r.data!).toList();
    return Result.success(data);
  }

  /// 并行执行Use Case
  static Future<Result<List<T>>> executeParallel<T, P extends UseCaseParams>(
    List<(UseCase<Result<T>, P>, P)> useCases,
  ) async {
    final futures = useCases.map((pair) => pair.$1.call(pair.$2)).toList();
    final results = await Future.wait(futures);
    
    // 检查是否有失败的结果
    for (final result in results) {
      if (result.isFailure) {
        return Result.failure(result.exception!);
      }
    }
    
    // 提取所有成功的数据
    final data = results.map((r) => r.data!).toList();
    return Result.success(data);
  }
} 
import 'package:logger/logger.dart';

import '../entities/user_profile_entity.dart';
import '../repositories/user_repository.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/result.dart';
import '../../core/utils/validation.dart';
import 'base_usecase.dart';

/// 创建用户用例参数
class CreateUserParams extends UseCaseParams {
  final String nickname;
  final String avatarId;

  const CreateUserParams({
    required this.nickname,
    required this.avatarId,
  });

  @override
  List<Object> get props => [nickname, avatarId];

  /// 转换为Map用于验证
  Map<String, dynamic> toMap() {
    return {
      'nickname': nickname,
      'avatarId': avatarId,
    };
  }

  @override
  ValidationResult validate() {
    // 使用批量验证器进行完整的参数验证
    final validator = BatchValidator()
        .validate('nickname', nickname, Validators.nickname())
        .validate('avatarId', avatarId, Validators.avatarId());

    return validator.toResult();
  }
}

/// 创建用户用例
/// 
/// 负责处理用户创建的业务逻辑，包括：
/// - 完整的输入参数验证
/// - 昵称唯一性检查
/// - 用户数量限制检查
/// - 用户创建和保存
/// - 统一的错误处理和结果返回
class CreateUserUseCase
    implements UseCase<Result<UserProfileEntity>, CreateUserParams> {
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  CreateUserUseCase(this._userRepository);

  @override
  Future<Result<UserProfileEntity>> call(CreateUserParams params) async {
    _logger.i('Creating user: ${params.nickname}');

    return Result.fromAsync(() async {
      // 1. 基础参数验证
      final validationResult = params.validate();
      if (validationResult.isFailure) {
        _logger.w('User creation failed: parameter validation error');
        throw validationResult.exception ?? UserException(
          errorCode: 'VALIDATION_ERROR',
          message: 'Parameter validation failed',
        );
      }

      // 2. 检查昵称唯一性
      final nicknameExistsResult = await _userRepository.isNicknameExists(params.nickname.trim());
      final nicknameExists = nicknameExistsResult.getOrThrow();
      if (nicknameExists) {
        throw NicknameAlreadyTakenException(nickname: params.nickname);
      }

      // 3. 检查用户数量限制
      final allUsersResult = await _userRepository.getAllUsers();
      final allUsers = allUsersResult.getOrThrow();
      if (allUsers.length >= 4) {
        throw UserLimitExceededException(
          currentCount: allUsers.length,
          maxCount: 4,
        );
      }

      // 4. 创建用户
      final savedUserResult = await _userRepository.createUser(
        nickname: params.nickname.trim(),
        avatarId: params.avatarId,
      );

      final savedUser = savedUserResult.getOrThrow();
      _logger.i('User created successfully: ${savedUser.id}');
      return savedUser;
    }).onError((exception, stackTrace) {
      _logger.e('Failed to create user: ${exception.toString()}', 
                error: exception, stackTrace: stackTrace);
      throw (exception ?? Exception("Unknown error"));
    });
  }
} 
import '../entities/puzzle_entity.dart';
import '../../core/utils/result.dart';
import '../../core/utils/validation.dart';
import 'base_usecase.dart';

/// 批量验证答案参数
class BatchValidateAnswersParams extends UseCaseParams {
  final List<({PuzzleEntity puzzle, dynamic userAnswer})> validations;

  const BatchValidateAnswersParams({
    required this.validations,
  });

  @override
  List<Object> get props => [validations];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<({PuzzleEntity puzzle, dynamic userAnswer})>('validations').validate(validations);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<({PuzzleEntity puzzle, dynamic userAnswer})>(
      parameterName: 'validations',
      min: 1,
      max: 50,
    ).validate(validations);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < validations.length; i++) {
      final validation = validations[i];
      
      final puzzleResult = Validators.notNull<PuzzleEntity>('validation[$i].puzzle').validate(validation.puzzle);
      if (puzzleResult.isFailure) return puzzleResult;
      
      final answerResult = Validators.notNull<dynamic>('validation[$i].userAnswer').validate(validation.userAnswer);
      if (answerResult.isFailure) return answerResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'validations': validations.map((validation) => {
          'puzzleId': validation.puzzle.levelId,
          'userAnswer': validation.userAnswer,
        }).toList(),
      };
}

/// 批量生成提示参数
// ignore: must_be_immutable
class BatchGenerateHintsParams extends UseCaseParams {
  final List<({PuzzleEntity puzzle, int hintLevel})> requests;

  const BatchGenerateHintsParams({
    required this.requests,
  });

  @override
  List<Object> get props => [requests];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<({PuzzleEntity puzzle, int hintLevel})>('requests').validate(requests);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<({PuzzleEntity puzzle, int hintLevel})>(
      parameterName: 'requests',
      min: 1,
      max: 30,
    ).validate(requests);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < requests.length; i++) {
      final request = requests[i];
      
      final puzzleResult = Validators.notNull<PuzzleEntity>('request[$i].puzzle').validate(request.puzzle);
      if (puzzleResult.isFailure) return puzzleResult;
      
      final hintLevelResult = Validators.intRange(parameterName: 'request[$i].hintLevel', min: 1, max: 3).validate(request.hintLevel);
      if (hintLevelResult.isFailure) return hintLevelResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'requests': requests.map((request) => {
          'puzzleId': request.puzzle.levelId,
          'hintLevel': request.hintLevel,
        }).toList(),
      };
}

/// 批量创建谜题参数
// ignore: must_be_immutable
class BatchCreatePuzzlesParams extends UseCaseParams {
  final List<PuzzleEntity> puzzles;

  const BatchCreatePuzzlesParams({
    required this.puzzles,
  });

  @override
  List<Object> get props => [puzzles];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<PuzzleEntity>('puzzles').validate(puzzles);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<PuzzleEntity>(
      parameterName: 'puzzles',
      min: 1,
      max: 100,
    ).validate(puzzles);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < puzzles.length; i++) {
      final puzzle = puzzles[i];
      
      final levelIdResult = Validators.stringNotEmpty('puzzle[$i].levelId').validate(puzzle.levelId);
      if (levelIdResult.isFailure) return levelIdResult;
      
      final promptResult = Validators.stringNotEmpty('puzzle[$i].prompt').validate(puzzle.prompt);
      if (promptResult.isFailure) return promptResult;
      
      final typeResult = Validators.puzzleType().validate(puzzle.puzzleType.id);
      if (typeResult.isFailure) return typeResult;
      
      final dataResult = Validators.notNull<Map<String, dynamic>>('puzzle[$i].data').validate(puzzle.data);
      if (dataResult.isFailure) return dataResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'puzzles': puzzles.map((puzzle) => puzzle.toMap()).toList(),
      };
}

/// 批量更新谜题参数
// ignore: must_be_immutable
class BatchUpdatePuzzlesParams extends UseCaseParams {
  final List<PuzzleEntity> puzzles;

  const BatchUpdatePuzzlesParams({
    required this.puzzles,
  });

  @override
  List<Object> get props => [puzzles];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<PuzzleEntity>('puzzles').validate(puzzles);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<PuzzleEntity>(
      parameterName: 'puzzles',
      min: 1,
      max: 100,
    ).validate(puzzles);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < puzzles.length; i++) {
      final puzzle = puzzles[i];
      
      final levelIdResult = Validators.stringNotEmpty('puzzle[$i].levelId').validate(puzzle.levelId);
      if (levelIdResult.isFailure) return levelIdResult;
      
      final promptResult = Validators.stringNotEmpty('puzzle[$i].prompt').validate(puzzle.prompt);
      if (promptResult.isFailure) return promptResult;
      
      final typeResult = Validators.puzzleType().validate(puzzle.puzzleType.id);
      if (typeResult.isFailure) return typeResult;
      
      final dataResult = Validators.notNull<Map<String, dynamic>>('puzzle[$i].data').validate(puzzle.data);
      if (dataResult.isFailure) return dataResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'puzzles': puzzles.map((puzzle) => puzzle.toMap()).toList(),
      };
}

/// 批量删除谜题参数
// ignore: must_be_immutable
class BatchDeletePuzzlesParams extends UseCaseParams {
  final List<String> levelIds;

  const BatchDeletePuzzlesParams({
    required this.levelIds,
  });

  @override
  List<Object> get props => [levelIds];

  @override
  Result<void> validate() {
    // 验证列表不为空
    final listEmptyResult = Validators.listNotEmpty<String>('levelIds').validate(levelIds);
    if (listEmptyResult.isFailure) return listEmptyResult;
    
    // 验证列表长度
    final listLengthResult = Validators.listLength<String>(
      parameterName: 'levelIds',
      min: 1,
      max: 100,
    ).validate(levelIds);
    if (listLengthResult.isFailure) return listLengthResult;
    
    // 验证每个元素
    for (int i = 0; i < levelIds.length; i++) {
      final levelId = levelIds[i];
      
      final levelIdResult = Validators.stringNotEmpty('levelIds[$i]').validate(levelId);
      if (levelIdResult.isFailure) return levelIdResult;
    }
    
    return const Result.success(null);
  }

  Map<String, dynamic> toMap() => {
        'levelIds': levelIds,
      };
} 
import 'package:logger/logger.dart';

import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/result.dart';
import '../entities/puzzle_entity.dart';
import '../entities/user_profile_entity.dart';
import '../repositories/puzzle_repository.dart';
import '../repositories/user_repository.dart';
import '../value_objects/puzzle_types.dart';
import 'base_usecase.dart';

/// 生成提示参数
class GenerateHintParams extends UseCaseParams {
  final String userId;
  final String levelId;
  final int hintLevel;
  final Map<String, dynamic> currentGameState;

  const GenerateHintParams({
    required this.userId,
    required this.levelId,
    required this.hintLevel,
    required this.currentGameState,
  });

  @override
  List<Object?> get props => [userId, levelId, hintLevel, currentGameState];

  @override
  Result<void> validate() {
    if (userId.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'INVALID_USER_ID', message: '用户ID不能为空'),
      );
    }
    if (levelId.isEmpty) {
      return Result.failure(
        PuzzleException(errorCode: 'INVALID_LEVEL_ID', message: '关卡ID不能为空'),
      );
    }
    if (hintLevel < 1 || hintLevel > 3) {
      return Result.failure(
        HintException(errorCode: 'INVALID_HINT_LEVEL', message: '提示级别必须在1-3之间'),
      );
    }
    return Result.success(null);
  }
}

/// 提示结果
class HintResult {
  final String hintText;
  final String hintType;
  final Map<String, dynamic> hintData;
  final int hintLevel;
  final bool isLastHint;

  const HintResult({
    required this.hintText,
    required this.hintType,
    required this.hintData,
    required this.hintLevel,
    required this.isLastHint,
  });

  /// 为了兼容性，提供content和type的getter
  String get content => hintText;
  String get type => hintType;
}

/// 生成提示用例
class GenerateHintUseCase
    implements UseCase<Result<HintResult>, GenerateHintParams> {
  final PuzzleRepository _puzzleRepository;
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  GenerateHintUseCase(this._puzzleRepository, this._userRepository);

  @override
  Future<Result<HintResult>> call(GenerateHintParams params) async {
    try {
      _logger.i(
        'Generating hint for user: ${params.userId}, level: ${params.levelId}',
      );

      // 验证参数
      final validation = params.validate();
      if (validation.isFailure) {
        return Result.failure(validation.exception!);
      }

      // 获取用户信息
      final userResult = await _userRepository.getUserById(params.userId);
      final user = userResult.getOrThrow();
      if (user == null) {
        return Result.failure(
          UserException(errorCode: 'USER_NOT_FOUND', message: '用户不存在'),
        );
      }

      // 获取谜题信息
      final puzzleResult = await _puzzleRepository.getPuzzleById(
        params.levelId,
      );
      final puzzle = puzzleResult.getOrThrow();
      if (puzzle == null) {
        return Result.failure(
          PuzzleException(errorCode: 'PUZZLE_NOT_FOUND', message: '谜题不存在'),
        );
      }

      // 检查提示权限
      final permissionCheck = await _checkHintPermission(
        user,
        puzzle,
        params.hintLevel,
      );
      if (permissionCheck.isFailure) {
        return Result.failure(permissionCheck.exception!);
      }

      // 生成智能提示
      final hint = await _generateIntelligentHint(
        puzzle,
        params.hintLevel,
        params.currentGameState,
      );

      // 更新提示使用记录
      await _updateHintUsage(user, params.levelId, params.hintLevel);

      _logger.i('Hint generated successfully for level: ${params.levelId}');
      return Result.success(hint);
    } catch (e, stackTrace) {
      _logger.e('Failed to generate hint', error: e, stackTrace: stackTrace);
      return Result.failure(
        HintException(
          errorCode: 'GENERATE_HINT_ERROR',
          message: '生成提示失败：${e.toString()}',
        ),
      );
    }
  }

  /// 检查提示权限
  Future<Result<void>> _checkHintPermission(
    UserProfileEntity user,
    PuzzleEntity puzzle,
    int hintLevel,
  ) async {
    // 检查用户是否有足够的提示次数
    final maxHints = puzzle.maxHints ?? 3;
    final usedHints = user.levelProgress[puzzle.levelId]?.hintsUsed ?? 0;

    if (usedHints >= maxHints) {
      return Result.failure(
        HintException(errorCode: 'MAX_HINTS_REACHED', message: '已达到最大提示次数'),
      );
    }

    if (hintLevel > maxHints) {
      return Result.failure(
        HintException(errorCode: 'INVALID_HINT_LEVEL', message: '提示级别超出范围'),
      );
    }

    return Result.success(null);
  }

  /// 生成智能提示
  Future<HintResult> _generateIntelligentHint(
    PuzzleEntity puzzle,
    int hintLevel,
    Map<String, dynamic> currentGameState,
  ) async {
    switch (puzzle.puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return _generatePatternHint(puzzle, hintLevel, currentGameState);
      case PuzzleType.spatialVisualization:
        return _generateSpatialHint(puzzle, hintLevel, currentGameState);
      case PuzzleType.numericLogic:
        return _generateNumericHint(puzzle, hintLevel, currentGameState);
      case PuzzleType.introToCoding:
        return _generateCodingHint(puzzle, hintLevel, currentGameState);
      case PuzzleType.mirrorSymmetry:
        return _generateMirrorSymmetryHint(puzzle, hintLevel, currentGameState);
    }
  }

  /// 生成图形模式提示
  Future<HintResult> _generatePatternHint(
    PuzzleEntity puzzle,
    int hintLevel,
    Map<String, dynamic> currentGameState,
  ) async {
    final hints = [
      '仔细观察图形的颜色和形状规律',
      '注意图形的位置变化模式',
      '答案是：${puzzle.correctAnswer}',
    ];

    return HintResult(
      hintText: hints[hintLevel - 1],
      hintType: 'pattern',
      hintData: {'pattern_type': 'visual'},
      hintLevel: hintLevel,
      isLastHint: hintLevel >= hints.length,
    );
  }

  /// 生成空间可视化提示
  Future<HintResult> _generateSpatialHint(
    PuzzleEntity puzzle,
    int hintLevel,
    Map<String, dynamic> currentGameState,
  ) async {
    final hints = [
      '想象物体在三维空间中的旋转',
      '注意物体的隐藏面和可见面',
      '答案是：${puzzle.correctAnswer}',
    ];

    return HintResult(
      hintText: hints[hintLevel - 1],
      hintType: 'spatial',
      hintData: {'spatial_type': '3d_rotation'},
      hintLevel: hintLevel,
      isLastHint: hintLevel >= hints.length,
    );
  }

  /// 生成数值逻辑提示
  Future<HintResult> _generateNumericHint(
    PuzzleEntity puzzle,
    int hintLevel,
    Map<String, dynamic> currentGameState,
  ) async {
    final hints = ['寻找数字之间的数学关系', '尝试加减乘除运算规律', '答案是：${puzzle.correctAnswer}'];

    return HintResult(
      hintText: hints[hintLevel - 1],
      hintType: 'numeric',
      hintData: {'numeric_type': 'sequence'},
      hintLevel: hintLevel,
      isLastHint: hintLevel >= hints.length,
    );
  }

  /// 生成编程逻辑提示
  Future<HintResult> _generateCodingHint(
    PuzzleEntity puzzle,
    int hintLevel,
    Map<String, dynamic> currentGameState,
  ) async {
    final hints = ['思考算法的基本步骤', '注意循环和条件判断的逻辑', '答案是：${puzzle.correctAnswer}'];

    return HintResult(
      hintText: hints[hintLevel - 1],
      hintType: 'coding',
      hintData: {'coding_type': 'algorithm'},
      hintLevel: hintLevel,
      isLastHint: hintLevel >= hints.length,
    );
  }

  /// 生成镜像对称提示
  Future<HintResult> _generateMirrorSymmetryHint(
    PuzzleEntity puzzle,
    int hintLevel,
    Map<String, dynamic> currentGameState,
  ) async {
    final hints = [
      '观察图形的对称轴位置',
      '想象图形沿对称轴翻折后的样子',
      '答案是：${puzzle.correctAnswer}',
    ];

    return HintResult(
      hintText: hints[hintLevel - 1],
      hintType: 'mirror_symmetry',
      hintData: {'symmetry_type': 'mirror'},
      hintLevel: hintLevel,
      isLastHint: hintLevel >= hints.length,
    );
  }

  /// 更新提示使用记录
  Future<void> _updateHintUsage(
    UserProfileEntity user,
    String levelId,
    int hintLevel,
  ) async {
    // 这里可以更新用户的提示使用统计
    // 目前暂时省略，因为这会在SaveProgressUseCase中处理
    _logger.d(
      'Hint usage updated for user: ${user.id}, level: $levelId, hint level: $hintLevel',
    );
  }
}

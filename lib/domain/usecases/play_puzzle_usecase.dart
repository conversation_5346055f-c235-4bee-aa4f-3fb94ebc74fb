import 'package:logger/logger.dart';

import '../entities/puzzle_entity.dart';
import '../entities/user_profile_entity.dart';
import '../repositories/puzzle_repository.dart';
import '../repositories/user_repository.dart';
import '../../core/utils/result.dart';
import '../../core/exceptions/app_exceptions.dart';
import 'base_usecase.dart';

/// 开始游戏参数
class PlayPuzzleParams extends UseCaseParams {
  final String userId;
  final String levelId;
  final Map<String, dynamic>? savedState;

  const PlayPuzzleParams({
    required this.userId,
    required this.levelId,
    this.savedState,
  });

  @override
  List<Object?> get props => [userId, levelId, savedState];

  @override
  Result<void> validate() {
    if (userId.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'INVALID_USER_ID', message: '用户ID不能为空'),
      );
    }
    if (levelId.isEmpty) {
      return Result.failure(
        PuzzleException(errorCode: 'INVALID_LEVEL_ID', message: '关卡ID不能为空'),
      );
    }
    return Result.success(null);
  }
}

/// 游戏开始结果
class PuzzleGameResult {
  final PuzzleEntity puzzle;
  final UserProfileEntity user;
  final Map<String, dynamic> savedState;
  final bool isResume;
  final bool canResume;

  const PuzzleGameResult({
    required this.puzzle,
    required this.user,
    required this.savedState,
    required this.isResume,
    required this.canResume,
  });
}

/// 开始游戏用例
class PlayPuzzleUseCase
    implements UseCase<Result<PuzzleGameResult>, PlayPuzzleParams> {
  final PuzzleRepository _puzzleRepository;
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  PlayPuzzleUseCase(this._puzzleRepository, this._userRepository);

  @override
  Future<Result<PuzzleGameResult>> call(PlayPuzzleParams params) async {
    try {
      _logger.i(
        'Starting puzzle game for user: ${params.userId}, level: ${params.levelId}',
      );

      // 验证参数
      final validation = params.validate();
      if (validation.isFailure) {
        return Result.failure(validation.exception!);
      }

      // 获取用户信息
      final userResult = await _userRepository.getUserById(params.userId);
      if (userResult.isFailure) {
        return Result.failure(userResult.exception!);
      }
      final user = userResult.data;
      if (user == null) {
        return Result.failure(
          UserException(errorCode: 'USER_NOT_FOUND', message: '用户不存在'),
        );
      }

      // 获取谜题信息
      final puzzleResult = await _puzzleRepository.getPuzzleById(
        params.levelId,
      );
      if (puzzleResult.isFailure) {
        return Result.failure(puzzleResult.exception!);
      }
      final puzzle = puzzleResult.data;
      if (puzzle == null) {
        return Result.failure(
          PuzzleException(errorCode: 'PUZZLE_NOT_FOUND', message: '谜题不存在'),
        );
      }

      // 准备游戏状态
      final gameState = await _prepareGameState(puzzle, params.savedState);
      final isResume = params.savedState != null;

      final result = PuzzleGameResult(
        puzzle: puzzle,
        user: user,
        savedState: gameState,
        isResume: isResume,
        canResume: params.savedState != null,
      );

      _logger.i(
        'Puzzle game started successfully for level: ${params.levelId}',
      );
      return Result.success(result);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to start puzzle game',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'START_GAME_ERROR',
          message: '开始游戏失败：${e.toString()}',
        ),
      );
    }
  }

  /// 准备游戏状态
  Future<Map<String, dynamic>> _prepareGameState(
    PuzzleEntity puzzle,
    Map<String, dynamic>? savedState,
  ) async {
    if (savedState != null) {
      // 恢复保存的游戏状态
      return Map<String, dynamic>.from(savedState);
    }

    // 创建新的游戏状态
    return {
      'puzzle_id': puzzle.levelId,
      'start_time': DateTime.now().toIso8601String(),
      'attempts': 0,
      'hints_used': 0,
      'current_answer': null,
      'game_data': {},
    };
  }
}

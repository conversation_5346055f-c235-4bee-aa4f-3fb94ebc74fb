import 'package:flutter/foundation.dart';
import '../core/constants/game_asset_categories.dart';
import '../core/managers/asset_manager.dart';
import '../core/generators/puzzle_generator.dart';
import '../domain/value_objects/puzzle_types.dart';

/// 分类素材系统使用示例
///
/// 展示如何使用新的分类素材系统来生成随机组合的题目和素材
class AssetClassificationExample {
  static final AssetManager _assetManager = AssetManager.instance;
  static final PuzzleGenerator _puzzleGenerator = PuzzleGenerator.instance;

  /// 示例1: 生成随机镜像对称素材组合
  static Future<void> exampleMirrorSymmetryAssets() async {
    debugPrint('=== 镜像对称素材组合示例 ===');

    // 启用调试模式
    AssetManager.enableDebugMode();

    // 生成5个不同难度的镜像对称素材组合
    for (int difficulty = 1; difficulty <= 3; difficulty++) {
      debugPrint('\n--- 难度 $difficulty ---');

      final assetResult = await _assetManager.generateRandomMirrorSymmetryAsset(
        difficulty: difficulty,
        requiredTags: ['clothing', 'pattern'],
      );

      if (assetResult.isSuccess) {
        final asset = assetResult.data!;
        debugPrint('组合名称: ${asset.name}');
        debugPrint('组合描述: ${asset.description}');
        debugPrint('衣服类型: ${asset.clothingType.displayName}');
        debugPrint('花纹类型: ${asset.patternType.displayName}');
        debugPrint('颜色类型: ${asset.colorType.displayName}');
        debugPrint('素材路径: ${asset.generateAssetPath()}');

        // 获取实际素材路径
        final assetPathResult = await _assetManager.getMirrorSymmetryAsset(
          asset,
        );
        if (assetPathResult.isSuccess) {
          debugPrint('实际素材路径: ${assetPathResult.data}');
        }
      } else {
        debugPrint('生成失败: ${assetResult.exception}');
      }
    }
  }

  /// 示例2: 生成随机图形推理素材组合
  static Future<void> exampleGraphicPatternAssets() async {
    debugPrint('\n=== 图形推理素材组合示例 ===');

    // 生成3个图形推理素材组合
    for (int i = 0; i < 3; i++) {
      debugPrint('\n--- 组合 ${i + 1} ---');

      final assetResult = await _assetManager.generateRandomGraphicPatternAsset(
        difficulty: 2,
        allowedShapeTypes: [
          ShapeType.circle,
          ShapeType.square,
          ShapeType.triangle,
        ],
        allowedColorTypes: [ColorType.red, ColorType.blue, ColorType.green],
      );

      if (assetResult.isSuccess) {
        final asset = assetResult.data!;
        debugPrint('组合名称: ${asset.name}');
        debugPrint('组合描述: ${asset.description}');
        debugPrint('形状类型: ${asset.shapeType.displayName}');
        debugPrint('填充类型: ${asset.fillType.displayName}');
        debugPrint('颜色类型: ${asset.colorType.displayName}');
        debugPrint('大小类型: ${asset.sizeType.displayName}');
        debugPrint('素材路径: ${asset.generateAssetPath()}');
      }
    }
  }

  /// 示例3: 生成随机空间想象素材组合
  static Future<void> exampleSpatialVisualizationAssets() async {
    debugPrint('\n=== 空间想象素材组合示例 ===');

    // 生成不同视角的3D形状组合
    final viewTypes = [ViewType.front, ViewType.isometric, ViewType.top];

    for (final viewType in viewTypes) {
      debugPrint('\n--- ${viewType.displayName}视角 ---');

      final assetResult = await _assetManager
          .generateRandomSpatialVisualizationAsset(
            difficulty: 3,
            allowedViewTypes: [viewType],
            allowedShapeTypes: [
              Shape3DType.cube,
              Shape3DType.pyramid,
              Shape3DType.cylinder,
            ],
          );

      if (assetResult.isSuccess) {
        final asset = assetResult.data!;
        debugPrint('组合名称: ${asset.name}');
        debugPrint('组合描述: ${asset.description}');
        debugPrint('3D形状: ${asset.shapeType.displayName}');
        debugPrint('视角类型: ${asset.viewType.displayName}');
        debugPrint('材质类型: ${asset.materialType.displayName}');
        debugPrint('颜色类型: ${asset.colorType.displayName}');
        debugPrint('素材路径: ${asset.generateAssetPath()}');
      }
    }
  }

  /// 示例4: 生成随机编程启蒙素材组合
  static Future<void> exampleCodingAssets() async {
    debugPrint('\n=== 编程启蒙素材组合示例 ===');

    // 生成不同环境的角色组合
    final environments = [
      EnvironmentType.forest,
      EnvironmentType.space,
      EnvironmentType.castle,
    ];

    for (final environment in environments) {
      debugPrint('\n--- ${environment.displayName}环境 ---');

      final assetResult = await _assetManager.generateRandomCodingAsset(
        difficulty: 2,
        allowedEnvironmentTypes: [environment],
        allowedCharacterTypes: [
          CharacterType.robot,
          CharacterType.knight,
          CharacterType.wizard,
        ],
      );

      if (assetResult.isSuccess) {
        final asset = assetResult.data!;
        debugPrint('组合名称: ${asset.name}');
        debugPrint('组合描述: ${asset.description}');
        debugPrint('角色类型: ${asset.characterType.displayName}');
        debugPrint('角色状态: ${asset.characterState.displayName}');
        debugPrint('环境类型: ${asset.environmentType.displayName}');
        debugPrint('角色素材路径: ${asset.generateCharacterAssetPath()}');
        debugPrint('环境素材路径: ${asset.generateEnvironmentAssetPath()}');
      }
    }
  }

  /// 示例5: 批量生成素材组合
  static Future<void> exampleBatchAssetGeneration() async {
    debugPrint('\n=== 批量素材组合生成示例 ===');

    // 批量生成10个镜像对称素材组合
    final batchResult = await _assetManager
        .generateMultipleAssets<MirrorSymmetryAssetCombination>(
          () => _assetManager.generateRandomMirrorSymmetryAsset(difficulty: 2),
          10,
          allowDuplicates: false,
        );

    if (batchResult.isSuccess) {
      final assets = batchResult.data!;
      debugPrint('成功生成 ${assets.length} 个镜像对称素材组合:');

      for (int i = 0; i < assets.length; i++) {
        final asset = assets[i];
        debugPrint('${i + 1}. ${asset.name} - ${asset.description}');
      }
    } else {
      debugPrint('批量生成失败: ${batchResult.exception}');
    }
  }

  /// 示例6: 生成完整的镜像对称题目
  static Future<void> exampleMirrorSymmetryPuzzle() async {
    debugPrint('\n=== 镜像对称题目生成示例 ===');

    // 生成不同难度的镜像对称题目
    for (int difficulty = 1; difficulty <= 3; difficulty++) {
      debugPrint('\n--- 难度 $difficulty 题目 ---');

      final puzzleResult = await _puzzleGenerator.generateMirrorSymmetryPuzzle(
        difficulty: difficulty,
        requiredTags: ['beginner'],
      );

      if (puzzleResult.isSuccess) {
        final puzzle = puzzleResult.data!;
        debugPrint('题目ID: ${puzzle.id}');
        debugPrint('题目标题: ${puzzle.title}');
        debugPrint('题目描述: ${puzzle.description}');
        debugPrint('难度等级: ${puzzle.difficulty}');
        debugPrint('预估时间: ${puzzle.estimatedTime.inSeconds}秒');
        debugPrint('提示数量: ${puzzle.hints.length}');
        debugPrint('题目数据: ${puzzle.data}');

        // 显示提示
        debugPrint('提示内容:');
        for (int i = 0; i < puzzle.hints.length; i++) {
          debugPrint('  ${i + 1}. ${puzzle.hints[i]}');
        }
      } else {
        debugPrint('题目生成失败: ${puzzleResult.exception}');
      }
    }
  }

  /// 示例7: 生成完整的图形推理题目
  static Future<void> exampleGraphicPatternPuzzle() async {
    debugPrint('\n=== 图形推理题目生成示例 ===');

    final puzzleResult = await _puzzleGenerator.generateGraphicPatternPuzzle(
      difficulty: 2,
      requiredTags: ['pattern', 'logic'],
    );

    if (puzzleResult.isSuccess) {
      final puzzle = puzzleResult.data!;
      debugPrint('题目ID: ${puzzle.id}');
      debugPrint('题目标题: ${puzzle.title}');
      debugPrint('题目描述: ${puzzle.description}');
      debugPrint('难度等级: ${puzzle.difficulty}');
      debugPrint('预估时间: ${puzzle.estimatedTime.inSeconds}秒');

      // 显示题目数据结构
      final data = puzzle.data;
      debugPrint('网格大小: ${data['gridSize']}x${data['gridSize']}');
      debugPrint('缺失位置: ${data['missingPosition']}');
      debugPrint('选项数量: ${data['options'].length}');
      debugPrint('正确答案索引: ${data['correctAnswer']}');
      debugPrint('显示网格线: ${data['showGrid']}');

      // 显示提示
      debugPrint('提示内容:');
      for (int i = 0; i < puzzle.hints.length; i++) {
        debugPrint('  ${i + 1}. ${puzzle.hints[i]}');
      }
    } else {
      debugPrint('题目生成失败: ${puzzleResult.exception}');
    }
  }

  /// 示例8: 批量生成不同类型的题目
  static Future<void> exampleBatchPuzzleGeneration() async {
    debugPrint('\n=== 批量题目生成示例 ===');

    // 批量生成5个镜像对称题目
    final mirrorPuzzlesResult = await _puzzleGenerator.generateMultiplePuzzles(
      PuzzleType.mirrorSymmetry,
      5,
      difficulty: 2,
      requiredTags: ['batch_test'],
    );

    if (mirrorPuzzlesResult.isSuccess) {
      final puzzles = mirrorPuzzlesResult.data!;
      debugPrint('成功生成 ${puzzles.length} 个镜像对称题目:');

      for (int i = 0; i < puzzles.length; i++) {
        final puzzle = puzzles[i];
        debugPrint(
          '${i + 1}. ${puzzle.title} (${puzzle.estimatedTime.inSeconds}秒)',
        );
      }
    }

    // 批量生成3个图形推理题目
    final graphicPuzzlesResult = await _puzzleGenerator.generateMultiplePuzzles(
      PuzzleType.graphicPattern3x3,
      3,
      difficulty: 3,
      requiredTags: ['advanced'],
    );

    if (graphicPuzzlesResult.isSuccess) {
      final puzzles = graphicPuzzlesResult.data!;
      debugPrint('成功生成 ${puzzles.length} 个图形推理题目:');

      for (int i = 0; i < puzzles.length; i++) {
        final puzzle = puzzles[i];
        debugPrint(
          '${i + 1}. ${puzzle.title} (${puzzle.estimatedTime.inSeconds}秒)',
        );
      }
    }
  }

  /// 示例9: 素材分类统计
  static void exampleAssetCategoryStats() {
    debugPrint('\n=== 素材分类统计示例 ===');

    debugPrint('衣服类型数量: ${ClothingType.values.length}');
    debugPrint('花纹类型数量: ${PatternType.values.length}');
    debugPrint('颜色类型数量: ${ColorType.values.length}');
    debugPrint('图形类型数量: ${ShapeType.values.length}');
    debugPrint('填充类型数量: ${FillType.values.length}');
    debugPrint('3D形状类型数量: ${Shape3DType.values.length}');
    debugPrint('视角类型数量: ${ViewType.values.length}');
    debugPrint('角色类型数量: ${CharacterType.values.length}');
    debugPrint('环境类型数量: ${EnvironmentType.values.length}');

    // 计算理论组合数量
    final mirrorCombinations =
        ClothingType.values.length *
        PatternType.values.length *
        ColorType.values.length;
    debugPrint('镜像对称理论组合数量: $mirrorCombinations');

    final graphicCombinations =
        ShapeType.values.length *
        FillType.values.length *
        ColorType.values.length *
        SizeType.values.length;
    debugPrint('图形推理理论组合数量: $graphicCombinations');

    final spatialCombinations =
        Shape3DType.values.length *
        ViewType.values.length *
        AssetMaterialType.values.length *
        ColorType.values.length;
    debugPrint('空间想象理论组合数量: $spatialCombinations');
  }

  /// 运行所有示例
  static Future<void> runAllExamples() async {
    debugPrint('开始运行分类素材系统示例...\n');

    // 运行素材生成示例
    await exampleMirrorSymmetryAssets();
    await exampleGraphicPatternAssets();
    await exampleSpatialVisualizationAssets();
    await exampleCodingAssets();
    await exampleBatchAssetGeneration();

    // 运行题目生成示例
    await exampleMirrorSymmetryPuzzle();
    await exampleGraphicPatternPuzzle();
    await exampleBatchPuzzleGeneration();

    // 运行统计示例
    exampleAssetCategoryStats();

    debugPrint('\n所有示例运行完成！');
  }
}

/// 在应用中使用示例的入口点
class AssetClassificationDemo {
  /// 演示基本用法
  static Future<void> basicUsageDemo() async {
    debugPrint('=== 基本用法演示 ===');

    // 1. 生成单个素材组合
    final assetManager = AssetManager.instance;
    final mirrorAssetResult = await assetManager
        .generateRandomMirrorSymmetryAsset(
          difficulty: 1,
          allowedClothingTypes: [ClothingType.shirt, ClothingType.dress],
          allowedPatternTypes: [PatternType.heart, PatternType.star],
          allowedColorTypes: [ColorType.red, ColorType.blue],
        );

    if (mirrorAssetResult.isSuccess) {
      final asset = mirrorAssetResult.data!;
      debugPrint('生成的素材组合: ${asset.description}');

      // 2. 获取素材路径
      final assetPathResult = await assetManager.getMirrorSymmetryAsset(asset);
      if (assetPathResult.isSuccess) {
        debugPrint('素材路径: ${assetPathResult.data}');
      }
    }

    // 3. 生成完整题目
    final puzzleGenerator = PuzzleGenerator.instance;
    final puzzleResult = await puzzleGenerator.generateMirrorSymmetryPuzzle(
      difficulty: 1,
      requiredTags: ['demo'],
    );

    if (puzzleResult.isSuccess) {
      final puzzle = puzzleResult.data!;
      debugPrint('生成的题目: ${puzzle.title}');
      debugPrint('预估时间: ${puzzle.estimatedTime.inSeconds}秒');
    }
  }

  /// 演示高级用法
  static Future<void> advancedUsageDemo() async {
    debugPrint('\n=== 高级用法演示 ===');

    // 1. 约束条件生成
    final assetManager = AssetManager.instance;

    // 2. 批量生成不重复的素材组合
    final batchResult = await assetManager
        .generateMultipleAssets<MirrorSymmetryAssetCombination>(
          () => assetManager.generateRandomMirrorSymmetryAsset(
            difficulty: 2,
            allowedColorTypes: [ColorType.red, ColorType.blue, ColorType.green],
          ),
          5,
          allowDuplicates: false,
        );

    if (batchResult.isSuccess) {
      debugPrint('批量生成了 ${batchResult.data!.length} 个不重复的素材组合');
    }

    // 3. 生成适应性难度的题目
    final puzzleGenerator = PuzzleGenerator.instance;
    for (int difficulty = 1; difficulty <= 3; difficulty++) {
      final puzzleResult = await puzzleGenerator.generateGraphicPatternPuzzle(
        difficulty: difficulty,
        requiredTags: ['adaptive'],
      );

      if (puzzleResult.isSuccess) {
        final puzzle = puzzleResult.data!;
        debugPrint(
          '难度 $difficulty 题目: ${puzzle.title} (${puzzle.estimatedTime.inSeconds}秒)',
        );
      }
    }
  }
}

# LogicLab - 逻辑实验室

一个专为6-12岁儿童设计的逻辑思维训练应用，采用Flutter开发，支持多平台运行。

## 📱 项目概述

LogicLab是一款教育类游戏应用，通过四种核心谜题类型帮助儿童发展逻辑思维能力：
- 🔍 **图形推理** - 3×3图形模式识别
- 🧊 **空间想象** - 3D立体图形折叠
- 🔢 **数字逻辑** - 4×4数独类逻辑推理
- 🤖 **编程启蒙** - 基础编程概念和算法思维

## 🏗️ 架构设计

项目采用**Clean Architecture**架构模式，确保代码的可维护性和可测试性：

```
lib/
├── core/                    # 核心模块
│   ├── constants/           # 应用常量和主题
│   ├── utils/              # 工具类
│   └── extensions/         # 扩展方法
├── data/                   # 数据层
│   ├── models/             # 数据模型
│   ├── repositories/       # 数据仓库实现
│   └── datasources/        # 数据源
├── domain/                 # 业务逻辑层
│   ├── entities/           # 业务实体
│   ├── repositories/       # 仓库接口
│   └── usecases/          # 用例
├── presentation/           # 表现层
│   ├── bloc/              # 状态管理
│   ├── pages/             # 页面
│   └── widgets/           # 通用组件
└── services/              # 服务层
    ├── user_service.dart  # 用户管理服务
    └── puzzle_engine.dart # 谜题引擎
```

## 🛠️ 技术栈

### 核心框架
- **Flutter 3.32.2** - 跨平台UI框架
- **Dart 3.8.1** - 编程语言

### 状态管理
- **flutter_bloc 8.1.6** - BLoC状态管理模式
- **equatable 2.0.5** - 对象比较

### 本地存储
- **hive 2.2.3** - 轻量级NoSQL数据库
- **hive_flutter 1.1.0** - Flutter集成
- **path_provider 2.1.4** - 路径管理

### 多媒体
- **audioplayers 5.2.1** - 音频播放
- **rive 0.13.20** - 高质量动画
- **lottie 3.1.2** - Lottie动画

### 开发工具
- **build_runner 2.4.12** - 代码生成
- **json_serializable 6.8.0** - JSON序列化
- **logger 2.4.0** - 日志管理

## 🎯 已完成功能

### ✅ 核心架构
- [x] 项目结构搭建
- [x] Clean Architecture实现
- [x] 依赖注入配置
- [x] 主题系统设计

### ✅ 数据层
- [x] 用户档案数据模型
- [x] 谜题数据模型
- [x] Hive本地存储配置
- [x] JSON序列化支持

### ✅ 服务层
- [x] 用户服务 - 完整的CRUD操作
- [x] 谜题引擎 - 核心游戏逻辑
- [x] 答案验证系统
- [x] 智能提示系统

### ✅ 表现层
- [x] 启动页面
- [x] 应用主题配置
- [x] 响应式设计
- [x] 错误处理机制

### ✅ 代码质量优化 (2024年12月更新)
- [x] **统一错误处理机制** - 完整的错误码系统和异常类层次结构
- [x] **完善参数验证** - 可组合的验证器框架和Use Cases层验证
- [x] **接口设计优化** - 统一的Result<T>返回类型和接口拆分
- [x] **Repository接口拆分** - 按职责拆分的专门接口
- [x] **缓存策略实现** - 完整的缓存管理器和装饰器
- [x] **批量操作支持** - Repository、Use Cases和性能优化层的批量接口

## 🚀 快速开始

### 环境要求
- Flutter SDK >= 3.8.1
- Dart SDK >= 3.8.1
- iOS 12.0+ / Android API 21+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/LogicLab.git
   cd LogicLab
   ```

2. **安装依赖**
   ```bash
   flutter pub get
   ```

3. **生成代码**
   ```bash
   dart run build_runner build
   ```

4. **运行应用**
   ```bash
   flutter run
   ```

## 📊 数据模型

### 用户档案 (UserProfile)
```dart
class UserProfile {
  final String id;                              // 用户唯一标识
  final String nickname;                        // 昵称
  final String avatarId;                        // 头像ID
  final Map<String, LevelProgress> levelProgress; // 关卡进度
  final Set<String> unlockedAchievements;       // 解锁成就
  final Map<String, int> skillPoints;          // 技能点数
  final UserSettings settings;                  // 用户设置
}
```

### 谜题 (Puzzle)
```dart
class Puzzle {
  final String levelId;                         // 关卡ID
  final PuzzleType puzzleType;                  // 谜题类型
  final DifficultyLevel difficulty;             // 难度等级
  final String prompt;                          // 题目描述
  final Map<String, dynamic> data;             // 谜题数据
  final ThemeWorld? themeWorld;                 // 主题世界
}
```

## 🎮 谜题系统

### 图形推理 (3×3)
- 模式识别和规律发现
- 支持多种图形组合
- 渐进式难度设计

### 空间想象
- 2D展开图到3D立体图形
- 培养空间思维能力
- 直观的视觉反馈

### 数字逻辑 (4×4)
- 类似数独的逻辑推理
- 行列约束和区域约束
- 多种图标组合

### 编程启蒙
- 基础编程概念
- 序列化思维训练
- 可视化编程界面

## 🌟 特色功能

### 🧠 智能难度调节
- 基于用户表现动态调整
- 个性化学习路径
- 避免挫败感和无聊感

### 🎯 分级提示系统
- 一级提示：引导思考方向
- 二级提示：排除错误选项
- 保护解题成就感

### 👨‍👩‍👧‍👦 多用户支持
- 最多4个用户档案
- 独立进度追踪
- 家长监控功能

### 🏆 成就系统
- 多维度成就设计
- 即时反馈激励
- 长期目标设定

## 📚 文档

### 核心文档
- [项目需求文档 (PRD)](./docs/prd.md) - 产品需求和功能规格
- [系统架构文档](./docs/ARCHITECTURE.md) - 技术架构和设计决策
- [实现总结](./docs/IMPLEMENTATION_SUMMARY.md) - 开发进展和技术实现
- [项目状态](./docs/project_status.md) - 当前开发状态和里程碑

### 功能模块文档
- [答案解析系统](./docs/answer_explanation_system.md) - 通用答案解析功能设计与实现
- [镜像对称游戏指南](./docs/MIRROR_SYMMETRY_GAME_GUIDE.md) - 镜像对称游戏功能说明
- [UX设计规范](./docs/UX_DESIGN_GUIDELINES.md) - 用户体验设计指南

### 开发文档
- [待办事项](./docs/TODO.md) - 开发任务和优先级
- [架构扩展性](./docs/ARCHITECTURE_EXTENSIBILITY.md) - 架构扩展指南
- [主题系统实现](./docs/THEME_SERVICE_ARCHITECTURE.md) - 主题系统架构

## 🔄 下一步开发计划

### 🎨 UI/UX
- [ ] 用户创建页面
- [ ] 主页设计
- [ ] 谜题游戏界面
- [ ] 进度统计页面

### 🎯 游戏功能
- [x] 答案解析系统 - 通用解析功能已完成
- [ ] BLoC状态管理实现
- [ ] 音效和背景音乐
- [ ] 动画效果优化
- [ ] 离线模式完善

### 📱 平台适配
- [ ] iOS平台测试
- [ ] Android平台测试
- [ ] 响应式布局优化
- [ ] 无障碍访问支持

### 🌍 国际化
- [ ] 多语言支持
- [ ] 文化本地化
- [ ] RTL语言支持

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📞 联系我们

- 项目主页: [GitHub Repository](https://github.com/your-username/LogicLab)
- 问题反馈: [Issues](https://github.com/your-username/LogicLab/issues)
- 电子邮件: <EMAIL>

---

**LogicLab** - 让逻辑思维变得有趣！🧠✨

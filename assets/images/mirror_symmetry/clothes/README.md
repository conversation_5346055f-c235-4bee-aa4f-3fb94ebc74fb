# 镜像对称游戏衣服素材

## 文件命名规范

所有衣服素材文件应遵循以下命名规范：
```
{衣服类型}_{编号}.png
```

## 支持的衣服类型

- `shirt` - 衬衫/T恤
- `dress` - 连衣裙
- `pants` - 裤子
- `skirt` - 裙子
- `jacket` - 夹克/外套

## 示例文件名

- `shirt_01.png` - 第1件衬衫
- `dress_01.png` - 第1件连衣裙
- `pants_01.png` - 第1条裤子
- `skirt_01.png` - 第1条裙子
- `jacket_01.png` - 第1件夹克

## 素材规格要求

- **尺寸**: 128x128px
- **格式**: PNG (支持透明背景)
- **分辨率**: 72 DPI
- **颜色模式**: RGB
- **文件大小**: 建议小于50KB

## 设计要求

1. **图案位置**: 衣服上的图案应该有明显的左右区别
2. **对称性**: 确保图案在镜像时能产生明显的视觉差异
3. **简洁性**: 图案应该简单明了，便于儿童理解
4. **色彩**: 使用明亮、对比度高的颜色
5. **背景**: 使用透明背景，便于在不同场景中使用

## 当前状态

⚠️ **素材待制作**

当前目录中的素材文件为占位符，需要制作实际的游戏素材。

## 制作建议

可以使用以下工具制作素材：
- Adobe Illustrator
- Figma
- Canva
- DALL-E 3 (AI生成)
- Midjourney (AI生成)

## 临时解决方案

在正式素材制作完成前，游戏会使用内置的占位符图标显示。 
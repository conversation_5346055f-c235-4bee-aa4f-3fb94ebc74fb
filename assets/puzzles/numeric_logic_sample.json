{"levelId": "numeric_001", "schemaVersion": "1.0.0", "author": "LogicLab Team", "tags": ["logic", "sudoku", "beginner"], "puzzleType": "NUMERIC_LOGIC", "difficulty": "easy", "prompt": "在4×4的网格中填入水果图标，确保每行、每列、每个2×2宫格内，四种水果都只出现一次。", "themeWorld": "forest", "orderInWorld": 2, "data": {"grid": ["apple", null, null, "grape", null, "banana", "apple", null, null, "grape", "banana", null, "banana", null, null, "apple"], "availableItems": ["apple", "banana", "grape", "orange"], "constraints": {"rows": 4, "cols": 4, "subgrids": [{"startRow": 0, "startCol": 0, "size": 2}, {"startRow": 0, "startCol": 2, "size": 2}, {"startRow": 2, "startCol": 0, "size": 2}, {"startRow": 2, "startCol": 2, "size": 2}]}}}
{"levelId": "coding_001", "schemaVersion": "1.0.0", "author": "LogicLab Team", "tags": ["coding", "maze", "beginner"], "puzzleType": "INTRO_TO_CODING", "difficulty": "easy", "prompt": "帮助小动物通过迷宫找到宝物！使用前进、左转、右转指令来编写路径。", "themeWorld": "forest", "orderInWorld": 3, "data": {"maze": {"grid": [[0, 0, 1, 0], [1, 0, 1, 0], [1, 0, 0, 0], [1, 1, 1, 0]]}, "startPosition": {"x": 0, "y": 0}, "endPosition": {"x": 3, "y": 3}, "availableCommands": ["forward", "turn_left", "turn_right"], "maxCommands": 8}}
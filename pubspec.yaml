name: logic_lab
description: "一个专为6-12岁儿童设计的逻辑思维训练应用"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  
  # Localization
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  
  # State Management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.5
  
  # Local Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Audio
  audioplayers: ^6.5.0
  
  # Animations
  rive: ^0.13.20
  lottie: ^3.1.2
  
  # Utilities
  path_provider: ^2.1.4
  json_annotation: ^4.9.0
  shared_preferences: ^2.2.2
  
  # Dependency Injection
  get_it: ^8.0.0
  
  # Logging
  logger: ^2.4.0

# dependency_overrides:
#   analyzer: '7.5.2'
#   source_gen: '2.0.0'
#   dart_style: '3.1.0'

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  
  # Code Generation
  build_runner: ^2.0.0
  hive_generator: ^2.0.1
  json_serializable: ^6.9.0


  
  # Testing
  bloc_test: ^10.0.0
  mocktail: ^1.0.4

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/audio/
    - assets/puzzles/
    - assets/animations/

  # Fonts (TODO: 添加字体文件后启用)
  # fonts:
  #   - family: ComicSans
  #     fonts:
  #       - asset: assets/fonts/ComicSans-Regular.ttf
  #       - asset: assets/fonts/ComicSans-Bold.ttf
  #         weight: 700 
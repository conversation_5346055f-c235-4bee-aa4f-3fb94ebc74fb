import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'lib/core/constants/game_asset_categories.dart';
import 'lib/core/managers/asset_manager.dart';
import 'lib/core/generators/puzzle_generator.dart';
import 'lib/core/constants/game_assets.dart';
import 'lib/services/puzzle_engine.dart';
import 'lib/core/constants/app_constants.dart';

/// 素材分类系统架构集成测试
/// 
/// 测试所有新组件的集成和向后兼容性
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🚀 开始素材分类系统架构集成测试...\n');
  
  try {
    // 1. 测试素材分类定义
    await testAssetCategories();
    
    // 2. 测试AssetManager
    await testAssetManager();
    
    // 3. 测试PuzzleGenerator
    await testPuzzleGenerator();
    
    // 4. 测试GameAssets向后兼容性
    await testGameAssetsCompatibility();
    
    // 5. 测试PuzzleEngine集成
    await testPuzzleEngineIntegration();
    
    // 6. 测试性能
    await testPerformance();
    
    print('\n✅ 所有测试通过！架构修订成功！');
    
  } catch (e, stackTrace) {
    print('\n❌ 测试失败: $e');
    print('堆栈跟踪: $stackTrace');
  }
}

/// 测试素材分类定义
Future<void> testAssetCategories() async {
  print('📋 测试素材分类定义...');
  
  // 测试镜像对称分类
  final clothingTypes = ClothingType.values;
  final patternTypes = PatternType.values;
  final colorTypes = ColorType.values;
  
  print('  - 衣服类型: ${clothingTypes.length}种');
  print('    ${clothingTypes.map((e) => e.displayName).join(', ')}');
  
  print('  - 花纹类型: ${patternTypes.length}种');
  print('    ${patternTypes.map((e) => e.displayName).join(', ')}');
  
  print('  - 颜色类型: ${colorTypes.length}种');
  print('    ${colorTypes.map((e) => e.displayName).join(', ')}');
  
  // 计算组合数量
  final mirrorCombinations = clothingTypes.length * patternTypes.length * colorTypes.length;
  print('  - 镜像对称理论组合数: $mirrorCombinations');
  
  // 测试图形推理分类
  final shapeTypes = ShapeType.values;
  final fillTypes = FillType.values;
  final sizeTypes = SizeType.values;
  
  final graphicCombinations = shapeTypes.length * fillTypes.length * colorTypes.length * sizeTypes.length;
  print('  - 图形推理理论组合数: $graphicCombinations');
  
  // 测试空间想象分类
  final shape3DTypes = Shape3DType.values;
  final viewTypes = ViewType.values;
  final materialTypes = MaterialType.values;
  
  final spatialCombinations = shape3DTypes.length * viewTypes.length * materialTypes.length * colorTypes.length;
  print('  - 空间想象理论组合数: $spatialCombinations');
  
  // 测试编程启蒙分类
  final characterTypes = CharacterType.values;
  final characterStates = CharacterState.values;
  final environmentTypes = EnvironmentType.values;
  
  final codingCombinations = characterTypes.length * characterStates.length * environmentTypes.length;
  print('  - 编程启蒙理论组合数: $codingCombinations');
  
  final totalCombinations = mirrorCombinations + graphicCombinations + spatialCombinations + codingCombinations;
  print('  - 总理论组合数: $totalCombinations');
  
  print('✅ 素材分类定义测试通过\n');
}

/// 测试AssetManager
Future<void> testAssetManager() async {
  print('🎨 测试AssetManager...');
  
  final assetManager = AssetManager.instance;
  
  // 启用调试模式
  assetManager.enableDebugMode();
  
  // 测试镜像对称素材生成
  print('  - 测试镜像对称素材生成...');
  final mirrorResult = await assetManager.generateRandomMirrorSymmetryAsset(difficulty: 2);
  
  if (mirrorResult.isSuccess) {
    final asset = mirrorResult.data!;
    print('    生成成功: ${asset.clothingType.displayName} + ${asset.patternType.displayName} + ${asset.colorType.displayName}');
    print('    素材路径: ${asset.assetPath}');
  } else {
    print('    生成失败: ${mirrorResult.error}');
  }
  
  // 测试图形推理素材生成
  print('  - 测试图形推理素材生成...');
  final graphicResult = await assetManager.generateRandomGraphicPatternAsset(difficulty: 2);
  
  if (graphicResult.isSuccess) {
    final asset = graphicResult.data!;
    print('    生成成功: ${asset.shapeType.displayName} + ${asset.fillType.displayName} + ${asset.colorType.displayName} + ${asset.sizeType.displayName}');
    print('    素材路径: ${asset.assetPath}');
  } else {
    print('    生成失败: ${graphicResult.error}');
  }
  
  // 测试批量生成
  print('  - 测试批量生成不重复素材...');
  final batchResult = await assetManager.generateMultipleAssets<MirrorSymmetryAssetCombination>(
    () => assetManager.generateRandomMirrorSymmetryAsset(difficulty: 2),
    5,
    allowDuplicates: false,
  );
  
  if (batchResult.isSuccess) {
    print('    批量生成成功: ${batchResult.data!.length}个不重复素材');
    for (int i = 0; i < batchResult.data!.length; i++) {
      final asset = batchResult.data![i];
      print('    [$i] ${asset.clothingType.displayName} + ${asset.patternType.displayName} + ${asset.colorType.displayName}');
    }
  } else {
    print('    批量生成失败: ${batchResult.error}');
  }
  
  print('✅ AssetManager测试通过\n');
}

/// 测试PuzzleGenerator
Future<void> testPuzzleGenerator() async {
  print('🧩 测试PuzzleGenerator...');
  
  final puzzleGenerator = PuzzleGenerator.instance;
  
  // 测试镜像对称题目生成
  print('  - 测试镜像对称题目生成...');
  final mirrorPuzzleResult = await puzzleGenerator.generateMirrorSymmetryPuzzle(
    difficulty: 2,
    requiredTags: ['clothing', 'pattern'],
  );
  
  if (mirrorPuzzleResult.isSuccess) {
    final puzzle = mirrorPuzzleResult.data!;
    print('    题目生成成功: ${puzzle.title}');
    print('    难度: ${puzzle.difficulty.displayName}');
    print('    类型: ${puzzle.puzzleType.displayName}');
    print('    选项数量: ${(puzzle.data['options'] as List?)?.length ?? 0}');
  } else {
    print('    题目生成失败: ${mirrorPuzzleResult.error}');
  }
  
  // 测试图形推理题目生成
  print('  - 测试图形推理题目生成...');
  final graphicPuzzleResult = await puzzleGenerator.generateGraphicPatternPuzzle(
    difficulty: 3,
    requiredTags: ['shape', 'color'],
  );
  
  if (graphicPuzzleResult.isSuccess) {
    final puzzle = graphicPuzzleResult.data!;
    print('    题目生成成功: ${puzzle.title}');
    print('    难度: ${puzzle.difficulty.displayName}');
    print('    选项数量: ${(puzzle.data['options'] as List?)?.length ?? 0}');
  } else {
    print('    题目生成失败: ${graphicPuzzleResult.error}');
  }
  
  print('✅ PuzzleGenerator测试通过\n');
}

/// 测试GameAssets向后兼容性
Future<void> testGameAssetsCompatibility() async {
  print('🔄 测试GameAssets向后兼容性...');
  
  // 启用调试模式
  GameAssets.enableDebugMode();
  
  // 测试原有方法
  print('  - 测试原有方法...');
  final oldMirrorPath = GameAssets.mirrorClothing(ClothingType.shirt, '01');
  print('    原有镜像素材路径: $oldMirrorPath');
  
  final oldGraphicPath = GameAssets.graphicPattern('circle_red');
  print('    原有图形素材路径: $oldGraphicPath');
  
  // 测试新增方法
  print('  - 测试新增组合方法...');
  final newMirrorPath = GameAssets.mirrorClothingWithPattern(
    ClothingType.dress,
    PatternType.heart,
    ColorType.red,
  );
  print('    新组合镜像素材路径: $newMirrorPath');
  
  final newGraphicPath = GameAssets.graphicPatternWithShape(
    ShapeType.circle,
    FillType.solid,
    ColorType.blue,
    SizeType.medium,
  );
  print('    新组合图形素材路径: $newGraphicPath');
  
  // 测试直接访问AssetManager
  print('  - 测试直接访问AssetManager...');
  final assetManager = GameAssets.assetManager;
  print('    AssetManager实例获取成功: ${assetManager.runtimeType}');
  
  print('✅ GameAssets向后兼容性测试通过\n');
}

/// 测试PuzzleEngine集成
Future<void> testPuzzleEngineIntegration() async {
  print('⚙️ 测试PuzzleEngine集成...');
  
  final puzzleEngine = PuzzleEngine();
  
  // 初始化PuzzleEngine
  await puzzleEngine.initialize();
  
  // 测试新的题目生成方法
  print('  - 测试新的题目生成方法...');
  
  final mirrorPuzzle = await puzzleEngine.generateMirrorSymmetryPuzzle(
    difficulty: 2,
    requiredTags: ['clothing'],
  );
  
  if (mirrorPuzzle != null) {
    print('    镜像对称题目生成成功: ${mirrorPuzzle.title}');
  } else {
    print('    镜像对称题目生成失败');
  }
  
  final graphicPuzzle = await puzzleEngine.generateGraphicPatternPuzzle(
    difficulty: 2,
    requiredTags: ['shape'],
  );
  
  if (graphicPuzzle != null) {
    print('    图形推理题目生成成功: ${graphicPuzzle.title}');
  } else {
    print('    图形推理题目生成失败');
  }
  
  // 测试批量生成
  print('  - 测试批量题目生成...');
  final multiplePuzzles = await puzzleEngine.generateMultiplePuzzles(
    puzzleType: PuzzleType.mirrorSymmetry,
    difficulty: 2,
    count: 3,
    allowDuplicates: false,
  );
  
  print('    批量生成成功: ${multiplePuzzles.length}个题目');
  for (int i = 0; i < multiplePuzzles.length; i++) {
    print('    [$i] ${multiplePuzzles[i].title}');
  }
  
  // 测试访问新管理器
  print('  - 测试访问新管理器...');
  final assetManager = puzzleEngine.assetManager;
  final puzzleGenerator = puzzleEngine.puzzleGenerator;
  print('    AssetManager访问成功: ${assetManager.runtimeType}');
  print('    PuzzleGenerator访问成功: ${puzzleGenerator.runtimeType}');
  
  print('✅ PuzzleEngine集成测试通过\n');
}

/// 测试性能
Future<void> testPerformance() async {
  print('⚡ 测试性能...');
  
  final assetManager = AssetManager.instance;
  final puzzleGenerator = PuzzleGenerator.instance;
  
  // 测试素材生成性能
  print('  - 测试素材生成性能...');
  final stopwatch1 = Stopwatch()..start();
  
  for (int i = 0; i < 100; i++) {
    await assetManager.generateRandomMirrorSymmetryAsset(difficulty: 2);
  }
  
  stopwatch1.stop();
  print('    100个镜像对称素材生成耗时: ${stopwatch1.elapsedMilliseconds}ms');
  print('    平均每个素材生成耗时: ${stopwatch1.elapsedMilliseconds / 100}ms');
  
  // 测试题目生成性能
  print('  - 测试题目生成性能...');
  final stopwatch2 = Stopwatch()..start();
  
  for (int i = 0; i < 10; i++) {
    await puzzleGenerator.generateMirrorSymmetryPuzzle(difficulty: 2);
  }
  
  stopwatch2.stop();
  print('    10个镜像对称题目生成耗时: ${stopwatch2.elapsedMilliseconds}ms');
  print('    平均每个题目生成耗时: ${stopwatch2.elapsedMilliseconds / 10}ms');
  
  // 测试批量生成性能
  print('  - 测试批量生成性能...');
  final stopwatch3 = Stopwatch()..start();
  
  await assetManager.generateMultipleAssets<MirrorSymmetryAssetCombination>(
    () => assetManager.generateRandomMirrorSymmetryAsset(difficulty: 2),
    50,
    allowDuplicates: false,
  );
  
  stopwatch3.stop();
  print('    50个不重复素材批量生成耗时: ${stopwatch3.elapsedMilliseconds}ms');
  
  print('✅ 性能测试通过\n');
} 
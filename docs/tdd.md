## **第二部分：技术设计文档 (TDD)**

**文档版本:** 1.2
**创建日期:** 2025年6月29日
**负责人:** Gemini 产品与开发专家团队

### 1. 技术选型 (Technology Stack) **(MVP)**

#### 1.1 核心框架选型分析
*   **Unity:**
    *   **优势:** 强大的2D/3D渲染引擎，成熟的资源商店 (Asset Store)，强大的动画和物理系统，非常适合重度游戏开发。
    *   **劣势:** 对于以UI为核心、逻辑驱动的益智游戏而言，Unity显得较重，构建包体更大。其UI系统 (UGUI) 在跨分辨率自适应方面不如现代UI框架灵活。
*   **Flutter:**
    *   **优势:** Google出品的现代UI工具包，天生为跨平台而生。拥有极佳的性能（直接编译为ARM/x86代码），富有表现力的UI构建方式，以及极高的开发效率（Hot Reload）。其声明式UI范式非常适合数据驱动的视图更新。生态系统庞大，社区活跃。
    *   **劣势:** 3D支持相对薄弱（但本项目不涉及复杂3D渲染）。
*   **最终推荐: Flutter**
    *   **理由:** “逻辑实验室 (LogicLab)”的核心是2D界面的交互和逻辑判断，而非复杂的图形渲染。Flutter能以一套代码库完美覆盖所有目标平台，提供原生级别的性能和高度一致的UI体验。其开发效率、UI构建的灵活性和更小的包体，使其成为本项目的最优选择。

#### 1.2 编程语言
*   **Dart:** 配合Flutter框架使用。Dart是一门类型安全、面向对象的语言，为客户端应用优化，支持AOT和JIT编译，完美契合Flutter。

#### 1.3 数据库
*   **Hive:** 一个用纯Dart编写的轻量级、快速的键值对数据库。非常适合存储结构化数据，如用户配置、关卡进度、成就状态等。相比`SQLite`，它更简单、更快，且是Dart-native。

#### 1.4 开发环境
*   **IDE:** **Cursor**
*   **版本控制:** **Git** (代码托管在 GitHub)

### 2. 系统架构 (System Architecture)

#### 2.1 整体架构图 (Mermaid.js) **(MVP)**

```mermaid
graph TD
    subgraph UI/View Layer
        A[主界面]
        B[关卡地图]
        C[游戏场景]
        D[家长中心]
    end

    subgraph Business Logic Layer (BLoC)
        E[ProfileBloc]
        G[PuzzleBloc]
        H[ParentDashboardBloc]
    end

    subgraph Service Layer
        I[用户服务 ProfileService]
        P[谜题服务 PuzzleService]
        F[谜题引擎 PuzzleEngine]
        J[资源管理器 AssetManager]
    end

    subgraph Data Layer
        K[Hive Database]
        L[JSON Puzzle Files]
        M[Image/Audio Assets]
    end

    A & B & C & D --> E & G & H
    E --> I
    H --> I
    G --> P
    P --> F
    F --> L
    I --> K
    J --> M
```

#### 2.2 模块化设计
*   **用户模块 (ProfileModule):** **(MVP)** 包含`ProfileService`和`ProfileBloc`。负责创建、读取、更新和删除用户档案。`ProfileService`直接与Hive数据库交互。
*   **谜题引擎 (PuzzleEngine):** **(MVP)** 核心无状态服务。负责从JSON文件加载谜题数据，验证玩家操作的正确性。MVP阶段将实现**图形推理**和**数字逻辑**的校验规则。
*   **谜题服务 (PuzzleService):** **(MVP)** 负责处理与谜题相关的业务逻辑，作为`PuzzleBloc`和`PuzzleEngine`之间的桥梁。
*   **UI/视图模块 (UIViewModule):** **(MVP)** 包含所有Flutter Widgets。严格遵循BLoC模式，UI层只负责渲染状态和发送事件，不包含任何业务逻辑。
*   **资源管理模块 (AssetManager):** **(MVP)** 负责管理应用的核心静态资源（图片、音频、JSON文件）。

#### 2.3 架构原则 (Architectural Principles)
*   **离线优先，云端增强 (Offline-First, Cloud-Enhanced):**
    *   **核心理念 (MVP):** 应用的核心功能被设计为完全可在本地离线运行。用户无需网络连接即可进行游戏、保存进度。
    *   **可选的云功能 (后续版本):** 网络连接被视为一种增强功能。未来可以启用数据云同步和下载新的内容更新。

#### 3.1 性能指标 (Performance Benchmarks) **(MVP)**
*   **启动时间:** 冷启动时间 < 3秒。
*   **帧率 (FPS):** 所有动画和交互界面必须稳定在 60fps，低端设备上不低于 45fps。
*   **内存占用:** 在中端设备（如 Google Pixel 5）上，应用平均内存占用 < 150MB。
*   **CPU 使用率:** 在持续游戏过程中，CPU平均使用率 < 30%。

#### 3.2 关键技术实现细节
*   **音频管理策略 (MVP):**
    *   **库选择:** 使用 `audioplayers` Flutter包。
    *   **资源管理:** 背景音乐(BGM)流式加载，UI音效(SFX)预加载到内存。
    *   **并发控制:** 实现一个音频服务来管理播放队列。
*   **动画性能优化 (MVP):**
    *   **原则:** 仅在必要时重绘屏幕的最小区域。
    *   **方案:** MVP阶段优先使用Flutter内置的`AnimatedBuilder`和`AnimatedWidget`。复杂的庆祝动画可使用简单的粒子效果，`Rive`或`Lottie`的集成可推至后续版本。
*   **响应式UI (MVP):**
    *   我们将使用Flutter的内置机制来确保UI在不同设备上的适应性 (`MediaQuery`, `LayoutBuilder`, `FittedBox`)。
    *   **策略:** 设计将基于逻辑像素，并主要采用弹性布局。
*   **谜题定义与加载机制 (MVP):**
    *   **数据结构 (JSON 范例):** MVP阶段将完全遵循此结构。
    *   **用户进度与成就数据模型 (Hive):** MVP阶段将实现`UserProfile`和`LevelProgress`。`unlockedAchievements`字段可保留，但相关功能在后续版本实现。
    *   **加载流程:** MVP阶段将完全实现此流程。

#### 3.3 状态管理 **(MVP)**
*   **方案推荐: BLoC (Business Logic Component)**
    *   **理由:** 将业务逻辑与UI彻底分离，利于测试、维护和复用。
    *   **应用:**
        *   **全局状态:** `ProfileBloc`管理当前用户信息。
        *   **页面状态:** `PuzzleBloc`管理游戏页面的状态。`ParentDashboardBloc`在MVP阶段只处理时间控制逻辑。

### 4. 开发与部署 (Development & Deployment)

#### 4.1 开发流程 **(MVP)**
*   **需求 -> 设计:** 严格遵循本PRD和TDD文档的MVP范围。
*   **编码 (在 Cursor IDE 中):**
    *   **AI辅助编码:** 充分利用Cursor的AI能力加速开发，如模型生成、单元测试生成、代码重构与解释。

#### 4.2 持续集成/持续部署 (CI/CD) **(MVP)**
*   **工具:** **GitHub Actions**
*   **工作流 (Workflow):**
    1.  **触发:** `push`到`main`分支或创建`pull_request`时触发。
    2.  **作业 (Jobs):**
        *   `analyze_and_test`: 运行 `flutter analyze` 和 `flutter test`。
        *   `build_android`: 构建 `apk` 和 `appbundle`。
        *   `build_ios`: 构建 `ipa`。
        *   `build_desktop`: **(后续版本)** 桌面版构建可延后。
    3.  **部署 (后续版本):** 自动部署到应用商店的流程在MVP阶段后建立。

### 6. 测试策略 (Testing Strategy)
*   **单元测试 (Unit Tests):** **(MVP)** 针对`PuzzleEngine`的核心校验逻辑、`ProfileService`的数据处理进行测试。代码覆盖率目标 > 80%。
*   **组件测试 (Widget Tests):** **(MVP)** 针对核心的、可复用的Widget进行测试。
*   **集成测试 (Integration Tests):** **(MVP)** 测试“创建用户 -> 进入关卡 -> 完成谜题 -> 查看星星”的核心流程。
*   **性能与压力测试:** **(后续版本)** 完整的自动化压力测试将在后续版本进行。MVP阶段以手动测试和性能分析为主。
*   **无障碍测试 (Accessibility Testing):** **(后续版本)**
*   **儿童用户测试 (Child User Testing):** **(后续版本)**
*   **设备兼容性测试矩阵:** **(MVP - 简化版)**
    *   **目标:** 确保在主流设备和操作系统版本上提供一致的优质体验。
    *   **矩阵:**
        | 平台      | 系统版本        | 代表设备                               |
        | --------- | --------------- | -------------------------------------- |
        | iOS       | 16.x, 17.x      | iPhone 14, iPad (9th gen)              |
        | Android   | 12, 13, 14      | Google Pixel 6, Samsung Galaxy S22     |

### 6. 风险评估与缓解策略 (Risk Assessment & Mitigation)

#### 6.1 技术风险
*   **风险点 (MVP):** Flutter在某些特定或老旧的iOS/Android版本上可能存在未知的兼容性或性能瓶颈。
*   **缓解策略 (MVP):** 
    1.  在简化的设备矩阵上手动进行充分测试。
    2.  开发过程中使用 `DevTools` 持续关注性能。

*   **风险点 (MVP):** 动画效果可能导致低端设备性能下降。
*   **缓解策略 (MVP):** 
    1.  提供“性能模式”选项，允许用户减少动画。
    2.  MVP阶段避免使用过于复杂的动画。

#### 6.2 产品风险
*   **风险点 (MVP):** 核心玩法不够吸引人，导致留存率低。
*   **缓解策略 (MVP):** 
    1.  **快速迭代:** MVP的目标就是尽快将产品交到真实用户手中，收集反馈，快速验证核心玩法的吸引力。
    2.  **数据驱动:** 密切关注核心指标（次日留存率、核心关卡完成率），用数据指导后续版本的迭代方向。

*   **风险点 (MVP):** 家长对屏幕时间的普遍担忧可能影响购买决策。
*   **缓解策略 (MVP):** 
    1.  **突出家长控制:** 在营销和应用介绍中，将“游戏时间控制”作为核心卖点之一，缓解家长的焦虑。

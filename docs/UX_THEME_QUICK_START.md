# UX 主题配置快速入门

本指南帮助开发者快速上手 LogicLab 的新 UX 主题配置系统。

## 🚀 快速开始

### 1. 基础使用

```dart
import 'package:logic_lab/core/theme/ux_theme_config.dart';
import 'package:logic_lab/core/constants/app_theme.dart';

// 使用颜色
Container(
  color: UXThemeConfig.primaryBlue,
  child: Text(
    '标题',
    style: TextStyle(
      color: UXThemeConfig.textOnPrimary,
      fontSize: UXThemeConfig.fontSizeHeading,
    ),
  ),
)

// 使用间距
Padding(
  padding: EdgeInsets.all(UXThemeConfig.spacingL),
  child: child,
)

// 使用圆角
Container(
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
  ),
)
```

### 2. 应用主题

在 `main.dart` 中：

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  home: HomePage(),
)
```

### 3. 响应式设计

```dart
Widget build(BuildContext context) {
  return Container(
    padding: EdgeInsets.all(context.responsiveSpacing),
    child: Text(
      '响应式文本',
      style: TextStyle(
        fontSize: context.responsiveFontSize(UXThemeConfig.fontSizeBody),
      ),
    ),
  );
}
```

## 📚 核心概念

### 颜色系统

| 类别 | 用途 | 示例 |
|------|------|------|
| 主色调 | 品牌色彩 | `UXThemeConfig.primaryBlue` |
| 辅助色 | 强调色彩 | `UXThemeConfig.accentGreen` |
| 功能色 | 状态指示 | `UXThemeConfig.successGreen` |
| 中性色 | 背景文本 | `UXThemeConfig.neutralGray500` |

### 间距系统

基于 4px 网格：
- `spacingXS` = 4px
- `spacingS` = 8px  
- `spacingM` = 12px
- `spacingL` = 16px
- `spacingXL` = 20px

### 字体系统

- `fontSizeCaption` = 10px
- `fontSizeSmall` = 12px
- `fontSizeBody` = 14px
- `fontSizeSubtitle` = 16px
- `fontSizeHeading` = 20px

## 🎮 游戏特定功能

### 主题世界

```dart
// 获取森林主题
final forestTheme = AppTheme.getThemeWorldTheme('forest');

// 获取海洋暗色主题
final oceanDarkTheme = AppTheme.getThemeWorldTheme('ocean', isDark: true);
```

### 游戏颜色

```dart
// 游戏状态颜色
Container(color: AppTheme.correctColor)    // 正确 - 绿色
Container(color: AppTheme.incorrectColor)  // 错误 - 红色
Container(color: AppTheme.hintColor)       // 提示 - 紫色
Container(color: AppTheme.starColor)       // 星星 - 金色
```

### 响应式谜题网格

```dart
Container(
  width: context.responsivePuzzleGridSize,
  height: context.responsivePuzzleGridSize,
  child: GridView.builder(
    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: context.deviceType == DeviceType.mobile ? 2 : 3,
      crossAxisSpacing: AppTheme.puzzleGridSpacing,
      mainAxisSpacing: AppTheme.puzzleGridSpacing,
    ),
    itemBuilder: (context, index) => PuzzleCell(),
  ),
)
```

## 📱 响应式断点

| 设备类型 | 屏幕宽度 | 网格列数 | 间距 |
|----------|----------|----------|------|
| Mobile | < 480px | 2 | 16px |
| Tablet | 480-768px | 3 | 20px |
| Desktop | 768-1024px | 4 | 24px |
| Large | > 1024px | 5 | 24px |

## 🎨 常用组件样式

### 按钮

```dart
// 主要按钮
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: UXThemeConfig.primaryBlue,
    foregroundColor: UXThemeConfig.textOnPrimary,
    padding: EdgeInsets.symmetric(
      horizontal: UXThemeConfig.paddingButtonHorizontal,
      vertical: UXThemeConfig.paddingButtonVertical,
    ),
  ),
  child: Text('按钮'),
)

// 游戏按钮（更圆润）
ElevatedButton(
  style: ElevatedButton.styleFrom(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(UXThemeConfig.radiusButton * 3),
    ),
  ),
  child: Text('游戏按钮'),
)
```

### 卡片

```dart
Card(
  elevation: 6,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(UXThemeConfig.radiusCard),
  ),
  child: Padding(
    padding: EdgeInsets.all(UXThemeConfig.spacingL),
    child: content,
  ),
)
```

### 输入框

```dart
TextField(
  decoration: InputDecoration(
    filled: true,
    fillColor: UXThemeConfig.backgroundSecondary,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(UXThemeConfig.radiusInput),
      borderSide: BorderSide(color: UXThemeConfig.borderLight),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(UXThemeConfig.radiusInput),
      borderSide: BorderSide(color: UXThemeConfig.borderFocus, width: 2),
    ),
  ),
)
```

## 🔧 动画配置

```dart
// 基础动画
AnimatedContainer(
  duration: UXThemeConfig.durationNormal,
  curve: UXThemeConfig.curveDefault,
)

// 游戏特定动画
AnimatedContainer(
  duration: GameAnimations.starAnimationDuration,
  curve: GameAnimations.starAnimationCurve,
)
```

## 🎯 最佳实践

### ✅ 推荐做法

```dart
// 使用主题常量
Container(
  padding: EdgeInsets.all(UXThemeConfig.spacingL),
  decoration: BoxDecoration(
    color: UXThemeConfig.primaryBlue,
    borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
  ),
)

// 使用响应式扩展
Widget build(BuildContext context) {
  return Padding(
    padding: EdgeInsets.all(context.responsiveSpacing),
    child: Text(
      '文本',
      style: TextStyle(
        fontSize: context.responsiveFontSize(UXThemeConfig.fontSizeBody),
      ),
    ),
  );
}
```

### ❌ 避免做法

```dart
// 硬编码数值
Container(
  padding: EdgeInsets.all(16.0),
  decoration: BoxDecoration(
    color: Color(0xFF4A90E2),
    borderRadius: BorderRadius.circular(8.0),
  ),
)

// 忽略响应式设计
Text(
  '文本',
  style: TextStyle(fontSize: 14),
)
```

## 🧪 测试主题

运行应用后，访问任何占位页面，点击 "主题演示" 按钮查看：

1. **颜色系统** - 所有颜色的展示
2. **字体系统** - 不同字体大小和粗细
3. **组件样式** - 按钮、输入框等组件
4. **游戏元素** - 星星、状态指示器等
5. **响应式设计** - 设备类型和网格布局
6. **主题世界** - 森林、海洋、太空主题切换
7. **暗色模式** - 亮色/暗色主题切换

## 📖 更多资源

- [详细使用示例](UX_THEME_USAGE_EXAMPLES.md)
- [迁移指南](UX_THEME_MIGRATION_GUIDE.md)
- [设计规范文档](UX_DESIGN_GUIDELINES.md)

## 🆘 常见问题

**Q: 如何添加自定义颜色？**
A: 在 `UXThemeConfig` 中添加新的颜色常量，或创建扩展。

**Q: 如何支持新的设备尺寸？**
A: 修改 `UXThemeConfig.getDeviceType()` 方法中的断点值。

**Q: 如何创建新的主题世界？**
A: 在 `AppTheme.getThemeWorldColors()` 中添加新的世界配置。

**Q: 主题不生效怎么办？**
A: 确保在 `main.dart` 中正确应用了 `AppTheme.lightTheme` 和 `AppTheme.darkTheme`。 
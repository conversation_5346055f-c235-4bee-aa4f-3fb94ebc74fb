## **第一部分：产品需求文档 (PRD)**

**文档版本:** 1.2
**创建日期:** 2025年6月29日
**负责人:** Gemini 产品与开发专家团队

### 1. 概述 (Overview)

#### 1.1 项目愿景
“逻辑实验室 (LogicLab)”致力于成为全球儿童逻辑思维启蒙的首选数字伙伴。我们通过将抽象、枯燥的逻辑谜题转化为生动有趣、充满互动和正向激励的游戏体验，让孩子们在玩乐中主动探索、学习和成长，无缝地培养起受益终生的图形、空间、数字和计算思维能力。

#### 1.2 目标用户画像 (User Persona)

*   **小学生 (核心使用者)**
    *   **年龄:** 6-12岁
    *   **特征:**
        *   **认知:** 对世界充满好奇，喜欢探索和发现规律。
        *   **偏好:** 偏爱鲜艳的色彩、可爱的卡通角色和即时的成就感。
        *   **行为:** 注意力集中时间较短（约10-15分钟），主要通过直观的点击、拖拽、滑动进行交互。对失败有挫败感，需要温和的引导。
    *   **痛点:** 传统书本练习题形式单一，缺乏趣味性，容易产生抵触情绪。

*   **家长/老师 (购买者/监督者)**
    *   **年龄:** 30-45岁
    *   **特征:**
        *   **关注点:** 极为关心孩子的教育成果和身心健康。
        *   **期望:** 软件需具备明确的教育价值，内容安全、健康、无广告。希望能追踪孩子的学习进度，并能有效管理孩子的屏幕使用时间。
    *   **痛点:** 难以找到既能让孩子喜欢、又能真正提升思维能力的优质应用。担心孩子沉迷游戏或接触到不良信息。

#### 1.3 成功指标 (Success Metrics)
*   **第一阶段 (上线后1个月):**
    *   日活跃用户 (DAU) > 1,000
    *   次日留存率 > 30%
    *   核心关卡平均完成率 > 70%
*   **第二阶段 (上线后3个月):**
    *   日活跃用户 (DAU) > 5,000
    *   7日留存率 > 15%
    *   用户在同一类型谜题中的平均首次通关错误率，在一个月内下降10%。
*   **长期目标 (上线后6个月):**
    *   日活跃用户 (DAU) > 10,000
    *   应用商店评分 > 4.6星
    *   家长净推荐值 (NPS) > 50

### 2. 功能需求 (Functional Requirements)

#### 2.1 核心游戏模块 (Core Gameplay)

*   **2.1.1 谜题库 (Puzzle Library)**
    *   **图形推理 (Pattern Recognition):** **(MVP)**
        *   **玩法描述:** 屏幕呈现一个 `3x3` 的网格，其中8格已填有按特定规律（如形状、颜色、数量、旋转递增）排列的图形。下方提供3-4个备选图形。孩子需要观察规律，将正确的图形拖拽到空缺的第9格中。
    *   **空间想象 (Spatial Visualization):** **(后续版本)**
        *   **玩法描述:** 左侧展示一个几何体（如立方体、三棱锥）的2D展开图，右侧展示3-4个不同的3D立体模型。孩子需要通过心智旋转和折叠，点击选择哪个3D模型是由该展开图构成的。
    *   **数字逻辑 (Numeric Logic):** **(MVP)**
        *   **玩法描述:** 简化版“数独”。在一个 `4x4` 的宫格中，部分格子已填有可爱的图标（如苹果、香蕉、草莓、葡萄）。孩子需要将下方备选区的图标拖拽到空格中，确保每一行、每一列、每个 `2x2` 的粗线宫内，四种图标都只出现一次。
    *   **编程启蒙 (Intro to Coding):** **(后续版本)**
        *   **玩法描述:** 一个小动物角色站在迷宫起点，终点有宝物（如坚果）。下方提供有限数量的指令方块（如“向前走”、“向左转”、“向右转”）。孩子需要将指令方块按正确顺序排列在指令序列区，然后点击“运行”，观看小动物根据指令走出迷宫。

*   **2.1.2 交互设计 (Interactive Design)** **(MVP)**
    *   **操作:** 所有谜题均支持直观的拖拽、点击、连线操作。
    *   **即时反馈:**
        *   **拖拽过程:** 当一个元素被拖拽到正确位置上时，目标格子会发光或放大，并伴有“吸附”效果和清脆的音效。
        *   **操作结果:** 正确操作会触发粒子效果（如星星、彩带）和“叮！”的音效；错误操作则有轻微的摇头动画和温和的“嗡”音效，物体会弹回原位。
    *   **完成庆祝:** 成功解开一道谜题后，会有短暂（2-3秒）且华丽的庆祝动画，例如角色跳跃欢呼、屏幕绽放烟花等，并给予“星星”奖励。

*   **2.1.3 智能辅助系统 (Intelligent Assistance)**
    *   **分级提示系统 (Hint System):** **(MVP - 简化版)**
        *   **触发:** 当孩子在同一关卡停留超过30秒或连续出错2次时，提示按钮会发光闪烁。
        *   **一级提示 (规则提醒):** 点击后，以高亮动画和语音提示，重申当前谜题的核心规则。例如，在数独中提示“每一行都不能有重复的水果哦”。
        *   **二级提示 (聚焦线索):** **(后续版本)** 如果孩子仍有困难，再次点击提示会高亮关键区域或排除一个错误选项，缩小解题范围。
        *   **消耗机制:** **(后续版本)** 每次使用二级提示会消耗少量游戏内货币（通过正常游戏获得），鼓励孩子独立思考。
    *   **动态难度自适应 (Dynamic Difficulty Adjustment):** **(后续版本)**
        *   **机制:** 系统会根据孩子在最近10个关卡的表现（通关时间、错误率、提示使用次数）动态调整后续关卡的难度。
        *   **表现优异:** 如果孩子表现出色，系统会适当解锁更高难度的挑战关卡，或在现有谜题中引入更复杂的规律变体。
        *   **遇到瓶颈:** 如果孩子遇到困难，系统会推荐一些同类型但更基础的练习关卡，帮助其巩固核心概念，建立信心。

#### 2.2 辅助系统

*   **2.2.1 用户系统 (User System)** **(MVP)**
    *   **本地多用户:** 无需注册。首次进入时，提示创建用户。用户只需输入昵称、选择一个预设的卡通头像即可。
    *   **进度独立:** 支持在同一台设备上创建最多4个用户档案，每个档案的游戏进度、关卡解锁和成就完全独立。

*   **2.2.2 关卡/地图系统 (Level Map System)** **(MVP)**
    *   **主题世界:** 游戏进程由一个大型卷轴地图串联，分为多个主题区域，如“奇妙森林”、“蔚蓝海洋”、“梦幻太空”。
    *   **线性解锁:** 每个主题包含15-20个关卡节点。玩家每完成一关，地图上会有一条发光的路径连接到下一关，下一关的节点被点亮激活。这种设计为孩子提供了清晰的前进路径和目标感。

*   **2.2.3 激励与成就系统 (Incentive & Achievement System)**
    *   **星星奖励:** **(MVP)** 每关根据表现（如用时、错误次数）可获得1-3颗星星。星星用于解锁新的主题世界或特殊奖励。
    *   **成就勋章墙:** **(后续版本)** 在个人主页设有一个“成就墙”。当完成特定里程碑（如“连续答对10题”、“发现所有森林动物”、“首次使用提示”）时，对应的勋章会被点亮，并有弹窗祝贺。

#### 2.3 家长中心 (Parent Dashboard)

*   **安全入口:** **(MVP)** 从主界面进入家长中心前，需要回答一道两位数的加法题（如 `28 + 17 = ?`），防止儿童误入。
*   **功能:**
    *   **学习报告:** **(后续版本)** 以雷达图形式直观展示孩子在“图形”、“空间”、“数字”、“编程”四个维度的能力表现。可查看总游戏时长和关卡完成详情。
    *   **游戏时间控制:** **(MVP)** 可设置每日游戏时长上限（如30分钟/60分钟）和禁用时段（如晚上9点后）。
    *   **进度管理:** **(后续版本)** 可选择重置指定孩子的游戏进度，方便二孩家庭或重新开始。

### 3. 非功能需求 (Non-Functional Requirements)

#### 3.1 跨平台性 (Cross-Platform) **(MVP)**
核心玩法、UI布局和用户数据必须在 iOS 13+、Android 6.0+、Windows 10+ 和 macOS 11+ 上保持高度一致性和无缝同步。

#### 3.2 性能 (Performance) **(MVP)**
*   **启动时间:** 冷启动时间应小于3秒。
*   **帧率:** 所有动画和交互界面必须达到稳定的 60fps。
*   **兼容性:** 在近5年内发布的中低端设备上（如 iPhone 8, Google Pixel 3a）运行流畅，无明显卡顿或发热。

#### 3.3 用户体验 (UI/UX) **(MVP)**
*   **视觉风格:** 采用高饱和度、明亮、友好的扁平化卡通风格。界面元素（按钮、图标）尺寸大，间距宽，确保儿童易于点击，符合Fitts定律。
*   **音频设计:**
    *   **背景音乐 (BGM):** 分场景使用轻松、欢快的背景音乐，可由用户关闭。
    *   **音效 (SFX):** 所有交互都有对应的功能性音效。
    *   **配音 (VO):** 关键引导（如“我们来帮小松鼠找到正确的图形吧！”）采用标准的、富有亲和力的真人童声配音。

#### 3.4 安全与隐私 (Security & Privacy) **(MVP)**
*   **合规性:** 严格遵守美国的 COPPA、欧洲的 GDPR-K 等全球儿童在线隐私保护法规。
*   **数据策略:** 不收集任何个人可识别信息 (PII)。所有用户数据仅存储在本地设备。
*   **应用环境:** 绝不包含任何形式的广告、内购（IAP）或指向外部网站/社交媒体的链接。

### 4. 商业模式 (Business Model)

#### 4.1 核心推荐：付费下载 (Premium / 一次性买断) **(MVP)**
*   **模式描述:** 用户在 App Store 或 Google Play 等应用商店支付一次性费用来下载和安装“逻辑实验室”。一次购买，永久拥有所有核心功能。
*   **优势:** 建立家长信任，体验简单透明，符合教育产品价值感知，支持家庭共享。
*   **定价建议:** **$6.99 美元** (或等值的区域定价)。

#### 4.2 备选方案：免费试用 + 一次性解锁 (Freemium with a Single Unlock)
*   **模式描述:** 用户可免费下载并体验一个完整主题世界。如需解锁全部内容，需在**家长中心**内进行一次性应用内购买 (IAP)。
*   **优势:** 降低尝试门槛，通过实际体验建立付费意愿，获取更广泛用户反馈。
*   **执行要点:** 必须是“一次性”解锁，付费入口严格限制在家长中心，免费内容需高质量且完整。

#### 4.3 长期盈利与扩展
*   **大型扩展包:** 未来可推出全新的、独立的大型内容扩展包（如新主题世界），作为新的付费内容（例如 $2.99 美元），为忠实用户提供持续价值。
*   **捆绑销售:** 当有扩展包时，可创建“完整版”捆绑包提供折扣。
*   **教育折扣:** 提供批量购买折扣给学校和教育机构。

### 5. 待办与未来规划 **(后续版本)**
*   **多语言支持:** V1.0版本发布时，将内置支持英语、西班牙语和简体中文。V1.1版本将加入德语、法语、日语、韩语。
*   **文化本地化:** 我们将与各语言区的教育顾问合作，审查游戏内的图标、比喻和鼓励语，确保其符合当地文化习惯，避免潜在的文化误解。
*   **无障碍访问 (Accessibility):** V2.0版本将进行全面的无障碍设计，包括为视觉障碍儿童提供高对比度模式和旁白描述（VoiceOver），为听觉障碍儿童提供完整的字幕和视觉提示。

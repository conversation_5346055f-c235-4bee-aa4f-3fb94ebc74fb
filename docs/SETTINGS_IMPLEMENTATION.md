# 设置页面实现文档

## 概述

设置页面为LogicLab应用提供了完整的用户个性化配置功能，包括音频设置、游戏设置、家长控制和其他功能。

## 架构设计

### 1. BLoC状态管理

#### SettingsBloc
- **位置**: `lib/presentation/bloc/settings/`
- **功能**: 管理设置页面的所有状态变化
- **核心方法**:
  - `LoadSettingsEvent`: 加载用户设置
  - `UpdateMusicVolumeEvent/UpdateSfxVolumeEvent`: 更新音量设置
  - `ToggleVibrationEvent/ToggleAnimationsEvent`: 切换开关设置
  - `ChangeLanguageEvent`: 切换语言
  - `UpdateDailyTimeLimitEvent`: 更新时间限制
  - `UpdateDisabledTimeSlotsEvent`: 更新禁用时间段
  - `SaveSettingsEvent`: 保存设置
  - `ResetSettingsEvent`: 重置设置

#### 状态类型
- `SettingsLoaded`: 设置已加载，包含hasUnsavedChanges标志
- `VolumeAdjusting`: 音量调整中的特殊状态
- `LanguageChanging`: 语言切换中的状态
- `SettingsSaving/SettingsSaved`: 保存状态
- `SettingsError`: 错误状态

### 2. 数据模型

#### UserSettingsEntity (Domain层)
```dart
class UserSettingsEntity {
  final double musicVolume;        // 音乐音量 (0.0-1.0)
  final double sfxVolume;          // 音效音量 (0.0-1.0)
  final bool enableVibration;      // 震动反馈
  final bool enableAnimations;     // 动画效果
  final String language;           // 界面语言
  final int dailyTimeLimitMinutes; // 每日时间限制(分钟)
  final List<String> disabledTimeSlots; // 禁用时间段
}
```

#### UserSettings (Data层)
- 继承自HiveObject，支持本地存储
- 提供与Domain层的转换方法

## UI组件

### 1. 主页面 (SettingsPage)
- **位置**: `lib/presentation/pages/settings_page.dart`
- **功能**: 
  - 设置项的展示和交互
  - 状态变化的监听和响应
  - 错误处理和用户反馈
  - 保存/重置功能

### 2. 专用组件

#### VolumeSliderWidget
- **位置**: `lib/presentation/widgets/settings/volume_slider_widget.dart`
- **功能**: 音量调节滑块
- **特性**:
  - 实时音量百分比显示
  - 音量级别可视化指示器
  - 调整时的动画效果
  - 音量描述文字(静音、很低、较低等)

#### LanguageSelectorWidget
- **位置**: `lib/presentation/widgets/settings/language_selector_widget.dart`
- **功能**: 语言选择器
- **支持语言**:
  - 简体中文 (zh_CN)
  - 繁體中文 (zh_TW)
  - English (en_US)
- **特性**: 国旗图标和本地化名称显示

#### TimeLimitSelectorWidget
- **位置**: `lib/presentation/widgets/settings/time_limit_selector_widget.dart`
- **功能**: 每日时间限制选择
- **选项**: 15分钟到3小时，以及无限制
- **特性**: 时间描述和图标展示

#### TimeSlotselectorWidget
- **位置**: `lib/presentation/widgets/settings/time_slots_selector_widget.dart`
- **功能**: 禁用时间段选择
- **特性**:
  - 18个时间段选择(6:00-0:00)
  - 快捷选择(学习时间、睡眠时间)
  - 时间段分类(早晨、上午、下午等)
  - 多选支持

## 功能特性

### 1. 音频设置
- **背景音乐**: 0-100%音量调节，实时预览
- **游戏音效**: 独立音量控制
- **音量可视化**: 5级音量指示器
- **音量描述**: 静音、很低、较低、中等、较高、最高

### 2. 游戏设置
- **震动反馈**: 答题错误时震动提醒
- **动画效果**: 控制游戏中的动画显示
- **界面语言**: 支持中英文切换

### 3. 家长控制
- **每日时间限制**: 15分钟到3小时可选
- **禁用时间段**: 精确到小时的时间控制
- **快捷设置**: 学习时间和睡眠时间快速选择

### 4. 其他功能
- **清除缓存**: 清理临时文件
- **数据导出**: 备份游戏进度(待实现)
- **关于应用**: 版本信息和开发者信息
- **设置重置**: 恢复默认设置
- **帮助说明**: 功能使用指导

## 用户体验

### 1. 状态反馈
- **实时预览**: 音量调整时立即显示效果
- **加载状态**: 保存、重置时的进度指示
- **成功反馈**: 操作完成的确认消息
- **错误处理**: 友好的错误提示和重试机制

### 2. 交互设计
- **未保存提醒**: 有更改时显示保存按钮
- **确认对话框**: 重置等危险操作的二次确认
- **动画效果**: 音量调整时的缩放动画
- **响应式布局**: 适配不同屏幕尺寸

### 3. 无障碍支持
- **语义标签**: 所有控件都有适当的语义描述
- **键盘导航**: 支持Tab键导航
- **高对比度**: 清晰的颜色区分
- **字体缩放**: 支持系统字体大小设置

## 数据持久化

### 1. 本地存储
- 使用Hive数据库存储用户设置
- 设置更改立即保存到本地
- 支持多用户独立设置

### 2. 默认值
```dart
UserSettingsEntity.defaultSettings() {
  musicVolume: 0.7,           // 70%音乐音量
  sfxVolume: 0.8,             // 80%音效音量
  enableVibration: true,      // 启用震动
  enableAnimations: true,     // 启用动画
  language: 'zh_CN',          // 简体中文
  dailyTimeLimitMinutes: 60,  // 1小时限制
  disabledTimeSlots: [],      // 无时间限制
}
```

## 扩展性

### 1. 新增设置项
1. 在`UserSettingsEntity`中添加新字段
2. 更新`UserSettings`数据模型
3. 在`SettingsBloc`中添加对应事件和状态
4. 在UI中添加对应的控件

### 2. 新增语言
1. 在`LanguageSelectorWidget`的`_supportedLanguages`中添加
2. 更新语言显示逻辑
3. 添加对应的本地化资源

### 3. 新增时间段
1. 在`TimeSlotselectorWidget`的`_timeSlots`中添加
2. 更新时间段分类逻辑
3. 调整UI布局适应新的时间段数量

## 测试

### 1. 单元测试
- BLoC状态变化测试
- 数据模型转换测试
- 业务逻辑验证测试

### 2. Widget测试
- UI组件渲染测试
- 用户交互测试
- 状态更新测试

### 3. 集成测试
- 完整设置流程测试
- 数据持久化测试
- 多用户设置隔离测试

## 已知问题

1. **字体设置**: 暂时注释了ComicSans字体，需要添加字体文件
2. **数据导出**: 功能标记为待实现
3. **清除缓存**: 功能需要完善实现
4. **国际化**: 需要完整的i18n支持

## 未来优化

1. **主题设置**: 支持深色模式切换
2. **字体大小**: 支持字体大小调节
3. **备份恢复**: 完整的数据备份和恢复功能
4. **云同步**: 设置的云端同步功能
5. **个性化**: 更多个性化选项(背景、主题色等) 
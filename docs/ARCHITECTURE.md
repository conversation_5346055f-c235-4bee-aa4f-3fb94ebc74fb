# LogicLab 应用架构文档

**文档版本:** 1.0
**创建日期:** 2025年6月29日

## 1. 核心设计思想

本项目的架构核心是**分层架构 (Layered Architecture)**，其灵感来源于**清晰架构 (Clean Architecture)**。设计的首要目标是实现**关注点分离 (Separation of Concerns)**，确保项目在功能扩展和长期维护上的高效性与稳定性。

核心原则是将项目代码划分为独立的、低耦合的层次，每一层都有明确的职责。这使得我们可以独立地修改和测试每一部分，而不会对整个系统造成意想不到的影响。

## 2. 架构分层详解

### 2.1 总体架构图

```mermaid
graph TD
    subgraph "Presentation Layer (UI & State)"
        direction LR
        A[Widgets] --> B[BLoC / Cubit];
    end

    subgraph "Domain Layer (Business Logic)"
        direction LR
        C[Use Cases] --> D[Repositories (Abstract)];
        D --> E[Entities];
    end

    subgraph "Data Layer (Data Sources)"
        direction LR
        F[Repositories (Implementation)] --> G[Services];
        G --> H[Models];
        G --> I[Remote / Local DB];
    end

    B --> C;
    F --> D;

    style A fill:#d4f0fd,stroke:#333,stroke-width:2px
    style B fill:#d4f0fd,stroke:#333,stroke-width:2px
    style C fill:#e1f7d5,stroke:#333,stroke-width:2px
    style D fill:#e1f7d5,stroke:#333,stroke-width:2px
    style E fill:#e1f7d5,stroke:#333,stroke-width:2px
    style F fill:#fff2cc,stroke:#333,stroke-width:2px
    style G fill:#fff2cc,stroke:#333,stroke-width:2px
    style H fill:#fff2cc,stroke:#333,stroke-width:2px
    style I fill:#fff2cc,stroke:#333,stroke-width:2px
```

### 2.2 各层职责

项目的代码主要组织在 `lib` 目录下的几个核心子目录中：

```
lib/
├── core/         # 核心工具与全局定义
├── data/         # 数据实现层 (数据源、模型、仓库实现)
├── domain/       # 领域逻辑层 (业务核心、实体、仓库接口)
├── presentation/ # 表现层 (UI、状态管理)
├── services/     # 核心服务层
└── main.dart     # 应用入口
```

### 2.1 `main.dart` - 应用入口

- **职责**: 作为应用的起点，`main.dart` 的职责非常单一和清晰：
  1.  **初始化**: 确保 Flutter 绑定已准备就绪。
  2.  **服务注册**: 初始化应用所需的核心服务 (如 `UserService`, `PuzzleEngine`)。
  3.  **应用启动**: 运行 `MaterialApp` 主应用。
- **优点**: 保持入口文件的干净，避免混入业务逻辑或UI代码。

### 2.2 `core/` - 核心工具与全局定义

- **职责**: 存放整个应用共享的、与业务逻辑无关的通用代码。
  - `constants/`: 定义全局常量，如 `AppConstants` 中的应用版本、数据库表名等，以及 `AppTheme` 中的颜色、字体、组件样式等。这极大地提高了代码的可读性和可维护性。
  - `utils/` (未来): 可存放通用的工具类，如日期格式化、字符串处理等。
- **优点**: 将全局配置和定义集中管理，确保了应用风格的统一，并避免了“魔法数字”和硬编码字符串。

### 2.3 `services/` - 核心服务层

- **职责**: 封装和管理应用的后台核心服务。这些服务通常以单例模式存在，为整个应用提供关键功能。
  - `PuzzleEngine`: **项目的“大脑”**。它负责从 `assets` 中加载、缓存、验证和提供所有谜题数据。这是一个设计精良的、可扩展的谜题处理中心，将游戏内容逻辑与UI完全解耦。
  - `UserService`: **用户数据专家**。它封装了所有与 `Hive` 数据库的交互，提供了清晰、原子化的用户数据操作接口 (CRUD)，并内置了如用户数量限制、昵称唯一性等业务规则。
- **优点**: 将复杂的核心逻辑封装在独立的服务中，使得上层业务（如BLoC）可以轻松调用，而无需关心其内部实现细节。

### 2.4 `data/` - 数据实现层

- **职责**: 负责应用数据的具体来源和实现。
  - `models/`: 定义了与数据源（JSON, Hive）结构完全匹配的数据模型，如 `Puzzle`, `UserProfile`。这些模型通过 `json_serializable` 和 `hive_generator` 实现了与外部数据格式的自动转换。
  - `repositories/` (未来): 将包含对 `domain` 层仓库接口的具体实现。例如，`UserRepositoryImpl` 会在这里实现，它内部会调用 `UserService` 来完成数据的持久化。
- **优点**: 将数据来源的细节（如数据库类型、API细节）与应用的其余部分隔离开来。

### 2.5 `domain/` - 领域逻辑层 (架构核心)

- **职责**: 定义应用的核心业务逻辑和规则，是整个架构中最稳定、最独立的一层。
  - `entities/` (未来): 定义纯粹的业务实体。这些实体只包含业务属性和方法，不依赖任何外部框架或数据源。
  - `repositories/` (未来): 定义抽象的仓库接口，如 `UserRepository`。它规定了数据层必须实现哪些功能（如 `getCurrentUser()`），但不关心这些功能是如何实现的。
  - `usecases/` (未来): 封装单个的业务用例，例如 `GetUserProfileUseCase`。
- **优点**: `domain` 层是应用的“心脏”，它不依赖于任何其他层，这使得核心业务逻辑可以被独立测试和复用。

### 2.6 `presentation/` - 表现层

- **职责**: 负责向用户展示信息（UI）和处理用户输入。
  - `pages/` 或 `screens/`: 存放应用的各个页面或屏幕，如 `SplashPage`, `HomePage`。
  - `widgets/`: 存放可复用的UI组件。
  - `bloc/` 或 `cubit/`: 存放状态管理逻辑。BLoC/Cubit 会通过调用 `domain` 层的仓库接口或用例来获取数据，并根据结果更新UI状态。
- **优点**: UI和状态管理逻辑与业务核心逻辑分离，使得UI的修改不会影响到业务规则。

## 3. 核心开发流程：添加新游戏内容

本架构的核心优势在于其**对内容扩展的友好性**。添加一个新的游戏类型（例如“空间想象”）的流程如下：

1.  **定义内容 (Data)**: 在 `assets/puzzles/` 目录下创建一个新的 `.json` 文件，定义新谜题的数据。
2.  **扩展模型 (Data)**: 在 `puzzle.dart` 中为新谜题类型创建一个新的 `Data` 模型类（如 `SpatialVisualizationData`）。
3.  **扩展逻辑 (Services)**: 在 `PuzzleEngine` 中添加一个对应的 `validate...` 方法来处理新谜题的验证逻辑。
4.  **构建界面 (Presentation)**: 创建一个新的 Widget 来渲染新谜题的UI，并在主游戏页面中根据 `puzzleType` 来动态加载这个Widget。

这个流程确保了新功能的添加是**增量式**的，对现有代码的侵入性极小，从而保证了项目的稳定性和可维护性。

## 4. 架构的未来演进方向

虽然当前架构已非常坚实，但随着项目的发展，可以从以下几个方面进行深化：

1.  **完善领域层**: 明确区分 `data/models` 和 `domain/entities`，让领域层更加纯粹。
2.  **全面应用依赖注入**: 利用 `GetIt` 来管理所有服务和仓库的依赖关系，而不是手动创建单例，以方便测试和管理。
3.  **引入用例 (Use Cases)**: 对于复杂的业务场景，可以引入用例类来进一步细化业务逻辑，使BLoC的职责更单一。

总而言之，当前架构为一个可扩展、可维护、易于测试的高质量应用打下了坚实的基础。

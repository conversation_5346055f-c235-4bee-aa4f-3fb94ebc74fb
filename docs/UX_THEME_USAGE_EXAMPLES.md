# UX 主题配置使用示例

本文档展示了如何在 LogicLab 项目中使用 `UXThemeConfig` 来确保设计的一致性。

## 1. 基础使用

### 1.1 在 MaterialApp 中应用主题

```dart
import 'package:flutter/material.dart';
import 'package:logic_lab/core/theme/ux_theme_config.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'LogicLab',
      theme: UXThemeConfig.buildLightTheme(),
      darkTheme: UXThemeConfig.buildDarkTheme(),
      themeMode: ThemeMode.system,
      home: HomePage(),
    );
  }
}
```

### 1.2 使用颜色常量

```dart
// 使用主色调
Container(
  color: UXThemeConfig.primaryBlue,
  child: Text(
    '主要内容',
    style: TextStyle(color: UXThemeConfig.textOnPrimary),
  ),
)

// 使用功能色彩
Container(
  decoration: BoxDecoration(
    color: UXThemeConfig.successGreen,
    border: Border.all(color: UXThemeConfig.borderSuccess),
  ),
  child: Text('成功消息'),
)

// 使用中性色系
Card(
  color: UXThemeConfig.backgroundSecondary,
  child: Padding(
    padding: EdgeInsets.all(UXThemeConfig.spacingL),
    child: Text(
      '卡片内容',
      style: TextStyle(color: UXThemeConfig.textSecondary),
    ),
  ),
)
```

### 1.3 使用间距系统

```dart
// 页面布局
Padding(
  padding: EdgeInsets.symmetric(
    horizontal: UXThemeConfig.paddingPageHorizontal,
    vertical: UXThemeConfig.paddingPageVertical,
  ),
  child: Column(
    children: [
      // 标题
      Text('页面标题'),
      
      SizedBox(height: UXThemeConfig.spacingXL), // 大间距
      
      // 内容区域
      Container(
        padding: EdgeInsets.all(UXThemeConfig.spacingL),
        child: Text('内容'),
      ),
      
      SizedBox(height: UXThemeConfig.spacingM), // 中等间距
      
      // 按钮
      ElevatedButton(
        onPressed: () {},
        child: Text('按钮'),
      ),
    ],
  ),
)
```

## 2. 组件样式应用

### 2.1 自定义按钮

```dart
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: UXThemeConfig.buttonHeightMedium,
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusButton),
        boxShadow: UXThemeConfig.shadowButton,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusButton),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: UXThemeConfig.paddingButtonHorizontal,
              vertical: UXThemeConfig.paddingButtonVertical,
            ),
            child: Center(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: UXThemeConfig.fontSizeBody,
                  fontWeight: UXThemeConfig.fontWeightMedium,
                  color: _getTextColor(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case ButtonType.primary:
        return UXThemeConfig.primaryBlue;
      case ButtonType.success:
        return UXThemeConfig.successGreen;
      case ButtonType.warning:
        return UXThemeConfig.warningAmber;
      case ButtonType.error:
        return UXThemeConfig.errorRed;
    }
  }

  Color _getTextColor() {
    return UXThemeConfig.textOnPrimary;
  }
}

enum ButtonType { primary, success, warning, error }
```

### 2.2 自定义卡片

```dart
class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? margin;
  final EdgeInsets? padding;

  const CustomCard({
    Key? key,
    required this.child,
    this.margin,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.all(UXThemeConfig.spacingS),
      decoration: BoxDecoration(
        color: UXThemeConfig.backgroundPrimary,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusCard),
        boxShadow: UXThemeConfig.shadowCard,
      ),
      child: Padding(
        padding: padding ?? EdgeInsets.symmetric(
          horizontal: UXThemeConfig.paddingCardHorizontal,
          vertical: UXThemeConfig.paddingCardVertical,
        ),
        child: child,
      ),
    );
  }
}
```

### 2.3 自定义输入框

```dart
class CustomTextField extends StatelessWidget {
  final String? labelText;
  final String? hintText;
  final TextEditingController? controller;
  final bool isError;

  const CustomTextField({
    Key? key,
    this.labelText,
    this.hintText,
    this.controller,
    this.isError = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: UXThemeConfig.inputHeightMedium,
      decoration: BoxDecoration(
        color: UXThemeConfig.backgroundSecondary,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusInput),
        border: Border.all(
          color: isError 
            ? UXThemeConfig.borderError 
            : UXThemeConfig.borderLight,
          width: 1.0,
        ),
      ),
      child: TextField(
        controller: controller,
        style: TextStyle(
          fontSize: UXThemeConfig.fontSizeBody,
          color: UXThemeConfig.textPrimary,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: UXThemeConfig.paddingInputHorizontal,
            vertical: UXThemeConfig.paddingInputVertical,
          ),
          labelStyle: TextStyle(
            fontSize: UXThemeConfig.fontSizeBody,
            color: UXThemeConfig.textSecondary,
          ),
          hintStyle: TextStyle(
            fontSize: UXThemeConfig.fontSizeBody,
            color: UXThemeConfig.textTertiary,
          ),
        ),
      ),
    );
  }
}
```

## 3. 响应式设计

### 3.1 使用扩展方法

```dart
class ResponsivePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(context.responsiveSpacing),
        child: Column(
          children: [
            Text(
              '响应式标题',
              style: TextStyle(
                fontSize: context.responsiveFontSize(UXThemeConfig.fontSizeHeading),
                fontWeight: UXThemeConfig.fontWeightBold,
              ),
            ),
            
            SizedBox(height: context.responsiveSpacing),
            
            _buildContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return _buildMobileLayout();
      case DeviceType.tablet:
        return _buildTabletLayout();
      case DeviceType.desktop:
      case DeviceType.large:
        return _buildDesktopLayout();
    }
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        // 移动端布局
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        // 平板布局
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Container(
      constraints: BoxConstraints(
        maxWidth: UXThemeConfig.maxWidthDesktop,
      ),
      child: Row(
        children: [
          // 桌面布局
        ],
      ),
    );
  }
}
```

### 3.2 响应式网格

```dart
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;

  const ResponsiveGrid({Key? key, required this.children}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    final crossAxisCount = _getCrossAxisCount(deviceType);
    final spacing = context.responsiveSpacing;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }

  int _getCrossAxisCount(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 2;
      case DeviceType.tablet:
        return 3;
      case DeviceType.desktop:
        return 4;
      case DeviceType.large:
        return 5;
    }
  }
}
```

## 4. 动画和转场

### 4.1 页面转场动画

```dart
class CustomPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;

  CustomPageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: UXThemeConfig.transitionDuration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: UXThemeConfig.transitionCurve,
              )),
              child: child,
            );
          },
        );
}

// 使用示例
Navigator.of(context).push(
  CustomPageRoute(child: NextPage()),
);
```

### 4.2 组件动画

```dart
class AnimatedCard extends StatefulWidget {
  final Widget child;

  const AnimatedCard({Key? key, required this.child}) : super(key: key);

  @override
  _AnimatedCardState createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<AnimatedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: UXThemeConfig.durationNormal,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: UXThemeConfig.curveEaseOut,
    ));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: CustomCard(child: widget.child),
        );
      },
    );
  }
}
```

## 5. 主题切换

### 5.1 主题管理器

```dart
class ThemeManager extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;

  ThemeMode get themeMode => _themeMode;

  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    notifyListeners();
  }

  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    notifyListeners();
  }
}

// 在 main.dart 中使用
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        return MaterialApp(
          theme: UXThemeConfig.buildLightTheme(),
          darkTheme: UXThemeConfig.buildDarkTheme(),
          themeMode: themeManager.themeMode,
          home: HomePage(),
        );
      },
    );
  }
}
```

### 5.2 主题切换按钮

```dart
class ThemeToggleButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final themeManager = Provider.of<ThemeManager>(context);
    final isDark = themeManager.themeMode == ThemeMode.dark;

    return IconButton(
      icon: AnimatedSwitcher(
        duration: UXThemeConfig.durationFast,
        child: Icon(
          isDark ? Icons.light_mode : Icons.dark_mode,
          key: ValueKey(isDark),
          color: UXThemeConfig.primaryBlue,
        ),
      ),
      onPressed: themeManager.toggleTheme,
    );
  }
}
```

## 6. 最佳实践

### 6.1 命名约定

```dart
// ✅ 推荐：使用 UXThemeConfig 常量
Container(
  padding: EdgeInsets.all(UXThemeConfig.spacingL),
  decoration: BoxDecoration(
    color: UXThemeConfig.primaryBlue,
    borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
  ),
)

// ❌ 不推荐：硬编码数值
Container(
  padding: EdgeInsets.all(16.0),
  decoration: BoxDecoration(
    color: Color(0xFF4A90E2),
    borderRadius: BorderRadius.circular(8.0),
  ),
)
```

### 6.2 扩展主题

```dart
// 为特定组件扩展主题
extension PuzzleTheme on UXThemeConfig {
  static const Color puzzleCorrect = successGreen;
  static const Color puzzleIncorrect = errorRed;
  static const Color puzzleHint = accentOrange;
  
  static const double puzzleCardSize = 120.0;
  static const double puzzleIconSize = iconSizeXL;
}

// 使用扩展
Container(
  width: PuzzleTheme.puzzleCardSize,
  height: PuzzleTheme.puzzleCardSize,
  decoration: BoxDecoration(
    color: PuzzleTheme.puzzleCorrect,
    borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
  ),
  child: Icon(
    Icons.check,
    size: PuzzleTheme.puzzleIconSize,
    color: UXThemeConfig.textOnAccent,
  ),
)
```

### 6.3 组件库构建

```dart
// 创建统一的组件库
class LogicLabComponents {
  // 主要按钮
  static Widget primaryButton({
    required String text,
    required VoidCallback onPressed,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      type: ButtonType.primary,
    );
  }

  // 卡片容器
  static Widget card({required Widget child}) {
    return CustomCard(child: child);
  }

  // 输入框
  static Widget textField({
    String? labelText,
    String? hintText,
    TextEditingController? controller,
  }) {
    return CustomTextField(
      labelText: labelText,
      hintText: hintText,
      controller: controller,
    );
  }
}
```

## 7. 调试和验证

### 7.1 主题预览工具

```dart
class ThemePreview extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('主题预览')),
      body: ListView(
        padding: EdgeInsets.all(UXThemeConfig.spacingL),
        children: [
          _buildColorSection(),
          _buildTypographySection(),
          _buildComponentSection(),
          _buildSpacingSection(),
        ],
      ),
    );
  }

  Widget _buildColorSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('颜色系统', style: TextStyle(fontSize: UXThemeConfig.fontSizeHeading)),
        SizedBox(height: UXThemeConfig.spacingM),
        Wrap(
          spacing: UXThemeConfig.spacingS,
          runSpacing: UXThemeConfig.spacingS,
          children: [
            _buildColorTile('主色调', UXThemeConfig.primaryBlue),
            _buildColorTile('成功', UXThemeConfig.successGreen),
            _buildColorTile('警告', UXThemeConfig.warningAmber),
            _buildColorTile('错误', UXThemeConfig.errorRed),
          ],
        ),
      ],
    );
  }

  Widget _buildColorTile(String name, Color color) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
      ),
      child: Center(
        child: Text(
          name,
          style: TextStyle(
            color: UXThemeConfig.textOnPrimary,
            fontSize: UXThemeConfig.fontSizeSmall,
          ),
        ),
      ),
    );
  }

  // 其他预览方法...
}
```

这个完整的 UX 主题配置文件提供了：

1. **完整的设计系统**：颜色、字体、间距、圆角、阴影等
2. **响应式支持**：断点系统和设备类型判断
3. **主题构建器**：自动生成亮色和暗色主题
4. **便捷扩展**：BuildContext 和 ThemeData 扩展方法
5. **工具方法**：响应式计算和设备类型判断

使用这个配置文件，整个 LogicLab 项目可以保持视觉一致性，同时支持灵活的主题定制和响应式设计。 
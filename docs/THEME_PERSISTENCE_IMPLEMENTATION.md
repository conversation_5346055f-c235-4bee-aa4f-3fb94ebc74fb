# 主题持久化实现文档

## 概述

本文档描述了LogicLab应用中主题持久化功能的实现，包括主题模式保存、加载、动画过渡和无障碍支持。

**更新时间：** 2024-12-19 UTC

## 功能特性

### 1. 主题持久化
- ✅ 主题模式持久化（亮色/暗色/自动）
- ✅ 主题世界持久化（经典/森林/海洋/太空）
- ✅ 高对比度模式持久化
- ✅ 动画过渡设置持久化

### 2. 动画过渡
- ✅ 平滑的主题切换动画（300ms）
- ✅ 可配置的动画开关
- ✅ 自适应动画曲线（easeInOut）

### 3. 无障碍支持
- ✅ 高对比度模式
- ✅ 语义化UI组件
- ✅ 动画减少选项

### 4. 性能优化
- ✅ 主题缓存机制
- ✅ 延迟加载
- ✅ 内存优化

## 架构设计

### 核心组件

```
ThemeService (主题服务)
├── SharedPreferences (持久化存储)
├── ThemeCache (主题缓存)
├── ChangeNotifier (状态通知)
└── ThemeBuilder (主题构建)

ThemeSettingsWidget (主题设置UI)
├── ThemeModeSelector (模式选择器)
├── ThemeWorldSelector (世界选择器)
├── AccessibilityOptions (无障碍选项)
└── AnimationOptions (动画选项)
```

### 数据流

```
用户操作 → ThemeSettingsWidget → SettingsBloc → ThemeService
                                                      ↓
SharedPreferences ← ThemeCache ← ThemeBuilder ← ChangeNotifier
```

## 实现细节

### 1. ThemeService 核心功能

```dart
class ThemeService extends ChangeNotifier {
  // 持久化键名
  static const String _themePrefsKey = 'app_theme_mode';
  static const String _themeWorldPrefsKey = 'app_theme_world';
  static const String _highContrastPrefsKey = 'app_high_contrast';
  static const String _animatedTransitionsPrefsKey = 'app_animated_transitions';
  
  // 主题状态
  ThemeMode _themeMode = ThemeMode.system;
  String _themeWorld = 'default';
  bool _highContrastMode = false;
  bool _animatedTransitions = true;
  
  // 主题缓存
  final Map<String, ThemeData> _lightThemeCache = {};
  final Map<String, ThemeData> _darkThemeCache = {};
}
```

### 2. 主题持久化机制

#### 保存设置
```dart
Future<void> setThemeMode(ThemeMode mode) async {
  if (_themeMode == mode) return;
  
  _themeMode = mode;
  await _saveThemeMode();
  notifyListeners();
}

Future<void> _saveThemeMode() async {
  if (_prefs == null) return;
  
  try {
    await _prefs!.setInt(_themePrefsKey, _themeMode.index);
  } catch (e) {
    _logger.w('Failed to save theme mode: $e');
  }
}
```

#### 加载设置
```dart
Future<void> _loadThemePreferences() async {
  if (_prefs == null) return;
  
  // 加载主题模式
  final themeModeIndex = _prefs!.getInt(_themePrefsKey);
  if (themeModeIndex != null && themeModeIndex < ThemeMode.values.length) {
    _themeMode = ThemeMode.values[themeModeIndex];
  }
  
  // 加载其他设置...
}
```

### 3. 主题缓存优化

```dart
ThemeData getLightTheme() {
  final cacheKey = '${_themeWorld}_${_highContrastMode ? 'hc' : 'normal'}';
  
  if (_lightThemeCache.containsKey(cacheKey)) {
    return _lightThemeCache[cacheKey]!;
  }
  
  ThemeData theme = _buildTheme(false);
  _lightThemeCache[cacheKey] = theme;
  return theme;
}
```

### 4. 高对比度支持

```dart
ThemeData _applyHighContrastMode(ThemeData theme, bool isDark) {
  if (!_highContrastMode) return theme;
  
  final backgroundColor = isDark ? Colors.black : Colors.white;
  final foregroundColor = isDark ? Colors.white : Colors.black;
  
  return theme.copyWith(
    scaffoldBackgroundColor: backgroundColor,
    colorScheme: theme.colorScheme.copyWith(
      primary: foregroundColor,
      onPrimary: backgroundColor,
      // 更多高对比度设置...
    ),
  );
}
```

## UI组件

### 1. ThemeSettingsWidget

主题设置的主要UI组件，包含：

- **主题模式选择器：** 浅色/深色/自动三个选项
- **主题世界选择器：** 经典/森林/海洋/太空主题
- **无障碍选项：** 高对比度模式开关
- **动画选项：** 主题切换动画开关
- **操作按钮：** 重置主题、预览主题

### 2. 主题模式卡片

```dart
class _ThemeModeCard extends StatelessWidget {
  // 显示主题模式图标和名称
  // 支持选中状态的视觉反馈
  // 包含点击动画效果
}
```

### 3. 主题世界卡片

```dart
class _ThemeWorldCard extends StatelessWidget {
  // 显示主题世界的emoji和名称
  // 水平滚动布局支持
  // 动态选中状态
}
```

## 集成方式

### 1. 服务注册

```dart
// lib/core/service_locator.dart
sl.registerLazySingleton<ThemeService>(() => ThemeService());
```

### 2. 主应用集成

```dart
// lib/main.dart
class _LogicLabAppState extends State<LogicLabApp> {
  late ThemeService _themeService;

  @override
  void initState() {
    super.initState();
    _themeService = sl<ThemeService>();
    _themeService.addListener(_onThemeChanged);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _themeService,
      builder: (context, child) {
        return MaterialApp(
          theme: _themeService.getLightTheme(),
          darkTheme: _themeService.getDarkTheme(),
          themeMode: _themeService.themeMode,
          themeAnimationDuration: _themeService.animatedTransitions
              ? const Duration(milliseconds: 300)
              : Duration.zero,
        );
      },
    );
  }
}
```

### 3. 设置页面集成

```dart
// lib/presentation/pages/settings_page.dart
Widget _buildSettingsView(BuildContext context, SettingsState state) {
  return Column(
    children: [
      // 主题设置
      const ThemeSettingsWidget(),
      
      // 其他设置...
    ],
  );
}
```

## 测试验证

### 1. 功能测试

- [ ] 主题模式切换并持久化
- [ ] 主题世界切换并持久化
- [ ] 高对比度模式切换并持久化
- [ ] 动画设置切换并持久化
- [ ] 应用重启后设置保持

### 2. 性能测试

- [ ] 主题缓存有效性
- [ ] 内存使用优化
- [ ] 动画流畅度
- [ ] 响应速度

### 3. 无障碍测试

- [ ] 高对比度模式可用性
- [ ] 语义化标签正确性
- [ ] 键盘导航支持
- [ ] 屏幕阅读器兼容性

## 配置选项

### SharedPreferences 键名

| 键名 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `app_theme_mode` | int | 0 (system) | 主题模式索引 |
| `app_theme_world` | string | 'default' | 主题世界ID |
| `app_high_contrast` | bool | false | 高对比度模式 |
| `app_animated_transitions` | bool | true | 动画过渡开关 |

### 主题世界映射

| 世界ID | 显示名称 | Emoji | 主色调 |
|--------|----------|-------|--------|
| default | 经典 | 🎨 | 蓝色 |
| forest | 奇妙森林 | 🌲 | 绿色 |
| ocean | 蔚蓝海洋 | 🌊 | 青色 |
| space | 梦幻太空 | 🚀 | 紫色 |

## 故障排除

### 常见问题

1. **主题设置不保存**
   - 检查SharedPreferences权限
   - 验证服务初始化顺序
   - 查看日志错误信息

2. **动画效果异常**
   - 确认动画开关状态
   - 检查AnimationController生命周期
   - 验证主题切换时机

3. **高对比度模式不生效**
   - 确认缓存清除机制
   - 检查主题构建逻辑
   - 验证颜色映射正确性

### 调试工具

```dart
// 获取主题统计信息
Map<String, dynamic> stats = themeService.getThemeStats();
print('Theme Stats: $stats');

// 清除主题缓存
themeService._clearThemeCache();

// 重置所有设置
await themeService.resetToDefaults();
```

## 性能指标

- **主题切换响应时间：** < 100ms
- **缓存命中率：** > 95%
- **内存占用：** < 2MB
- **动画帧率：** 60fps

## 后续优化

### 计划功能

1. **自定义主题色彩**
   - 用户自定义主色调
   - 色彩搭配建议
   - 预设色彩方案

2. **主题同步**
   - 云端主题同步
   - 跨设备一致性
   - 离线缓存机制

3. **智能主题**
   - 基于时间自动切换
   - 环境光感应适配
   - 用户习惯学习

### 技术优化

1. **性能提升**
   - 主题预加载
   - 异步主题构建
   - 更精细的缓存策略

2. **用户体验**
   - 更丰富的动画效果
   - 个性化推荐
   - 主题预览功能

---

**文档维护者：** LogicLab开发团队  
**最后更新：** 2024-12-19 UTC  
**版本：** v1.0.0 
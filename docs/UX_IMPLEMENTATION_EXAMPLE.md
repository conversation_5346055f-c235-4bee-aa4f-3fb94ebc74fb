# UX规范应用示例

## 📋 概述

本文档展示如何在LogicLab项目中应用新制定的UX设计规范，通过具体的代码示例说明主题配置的使用方法。

---

## 🎨 主题应用示例

### 1. 在main.dart中应用主题

```dart
import 'package:flutter/material.dart';
import 'core/constants/ux_theme_config.dart';

class LogicLabApp extends StatelessWidget {
  const LogicLabApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'LogicLab',
      // 应用UX规范主题
      theme: LogicLabTheme.lightTheme,
      home: const SplashPage(),
    );
  }
}
```

### 2. 使用标准化颜色

```dart
// ❌ 旧的硬编码颜色
Container(
  color: Color(0xFF6B73FF),
  child: Text('开始游戏'),
)

// ✅ 使用UX规范颜色
Container(
  color: LogicLabTheme.AppColors.primary,
  child: Text('开始游戏'),
)

// ✅ 根据游戏类型使用主题色
Container(
  color: LogicLabTheme.AppColors.getGameTypeColor('GRAPHIC_PATTERN_3X3'),
  child: Text('图形推理'),
)
```

### 3. 标准化字体大小

```dart
// ❌ 旧的硬编码字体
Text(
  '欢迎来到LogicLab',
  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
)

// ✅ 使用UX规范字体
Text(
  '欢迎来到LogicLab',
  style: TextStyle(
    fontSize: LogicLabTheme.FontSizes.h2,
    fontWeight: FontWeight.bold,
    color: LogicLabTheme.AppColors.onBackground,
  ),
)

// ✅ 使用主题文字样式
Text(
  '欢迎来到LogicLab',
  style: Theme.of(context).textTheme.headlineMedium,
)
```

### 4. 标准化间距和圆角

```dart
// ❌ 旧的硬编码间距
Padding(
  padding: EdgeInsets.all(16.0),
  child: Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(12.0),
    ),
  ),
)

// ✅ 使用UX规范间距
Padding(
  padding: EdgeInsets.all(LogicLabTheme.Spacing.md),
  child: Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(LogicLabTheme.BorderRadius.lg),
    ),
  ),
)
```

---

## 🎮 游戏组件应用示例

### 1. 游戏卡片组件

```dart
class GameCard extends StatelessWidget {
  final String gameType;
  final String title;
  final VoidCallback onTap;

  const GameCard({
    super.key,
    required this.gameType,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      // 使用标准游戏卡片装饰
      decoration: LogicLabTheme.gameCardDecoration(
        color: LogicLabTheme.AppColors.getGameTypeColor(gameType),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // 触觉反馈
            LogicLabTheme.HapticFeedback.light();
            onTap();
          },
          borderRadius: BorderRadius.circular(LogicLabTheme.BorderRadius.gameCard),
          child: Padding(
            padding: EdgeInsets.all(LogicLabTheme.Spacing.lg),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 游戏图标
                Icon(
                  _getGameIcon(gameType),
                  size: 48,
                  color: LogicLabTheme.AppColors.onPrimary,
                ),
                SizedBox(height: LogicLabTheme.Spacing.md),
                // 游戏标题
                Text(
                  title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: LogicLabTheme.AppColors.onPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getGameIcon(String gameType) {
    switch (gameType) {
      case 'GRAPHIC_PATTERN_3X3':
        return Icons.grid_3x3;
      case 'SPATIAL_VISUALIZATION':
        return Icons.view_in_ar;
      case 'NUMERIC_LOGIC':
        return Icons.calculate;
      case 'INTRO_TO_CODING':
        return Icons.code;
      default:
        return Icons.games;
    }
  }
}
```

### 2. 标准按钮组件

```dart
class LogicLabButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isSecondary;

  const LogicLabButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isSecondary = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isSecondary) {
      return TextButton(
        onPressed: onPressed,
        child: Text(text),
      );
    }

    return ElevatedButton(
      onPressed: onPressed,
      child: Text(text),
    );
  }
}

// 使用示例
LogicLabButton(
  text: '开始游戏',
  onPressed: () {
    // 处理点击事件
  },
)
```

### 3. 动画按钮组件

```dart
class AnimatedGameButton extends StatefulWidget {
  final String text;
  final VoidCallback onTap;
  final Color? backgroundColor;

  const AnimatedGameButton({
    super.key,
    required this.text,
    required this.onTap,
    this.backgroundColor,
  });

  @override
  State<AnimatedGameButton> createState() => _AnimatedGameButtonState();
}

class _AnimatedGameButtonState extends State<AnimatedGameButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: LogicLabTheme.AnimationDurations.buttonPress,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        _controller.forward();
        LogicLabTheme.HapticFeedback.light();
      },
      onTapUp: (_) {
        _controller.reverse();
        widget.onTap();
      },
      onTapCancel: () {
        _controller.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: LogicLabTheme.Spacing.lg,
                vertical: LogicLabTheme.Spacing.md,
              ),
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? LogicLabTheme.AppColors.primary,
                borderRadius: BorderRadius.circular(LogicLabTheme.BorderRadius.button),
                boxShadow: LogicLabTheme.Shadows.button,
              ),
              child: Text(
                widget.text,
                style: Theme.of(context).textTheme.labelLarge,
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      ),
    );
  }
}
```

---

## 📱 页面布局应用示例

### 1. 标准页面布局

```dart
class StandardPage extends StatelessWidget {
  final String title;
  final Widget child;
  final bool showBackButton;

  const StandardPage({
    super.key,
    required this.title,
    required this.child,
    this.showBackButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 使用渐变背景
      body: Container(
        decoration: const BoxDecoration(
          gradient: LogicLabTheme.AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 标准应用栏
              _buildAppBar(context),
              // 页面内容
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: LogicLabTheme.Spacing.pageMargin,
                  ),
                  child: child,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(LogicLabTheme.Spacing.md),
      child: Row(
        children: [
          if (showBackButton)
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back),
            ),
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.headlineMedium,
              textAlign: TextAlign.center,
            ),
          ),
          if (showBackButton)
            SizedBox(width: 48), // 占位符保持标题居中
        ],
      ),
    );
  }
}
```

### 2. 响应式网格布局

```dart
class ResponsiveGameGrid extends StatelessWidget {
  final List<GameData> games;

  const ResponsiveGameGrid({
    super.key,
    required this.games,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据屏幕宽度确定列数
        int crossAxisCount = 2;
        if (constraints.maxWidth > 600) {
          crossAxisCount = 3;
        }
        if (constraints.maxWidth > 900) {
          crossAxisCount = 4;
        }

        return GridView.builder(
          padding: EdgeInsets.all(LogicLabTheme.Spacing.md),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: LogicLabTheme.Spacing.md,
            mainAxisSpacing: LogicLabTheme.Spacing.md,
            childAspectRatio: 1.2,
          ),
          itemCount: games.length,
          itemBuilder: (context, index) {
            return GameCard(
              gameType: games[index].type,
              title: games[index].title,
              onTap: () => _onGameTap(games[index]),
            );
          },
        );
      },
    );
  }

  void _onGameTap(GameData game) {
    // 处理游戏点击
  }
}
```

---

## 🎯 状态反馈应用示例

### 1. 成功状态显示

```dart
class SuccessMessage extends StatelessWidget {
  final String message;

  const SuccessMessage({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(LogicLabTheme.Spacing.md),
      decoration: BoxDecoration(
        color: LogicLabTheme.successColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(LogicLabTheme.BorderRadius.md),
        border: Border.all(
          color: LogicLabTheme.successColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: LogicLabTheme.successColor,
            size: 24,
          ),
          SizedBox(width: LogicLabTheme.Spacing.sm),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: LogicLabTheme.successColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
```

### 2. 错误状态显示

```dart
class ErrorMessage extends StatelessWidget {
  final String message;

  const ErrorMessage({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(LogicLabTheme.Spacing.md),
      decoration: BoxDecoration(
        color: LogicLabTheme.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(LogicLabTheme.BorderRadius.md),
        border: Border.all(
          color: LogicLabTheme.errorColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error,
            color: LogicLabTheme.errorColor,
            size: 24,
          ),
          SizedBox(width: LogicLabTheme.Spacing.sm),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: LogicLabTheme.errorColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
```

---

## 🔄 迁移指南

### 1. 现有代码迁移步骤

1. **导入主题配置**
```dart
import 'package:logic_lab/core/constants/ux_theme_config.dart';
```

2. **替换硬编码颜色**
```dart
// 查找并替换所有硬编码的颜色值
// 使用 LogicLabTheme.AppColors 中的标准颜色
```

3. **标准化字体大小**
```dart
// 替换所有硬编码的字体大小
// 使用 LogicLabTheme.FontSizes 中的标准尺寸
```

4. **统一间距和圆角**
```dart
// 替换所有硬编码的间距和圆角值
// 使用 LogicLabTheme.Spacing 和 LogicLabTheme.BorderRadius
```

### 2. 迁移检查清单

- [ ] 所有页面应用渐变背景
- [ ] 所有按钮使用标准样式
- [ ] 所有文字使用主题字体大小
- [ ] 所有颜色使用主题颜色
- [ ] 所有间距使用标准间距
- [ ] 所有圆角使用标准圆角
- [ ] 添加触觉反馈
- [ ] 添加动画效果
- [ ] 支持响应式布局

---

## 📊 最佳实践

### 1. 颜色使用原则
- ✅ 优先使用语义化颜色（success、error、warning、info）
- ✅ 根据游戏类型使用对应主题色
- ✅ 保持足够的对比度（WCAG 2.1标准）
- ❌ 避免使用硬编码的颜色值

### 2. 字体使用原则
- ✅ 标题使用headlineLarge/Medium/Small
- ✅ 正文使用bodyLarge/Medium
- ✅ 按钮使用labelLarge/Medium
- ✅ 支持系统字体缩放
- ❌ 避免使用过小的字体（< 12px）

### 3. 交互设计原则
- ✅ 所有可点击元素提供触觉反馈
- ✅ 按钮按下时有视觉反馈
- ✅ 最小触摸区域48x48px
- ✅ 动画时长控制在合理范围
- ❌ 避免过于复杂的手势

### 4. 布局设计原则
- ✅ 使用标准间距系统
- ✅ 保持视觉层次清晰
- ✅ 支持不同屏幕尺寸
- ✅ 考虑单手操作友好性
- ❌ 避免内容过于密集

---

*最后更新时间: 2024年12月19日*  
*版本: v1.0.0*  
*配套文档: UX_DESIGN_GUIDELINES.md* 
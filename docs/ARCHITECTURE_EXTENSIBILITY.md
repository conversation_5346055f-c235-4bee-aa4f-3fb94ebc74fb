# 架构扩展性指南：支持多种游戏类型

**文档版本:** 1.1
**创建日期:** 2025年6月29日

## 1. 核心思想：内容与实现分离

本项目的架构核心优势在于其**对内容扩展的友好性**。我们通过将**游戏类型 (Type)**、**游戏数据 (Data)** 和 **游戏实现 (Implementation)** 三者解耦，实现了高度的可扩展性。

- **游戏类型 (`PuzzleType`):** 一个枚举，用于标识一个谜题属于哪个大的分类。
- **游戏数据 (`data`字段):** 一个通用的 `Map<String, dynamic>` 字段，其内部结构由 `PuzzleType` 决定。
- **游戏实现 (`PuzzleEngine` & Widgets):** 根据 `PuzzleType` 来调用对应的验证逻辑和渲染对应的UI组件。

下面的章节将详细阐述如何为每个主要的游戏分类添加新的玩法。

---

## 2. 图形推理 (Graphic Pattern)

### 当前实现: 3x3 网格推理

- **`PuzzleType`**: `graphicPattern3x3`
- **数据模型**: `GraphicPatternData` (包含 `grid`, `options`, `answer`)
- **描述**: 这是图形推理的一种经典形式，但绝不是唯一形式。

### 扩展示例: “找不同” (Find the Difference)

假设我们要添加一种新的图形推理游戏: "从四个相似的图形中找出唯一不同的那一个"。流程如下：

1.  **定义新类型**: 在 `PuzzleType` 枚举中添加 `findTheDifference`。
2.  **创建新数据模型**: 在 `puzzle.dart` 中创建 `FindTheDifferenceData` 模型，包含 `List<String> options` 和 `String answer`。
3.  **创建新内容**: 在 `assets/puzzles/` 目录下创建 `find_the_difference_001.json`，并使用新的 `puzzleType` 和数据结构。
4.  **添加新逻辑**: 在 `PuzzleEngine` 中添加 `validateFindTheDifference` 方法，并在 `validateAnswer` 的 `switch` 语句中增加一个 `case` 来调用它。
5.  **创建新UI**: 创建一个 `FindTheDifferenceWidget` 来渲染这种新的游戏界面。

**结论**: 我们可以通过此模式添加任意多种图形推理游戏，如**图形序列**、**图形组合**等，而无需修改现有逻辑。

---

## 3. 数字逻辑 (Numeric Logic)

### 当前实现: 4x4 简化数独

- **`PuzzleType`**: `numericLogic`
- **数据模型**: `NumericLogicData` (包含 `grid`, `availableItems`, `constraints`)
- **描述**: 这是一种经典的数字逻辑玩法，但数字逻辑的世界远不止于此。

### 扩展示例: “数和” (Kakuro/Cross Sums)

假设我们要添加一种名为“数和”的游戏，玩家需要根据行列提示的数字和来填充格子。

1.  **定义新类型**: 在 `PuzzleType` 枚举中添加 `numericKakuro`。
2.  **创建新数据模型**: 创建 `KakuroData` 模型，包含 `List<int?> grid` (用于填数字), `List<KakuroClue> clues` (用于定义行列的和的提示)。
3.  **创建新内容**: 创建 `numeric_kakuro_001.json`，定义一个数和谜题的布局和答案。
4.  **添加新逻辑**: 在 `PuzzleEngine` 中添加 `validateKakuro` 方法，该方法需要实现检查每个横向和纵向的和是否符合提示的复杂逻辑。
5.  **创建新UI**: 创建一个 `KakuroWidget`，它需要能够渲染带提示的、不规则的网格布局。

**结论**: 您的架构同样可以轻松扩展到其他数字逻辑游戏，如**KenKen (新算数)**、**逻辑回路**等。

---

## 4. 空间想象 (Spatial Visualization)

### 当前实现: 2D展开图到3D模型的匹配

- **`PuzzleType`**: `spatialVisualization`
- **数据模型**: `SpatialVisualizationData` (包含 `expandedShape`, `options`, `answer`)
- **描述**: 这是锻炼空间想象能力的一种方式。

### 扩展示例: “心理旋转” (Mental Rotation)

假设我们要添加一种“心理旋转”游戏，玩家需要从四个经过旋转的图形中，选出与原始图形相同的那一个。

1.  **定义新类型**: 在 `PuzzleType` 枚举中添加 `spatialMentalRotation`。
2.  **创建新数据模型**: 创建 `MentalRotationData` 模型，包含 `String baseShape` (原始图形) 和 `List<RotatedShapeOption> options` (选项，每个选项包含图形ID和旋转角度)。
3.  **创建新内容**: 创建 `spatial_rotation_001.json`。
4.  **添加新逻辑**: 在 `PuzzleEngine` 中添加 `validateMentalRotation` 方法。
5.  **创建新UI**: 创建一个 `MentalRotationWidget`，用于展示原始图形和四个旋转后的选项。

**结论**: 您的架构可以支持各种空间想象游戏，如**三视图还原**、**立体图形计数**等。

---

## 5. 编程启蒙 (Intro to Coding)

### 当前实现: 简单迷宫寻路

- **`PuzzleType`**: `introToCoding`
- **数据模型**: `CodingData` (包含 `maze`, `startPosition`, `endPosition`, `availableCommands`)
- **描述**: 这是编程思维启蒙的基础形式。

### 扩展示例: 带“循环”指令的迷宫

假设我们要引入更复杂的编程概念，如“循环”。

1.  **定义新类型**: 在 `PuzzleType` 枚举中添加 `codingWithLoops`。
2.  **创建新数据模型**: 创建 `CodingWithLoopsData` 模型。它的 `availableCommands` 字段中可以包含特殊指令，如 `loop_start`, `loop_end`，或者可以有一个 `maxLoops` 字段来限制循环次数。
3.  **创建新内容**: 创建 `coding_loops_001.json`，设计一个需要用循环才能高效解决的迷宫。
4.  **添加新逻辑**: 在 `PuzzleEngine` 中添加 `validateCodingWithLoops` 方法。这个方法的验证逻辑会更复杂，需要一个能解释循环指令的“虚拟机”。
5.  **创建新UI**: 创建一个 `CodingWithLoopsWidget`，它的指令序列区域需要支持嵌套的、可视化的循环模块。

**结论**: 您的架构为逐步引入更高级的编程概念（如**循环**、**条件判断**、**函数**）提供了清晰的路径，完美契合了编程启蒙的教学过程。

---

## 最终总结

本项目的架构**非常健壮且富有远见**。我们并没有将任何一种游戏类型限定为某一种具体玩法，而是将其实现为一种**抽象类型**下的一个**具体实例**。

开发者可以随心所欲地添加任意多种游戏，只需要遵循上述的扩展流程即可，核心框架完全能够支撑这种演进。

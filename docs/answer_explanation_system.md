# 答案解析系统设计文档

## 📋 概述

LogicLab的答案解析系统是一个通用的、可扩展的教育功能模块，为所有游戏类型提供统一的答案解析体验。该系统旨在帮助6-12岁儿童理解解题思路，培养逻辑思维能力。

## 🎯 设计目标

### 核心目标
- **教育价值最大化**: 通过详细的解析帮助儿童理解解题过程
- **认知负荷最小化**: 采用步骤化、可视化的方式降低理解难度
- **体验一致性**: 所有游戏类型使用统一的解析界面和交互模式
- **内容可扩展性**: 支持多种解析内容类型和未来功能扩展

### 技术目标
- **架构通用性**: 单一系统支持所有游戏类型
- **组件复用性**: 高度模块化的UI组件设计
- **数据灵活性**: 支持不同复杂度和类型的解析内容
- **性能优化**: 高效的数据加载和渲染机制

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    答案解析系统                              │
├─────────────────────────────────────────────────────────────┤
│  Presentation Layer (表现层)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  UniversalAnswerExplanationWidget                       │ │
│  │  ├── 结果展示区域                                        │ │
│  │  ├── 核心要点展示                                        │ │
│  │  ├── 解析步骤/选项分析切换                               │ │
│  │  ├── 步骤导航控制                                        │ │
│  │  └── 操作按钮区域                                        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Domain Layer (领域层)                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  AnswerExplanationFactory                               │ │
│  │  ├── createGraphicPatternExplanation()                  │ │
│  │  ├── createSpatialVisualizationExplanation()            │ │
│  │  ├── createNumericLogicExplanation()                    │ │
│  │  ├── createCodingExplanation()                          │ │
│  │  └── createMirrorSymmetryExplanation()                  │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (数据层)                                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  UniversalAnswerExplanation                             │ │
│  │  ├── puzzleType: String                                 │ │
│  │  ├── keyPoint: String                                   │ │
│  │  ├── steps: List<ExplanationStep>                       │ │
│  │  ├── optionExplanations: List<OptionExplanation>        │ │
│  │  └── additionalData: Map<String, dynamic>               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Service Layer (服务层)                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  PuzzleEngine                                           │ │
│  │  └── getAnswerExplanation()                             │ │
│  │                                                         │ │
│  │  PuzzleRepositoryImpl                                   │ │
│  │  └── getAnswerExplanation()                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件关系
```mermaid
graph TD
    A[PuzzleEngine] --> B[AnswerExplanationFactory]
    B --> C[UniversalAnswerExplanation]
    C --> D[UniversalAnswerExplanationWidget]
    
    E[GameData] --> A
    F[UserAnswer] --> A
    
    D --> G[ExplanationStep]
    D --> H[OptionExplanation]
    
    I[PuzzleRepositoryImpl] --> A
    J[PuzzleBloc] --> I
```

## 📊 数据模型设计

### 核心数据结构

#### UniversalAnswerExplanation
```dart
class UniversalAnswerExplanation {
  final String puzzleType;              // 游戏类型标识
  final String keyPoint;                // 核心要点
  final String? verificationText;       // 验证说明（可选）
  final String? thinkingMethod;         // 思维方式（可选）
  final List<ExplanationStep> steps;    // 解析步骤
  final List<OptionExplanation> optionExplanations; // 选项解释
  final String conclusion;              // 结论
  final Map<String, dynamic>? additionalData; // 额外数据
}
```

#### ExplanationStep
```dart
class ExplanationStep {
  final int stepNumber;                 // 步骤编号
  final String title;                   // 步骤标题
  final String description;             // 步骤描述
  final ExplanationStepType type;       // 步骤类型
  final Map<String, dynamic>? content;  // 步骤内容
  final List<String>? highlights;       // 需要高亮的元素
  final String? tip;                    // 提示信息
}
```

#### ExplanationStepType
```dart
enum ExplanationStepType {
  text,         // 纯文本解释
  visual,       // 视觉演示（图片、动画）
  interactive,  // 交互式演示
  comparison,   // 对比分析
  process,      // 过程演示
  rule,         // 规则说明
}
```

### 游戏类型特定数据

#### 图形推理解析
```dart
// 特点：规律识别和模式分析
{
  "puzzleType": "graphicPattern",
  "keyPoint": "观察图形的变化规律，找出缺失部分的正确图案",
  "steps": [
    {
      "stepNumber": 1,
      "title": "观察行变化",
      "type": "visual",
      "description": "分析每一行图形的变化规律"
    }
  ]
}
```

#### 空间想象解析
```dart
// 特点：3D空间转换和折叠过程
{
  "puzzleType": "spatialVisualization", 
  "keyPoint": "将2D展开图在脑中折叠成3D立体图形",
  "steps": [
    {
      "stepNumber": 1,
      "title": "标记关键面",
      "type": "interactive",
      "description": "在展开图上标记有特殊图案的面"
    }
  ]
}
```

#### 数字逻辑解析
```dart
// 特点：约束满足和逻辑推理
{
  "puzzleType": "numericLogic",
  "keyPoint": "根据约束条件，使用逻辑推理填充网格",
  "steps": [
    {
      "stepNumber": 1,
      "title": "理解约束",
      "type": "rule",
      "description": "仔细阅读并理解所有约束条件"
    }
  ]
}
```

#### 编程启蒙解析
```dart
// 特点：算法思维和路径规划
{
  "puzzleType": "coding",
  "keyPoint": "设计最优路径，使用最少的指令到达目标",
  "steps": [
    {
      "stepNumber": 1,
      "title": "分析路径",
      "type": "visual",
      "description": "观察起点和终点，规划可能的移动路径"
    }
  ]
}
```

#### 镜像对称解析
```dart
// 特点：对称关系和几何变换
{
  "puzzleType": "mirrorSymmetry",
  "keyPoint": "理解镜像对称的概念，找出正确的镜像图形",
  "steps": [
    {
      "stepNumber": 1,
      "title": "确定对称轴",
      "type": "visual",
      "description": "根据题目要求确定对称轴的位置"
    }
  ]
}
```

## 🎨 UI设计规范

### 视觉层次结构
```
┌─────────────────────────────────────────────────────────┐
│  结果标题区域 (正确/错误状态指示)                        │
├─────────────────────────────────────────────────────────┤
│  核心要点展示 (Key Point 高亮显示)                      │
├─────────────────────────────────────────────────────────┤
│  内容切换区域                                           │
│  ┌─────────────────┐  ┌─────────────────────────────────┐ │
│  │  解析步骤模式    │  │  选项分析模式                   │ │
│  │  ├── 步骤指示器  │  │  ├── 选项A (正确/错误/用户选择) │ │
│  │  ├── 当前步骤    │  │  ├── 选项B                     │ │
│  │  ├── 步骤导航    │  │  ├── 选项C                     │ │
│  │  └── 提示信息    │  │  └── 选项D                     │ │
│  └─────────────────┘  └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  操作按钮区域                                           │
│  ┌─────────────────┐  ┌─────────────────────────────────┐ │
│  │  切换视图按钮    │  │  主要操作 (关闭/重试/下一题)    │ │
│  └─────────────────┘  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 色彩系统
- **正确状态**: `UXThemeConfig.successGreen` (#4CAF50)
- **错误状态**: `UXThemeConfig.errorRed` (#F44336)
- **主要操作**: `UXThemeConfig.primaryBlue` (#2196F3)
- **警告提示**: `UXThemeConfig.warning` (#FF9800)
- **背景层次**: `UXThemeConfig.backgroundPrimary/Secondary`

### 动画效果
- **入场动画**: FadeTransition + SlideTransition (300ms)
- **内容切换**: AnimatedSwitcher (300ms)
- **步骤导航**: 平滑的页面切换效果
- **状态变化**: 颜色渐变和图标变化

## 🔧 API接口设计

### PuzzleEngine接口
```dart
class PuzzleEngine {
  /// 获取答案解析
  UniversalAnswerExplanation? getAnswerExplanation(
    Puzzle puzzle, 
    dynamic userAnswer
  );
  
  /// 验证答案
  bool validateAnswer(Puzzle puzzle, dynamic userAnswer);
}
```

### PuzzleRepository接口
```dart
abstract class PuzzleValidationRepository {
  /// 获取答案解析
  Future<Result<Map<String, dynamic>>> getAnswerExplanation(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  );
  
  /// 验证答案
  Future<Result<bool>> validateAnswer(
    PuzzleEntity puzzle, 
    dynamic userAnswer
  );
  
  /// 批量验证答案
  Future<Result<List<bool>>> validateAnswers(
    List<({PuzzleEntity puzzle, dynamic userAnswer})> validations,
  );
  
  /// 验证答案格式
  Future<Result<bool>> validateAnswerFormat(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  );
  
  /// 获取答案相似度
  Future<Result<double>> getAnswerSimilarity(
    PuzzleEntity puzzle,
    dynamic userAnswer,
  );
}
```

### AnswerExplanationFactory接口
```dart
class AnswerExplanationFactory {
  /// 图形推理解析
  static UniversalAnswerExplanation createGraphicPatternExplanation({
    required String correctAnswer,
    required List<String> options,
    required String pattern,
  });
  
  /// 空间想象解析
  static UniversalAnswerExplanation createSpatialVisualizationExplanation({
    required String correctAnswer,
    required List<String> options,
    required String expandedShape,
  });
  
  /// 数字逻辑解析
  static UniversalAnswerExplanation createNumericLogicExplanation({
    required Map<String, dynamic> constraints,
    required List<String> availableItems,
  });
  
  /// 编程启蒙解析
  static UniversalAnswerExplanation createCodingExplanation({
    required List<String> solution,
    required Map<String, int> startPosition,
    required Map<String, int> endPosition,
  });
  
  /// 镜像对称解析
  static UniversalAnswerExplanation createMirrorSymmetryExplanation({
    required String correctAnswer,
    required List<String> options,
    required String mirrorDirection,
    required String originalImage,
  });
}
```

## 📱 使用指南

### 基本使用流程

#### 1. 获取解析数据
```dart
// 通过PuzzleEngine获取
final explanation = puzzleEngine.getAnswerExplanation(puzzle, userAnswer);

// 通过Repository获取
final result = await puzzleRepository.getAnswerExplanation(puzzle, userAnswer);
if (result.isSuccess) {
  final explanationData = result.data;
  final explanation = UniversalAnswerExplanation.fromJson(explanationData);
}
```

#### 2. 显示解析UI
```dart
// 在游戏页面中显示解析
UniversalAnswerExplanationWidget(
  explanation: explanation,
  isCorrect: isAnswerCorrect,
  userAnswer: userSelectedAnswer,
  onClose: () {
    // 关闭解析，返回游戏
    Navigator.of(context).pop();
  },
  onRetry: isAnswerCorrect ? null : () {
    // 重试当前题目
    _retryCurrentPuzzle();
  },
  onNext: isAnswerCorrect ? () {
    // 继续下一题
    _loadNextPuzzle();
  } : null,
)
```

#### 3. 自定义解析内容
```dart
// 创建自定义解析
final customExplanation = UniversalAnswerExplanation(
  puzzleType: 'customType',
  keyPoint: '自定义核心要点',
  steps: [
    ExplanationStep(
      stepNumber: 1,
      title: '第一步',
      description: '详细描述',
      type: ExplanationStepType.text,
      tip: '提示信息',
    ),
  ],
  optionExplanations: [
    OptionExplanation(
      optionId: 'A',
      explanation: '选项A的解释',
      isCorrect: true,
    ),
  ],
  conclusion: '总结',
);
```

### 高级功能

#### 1. 动态内容加载
```dart
// 支持异步加载解析内容
class DynamicExplanationWidget extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UniversalAnswerExplanation>(
      future: _loadExplanation(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return UniversalAnswerExplanationWidget(
            explanation: snapshot.data!,
            // ... 其他参数
          );
        }
        return CircularProgressIndicator();
      },
    );
  }
}
```

#### 2. 个性化解析
```dart
// 根据用户水平调整解析详细程度
UniversalAnswerExplanation createPersonalizedExplanation(
  UserProfile user,
  Puzzle puzzle,
) {
  final baseExplanation = AnswerExplanationFactory.createExplanation(puzzle);
  
  if (user.skillLevel < 3) {
    // 初学者：添加更多详细步骤
    return baseExplanation.copyWith(
      steps: _addDetailedSteps(baseExplanation.steps),
    );
  } else {
    // 高级用户：简化步骤，突出关键点
    return baseExplanation.copyWith(
      steps: _simplifySteps(baseExplanation.steps),
    );
  }
}
```

## 🧪 测试策略

### 单元测试
```dart
// 测试解析工厂
group('AnswerExplanationFactory', () {
  test('should create graphic pattern explanation', () {
    final explanation = AnswerExplanationFactory.createGraphicPatternExplanation(
      correctAnswer: 'A',
      options: ['A', 'B', 'C', 'D'],
      pattern: 'rotation',
    );
    
    expect(explanation.puzzleType, equals('graphicPattern'));
    expect(explanation.steps.length, greaterThan(0));
    expect(explanation.optionExplanations.length, equals(4));
  });
});

// 测试数据模型
group('UniversalAnswerExplanation', () {
  test('should serialize to JSON correctly', () {
    final explanation = UniversalAnswerExplanation(/* ... */);
    final json = explanation.toJson();
    final restored = UniversalAnswerExplanation.fromJson(json);
    
    expect(restored, equals(explanation));
  });
});
```

### Widget测试
```dart
// 测试解析UI组件
group('UniversalAnswerExplanationWidget', () {
  testWidgets('should display correct answer state', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: UniversalAnswerExplanationWidget(
          explanation: mockExplanation,
          isCorrect: true,
          userAnswer: 'A',
        ),
      ),
    );
    
    expect(find.text('回答正确！'), findsOneWidget);
    expect(find.byIcon(Icons.check_circle), findsOneWidget);
  });
  
  testWidgets('should switch between steps and options', (tester) async {
    await tester.pumpWidget(/* ... */);
    
    // 初始显示选项分析
    expect(find.text('选项分析'), findsOneWidget);
    
    // 点击切换按钮
    await tester.tap(find.text('查看解析步骤'));
    await tester.pumpAndSettle();
    
    // 应该显示解析步骤
    expect(find.text('解析步骤'), findsOneWidget);
  });
});
```

### 集成测试
```dart
// 测试完整的解析流程
group('Answer Explanation Integration', () {
  testWidgets('complete explanation flow', (tester) async {
    // 1. 加载谜题
    final puzzle = await puzzleEngine.loadPuzzle('test_puzzle_1');
    
    // 2. 提交答案
    final isCorrect = puzzleEngine.validateAnswer(puzzle, 'A');
    
    // 3. 获取解析
    final explanation = puzzleEngine.getAnswerExplanation(puzzle, 'A');
    
    // 4. 显示解析UI
    await tester.pumpWidget(
      MaterialApp(
        home: UniversalAnswerExplanationWidget(
          explanation: explanation,
          isCorrect: isCorrect,
          userAnswer: 'A',
        ),
      ),
    );
    
    // 5. 验证UI状态
    expect(find.text(explanation.keyPoint), findsOneWidget);
  });
});
```

## 🚀 性能优化

### 数据缓存策略
```dart
class ExplanationCache {
  static final Map<String, UniversalAnswerExplanation> _cache = {};
  
  static UniversalAnswerExplanation? get(String puzzleId) {
    return _cache[puzzleId];
  }
  
  static void put(String puzzleId, UniversalAnswerExplanation explanation) {
    _cache[puzzleId] = explanation;
  }
  
  static void clear() {
    _cache.clear();
  }
}
```

### 懒加载机制
```dart
class LazyExplanationLoader {
  Future<UniversalAnswerExplanation> loadExplanation(
    String puzzleId,
    dynamic userAnswer,
  ) async {
    // 检查缓存
    final cached = ExplanationCache.get(puzzleId);
    if (cached != null) return cached;
    
    // 异步加载
    final explanation = await _generateExplanation(puzzleId, userAnswer);
    
    // 缓存结果
    ExplanationCache.put(puzzleId, explanation);
    
    return explanation;
  }
}
```

### UI渲染优化
```dart
class OptimizedExplanationWidget extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      // 使用ListView.builder进行懒渲染
      itemCount: explanation.steps.length,
      itemBuilder: (context, index) {
        return _buildStepWidget(explanation.steps[index]);
      },
    );
  }
  
  Widget _buildStepWidget(ExplanationStep step) {
    // 使用RepaintBoundary优化重绘
    return RepaintBoundary(
      child: ExplanationStepWidget(step: step),
    );
  }
}
```

## 📈 扩展规划

### 短期扩展 (1-3个月)
1. **内容增强**
   - 添加更多视觉演示元素
   - 支持动画和交互式内容
   - 增加音频解说功能

2. **个性化功能**
   - 根据用户水平调整解析详细程度
   - 记录用户偏好的解析类型
   - 提供解析内容的难度选择

### 中期扩展 (3-6个月)
1. **AI辅助解析**
   - 使用AI生成个性化解析内容
   - 智能识别用户的理解困难点
   - 动态调整解析策略

2. **多媒体支持**
   - 集成视频解析
   - 支持3D模型展示
   - 添加手势交互功能

### 长期扩展 (6-12个月)
1. **社交学习功能**
   - 用户可以分享自己的解析理解
   - 同龄人互助解析功能
   - 家长参与的解析讨论

2. **数据驱动优化**
   - 收集解析效果数据
   - A/B测试不同解析方式
   - 基于数据优化解析内容

## 📚 参考资料

### 教育理论基础
- **认知负荷理论**: 控制解析内容的复杂度，避免认知过载
- **建构主义学习理论**: 通过步骤化解析帮助学生构建知识
- **多元智能理论**: 支持不同类型的解析内容适应不同学习风格

### 技术参考
- **Flutter官方文档**: Widget设计和动画实现
- **Material Design**: UI设计规范和交互模式
- **Clean Architecture**: 系统架构设计原则

### 竞品分析
- **Khan Academy**: 教育内容的解析方式
- **Brilliant**: 交互式学习体验设计
- **DragonBox**: 儿童数学游戏的解析模式

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. 解析内容不显示
**问题**: UniversalAnswerExplanationWidget显示空白
**原因**: 解析数据为null或格式错误
**解决方案**:
```dart
// 检查解析数据
if (explanation == null) {
  return Text('暂无解析内容');
}

// 验证数据完整性
if (explanation.steps.isEmpty && explanation.optionExplanations.isEmpty) {
  return Text('解析内容加载失败');
}
```

#### 2. 步骤导航异常
**问题**: 步骤切换时出现IndexError
**原因**: 步骤索引超出范围
**解决方案**:
```dart
void _navigateToStep(int stepIndex) {
  if (stepIndex >= 0 && stepIndex < widget.explanation.steps.length) {
    setState(() {
      _currentStepIndex = stepIndex;
    });
  }
}
```

#### 3. 动画卡顿
**问题**: 解析界面动画不流畅
**原因**: 复杂内容导致渲染性能问题
**解决方案**:
```dart
// 使用RepaintBoundary优化
RepaintBoundary(
  child: UniversalAnswerExplanationWidget(
    explanation: explanation,
    // ...
  ),
)

// 减少不必要的重建
const UniversalAnswerExplanationWidget(
  key: ValueKey('explanation'),
  // ...
)
```

### 调试工具

#### 1. 解析数据验证器
```dart
class ExplanationValidator {
  static bool validate(UniversalAnswerExplanation explanation) {
    if (explanation.keyPoint.isEmpty) return false;
    if (explanation.steps.isEmpty && explanation.optionExplanations.isEmpty) return false;

    for (final step in explanation.steps) {
      if (step.title.isEmpty || step.description.isEmpty) return false;
    }

    return true;
  }
}
```

#### 2. 性能监控
```dart
class ExplanationPerformanceMonitor {
  static void trackLoadTime(String puzzleType, Duration loadTime) {
    print('Explanation load time for $puzzleType: ${loadTime.inMilliseconds}ms');
  }

  static void trackRenderTime(String puzzleType, Duration renderTime) {
    print('Explanation render time for $puzzleType: ${renderTime.inMilliseconds}ms');
  }
}
```

## 📋 最佳实践

### 内容创作指南

#### 1. 解析内容编写原则
- **简洁明了**: 使用儿童易懂的语言
- **循序渐进**: 按照逻辑顺序组织步骤
- **视觉辅助**: 尽可能使用图形和动画
- **互动性强**: 鼓励用户参与思考过程

#### 2. 步骤设计规范
```dart
// 好的步骤设计
ExplanationStep(
  stepNumber: 1,
  title: '观察图形特征',           // 简短明确的标题
  description: '仔细看看每个图形的形状、颜色和大小', // 具体的指导
  type: ExplanationStepType.visual,
  tip: '可以用手指指着图形来帮助观察',  // 实用的提示
)

// 避免的设计
ExplanationStep(
  stepNumber: 1,
  title: '第一步',              // 标题不明确
  description: '看图形',         // 描述过于简单
  type: ExplanationStepType.text,
  // 缺少提示信息
)
```

#### 3. 选项解释编写
```dart
// 正确答案解释
OptionExplanation(
  optionId: 'A',
  explanation: '选项A正确，因为它遵循了"每行图形顺时针旋转90度"的规律',
  isCorrect: true,
)

// 错误答案解释
OptionExplanation(
  optionId: 'B',
  explanation: '选项B不正确，它是逆时针旋转的，与规律不符',
  isCorrect: false,
)
```

### 代码开发规范

#### 1. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 设计通用的子组件
- **可测试性**: 便于编写单元测试
- **可访问性**: 支持屏幕阅读器等辅助功能

#### 2. 状态管理
```dart
// 使用StatefulWidget管理本地状态
class UniversalAnswerExplanationWidget extends StatefulWidget {
  // 不可变的配置参数
  final UniversalAnswerExplanation explanation;
  final bool isCorrect;

  // 可变的UI状态在State中管理
  // int _currentStepIndex;
  // bool _showSteps;
}
```

#### 3. 错误处理
```dart
// 优雅的错误处理
Widget _buildExplanationContent() {
  try {
    return _buildStepsOrOptions();
  } catch (e) {
    // 记录错误但不影响用户体验
    Logger().e('Failed to build explanation content', error: e);
    return _buildErrorFallback();
  }
}

Widget _buildErrorFallback() {
  return Container(
    padding: EdgeInsets.all(UXThemeConfig.paddingL),
    child: Column(
      children: [
        Icon(Icons.error_outline, size: 48, color: UXThemeConfig.warning),
        SizedBox(height: UXThemeConfig.paddingM),
        Text('解析内容暂时无法显示，请稍后重试'),
      ],
    ),
  );
}
```

## 🌐 国际化支持

### 多语言解析内容
```dart
// 支持多语言的解析工厂
class LocalizedAnswerExplanationFactory {
  static UniversalAnswerExplanation createExplanation(
    PuzzleType puzzleType,
    String locale, {
    required Map<String, dynamic> params,
  }) {
    switch (locale) {
      case 'zh_CN':
        return _createChineseExplanation(puzzleType, params);
      case 'en_US':
        return _createEnglishExplanation(puzzleType, params);
      default:
        return _createDefaultExplanation(puzzleType, params);
    }
  }
}
```

### 文本本地化
```dart
// 使用Flutter的国际化框架
class ExplanationLocalizations {
  static const Map<String, Map<String, String>> _localizedValues = {
    'zh_CN': {
      'correct_answer': '回答正确！',
      'wrong_answer': '回答错误',
      'key_point': '核心要点',
      'explanation_steps': '解析步骤',
      'option_analysis': '选项分析',
    },
    'en_US': {
      'correct_answer': 'Correct!',
      'wrong_answer': 'Incorrect',
      'key_point': 'Key Point',
      'explanation_steps': 'Explanation Steps',
      'option_analysis': 'Option Analysis',
    },
  };

  static String get(String key, String locale) {
    return _localizedValues[locale]?[key] ?? key;
  }
}
```

## 📊 数据分析与监控

### 用户行为追踪
```dart
class ExplanationAnalytics {
  static void trackExplanationViewed(String puzzleType, String puzzleId) {
    // 记录用户查看解析的行为
    AnalyticsService.track('explanation_viewed', {
      'puzzle_type': puzzleType,
      'puzzle_id': puzzleId,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackStepNavigation(int fromStep, int toStep) {
    // 记录步骤导航行为
    AnalyticsService.track('step_navigation', {
      'from_step': fromStep,
      'to_step': toStep,
    });
  }

  static void trackViewModeSwitch(String fromMode, String toMode) {
    // 记录视图切换行为
    AnalyticsService.track('view_mode_switch', {
      'from_mode': fromMode, // 'steps' or 'options'
      'to_mode': toMode,
    });
  }
}
```

### 性能指标监控
```dart
class ExplanationMetrics {
  static void recordLoadTime(String puzzleType, Duration loadTime) {
    MetricsService.record('explanation_load_time', {
      'puzzle_type': puzzleType,
      'duration_ms': loadTime.inMilliseconds,
    });
  }

  static void recordUserEngagement(Duration viewTime, int stepsViewed) {
    MetricsService.record('explanation_engagement', {
      'view_time_seconds': viewTime.inSeconds,
      'steps_viewed': stepsViewed,
    });
  }
}
```

---

*本文档版本: v1.0*
*最后更新: 2025-01-05*
*维护者: LogicLab开发团队*

## 📞 支持与反馈

如有问题或建议，请联系开发团队：
- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [LogicLab Issues](https://github.com/mflyn/LogicLab/issues)
- 📚 Wiki: [LogicLab Wiki](https://github.com/mflyn/LogicLab/wiki)

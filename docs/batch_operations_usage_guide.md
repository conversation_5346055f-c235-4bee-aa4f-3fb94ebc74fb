# 批量操作和性能优化使用指南

## 概述

LogicLab项目现已完成所有中优先级的代码评审改进，包括：

1. ✅ **统一错误处理机制** - 完整的错误码系统和异常类层次结构
2. ✅ **完善参数验证** - 可组合的验证器框架和Use Cases层验证
3. ✅ **接口设计优化** - 统一的Result<T>返回类型和接口拆分
4. ✅ **Repository接口拆分** - 按职责拆分的专门接口
5. ✅ **缓存策略实现** - 完整的缓存管理器和装饰器
6. ✅ **批量操作支持** - Repository、Use Cases和性能优化层的批量接口

本文档介绍如何使用新增的批量操作和性能优化功能。

## 批量用户操作

### 1. 基础批量操作

```dart
import 'package:logic_lab/domain/usecases/batch_user_operations_usecase.dart';

// 注入依赖
final batchUserFacade = BatchUserOperationsFacade(
  batchCreateUsersUseCase: BatchCreateUsersUseCase(userCommandRepository),
  batchUpdateUsersUseCase: BatchUpdateUsersUseCase(userCommandRepository),
  batchDeleteUsersUseCase: BatchDeleteUsersUseCase(userCommandRepository),
  batchUnlockAchievementsUseCase: BatchUnlockAchievementsUseCase(userCommandRepository),
);

// 批量创建用户
final users = [
  (nickname: '小明', avatarId: 'avatar_1'),
  (nickname: '小红', avatarId: 'avatar_2'),
  (nickname: '小刚', avatarId: 'avatar_3'),
];

final createResult = await batchUserFacade.createUsers(users);
createResult.fold(
  onSuccess: (createdUsers) {
    print('成功创建 ${createdUsers.length} 个用户');
    for (final user in createdUsers) {
      print('用户: ${user.nickname} (${user.id})');
    }
  },
  onFailure: (exception) {
    print('批量创建失败: ${exception.message}');
  },
);

// 批量解锁成就
final achievements = [
  (userId: 'user_1', achievementId: 'first_login'),
  (userId: 'user_2', achievementId: 'first_puzzle'),
  (userId: 'user_3', achievementId: 'daily_login'),
];

final unlockResult = await batchUserFacade.unlockAchievements(achievements);
unlockResult.fold(
  onSuccess: (_) => print('成就解锁成功'),
  onFailure: (exception) => print('成就解锁失败: ${exception.message}'),
);
```

### 2. 组合操作示例

```dart
// 创建用户并解锁初始成就
final usersWithAchievements = [
  (
    nickname: '新用户1',
    avatarId: 'avatar_1',
    initialAchievements: ['newcomer', 'first_login'],
  ),
  (
    nickname: '新用户2',
    avatarId: 'avatar_2',
    initialAchievements: ['newcomer'],
  ),
];

final result = await batchUserFacade.createUsersWithInitialAchievements(usersWithAchievements);
result.fold(
  onSuccess: (users) {
    print('成功创建用户并解锁初始成就');
    for (final user in users) {
      print('用户: ${user.nickname}');
    }
  },
  onFailure: (exception) => print('操作失败: ${exception.message}'),
);
```

## 批量谜题操作

### 1. 批量验证答案

```dart
import 'package:logic_lab/domain/usecases/batch_puzzle_operations_usecase.dart';

final batchPuzzleFacade = BatchPuzzleOperationsFacade(
  batchValidateAnswersUseCase: BatchValidateAnswersUseCase(puzzleValidationRepository),
  batchGenerateHintsUseCase: BatchGenerateHintsUseCase(puzzleHintRepository),
  batchCreatePuzzlesUseCase: BatchCreatePuzzlesUseCase(puzzleManagementRepository),
  batchUpdatePuzzlesUseCase: BatchUpdatePuzzlesUseCase(puzzleManagementRepository),
  batchDeletePuzzlesUseCase: BatchDeletePuzzlesUseCase(puzzleManagementRepository),
);

// 批量验证答案
final validations = [
  (puzzle: puzzle1, userAnswer: 'A'),
  (puzzle: puzzle2, userAnswer: 'B'),
  (puzzle: puzzle3, userAnswer: 'C'),
];

final validateResult = await batchPuzzleFacade.validateAnswers(validations);
validateResult.fold(
  onSuccess: (results) {
    for (int i = 0; i < results.length; i++) {
      final isCorrect = results[i];
      final puzzle = validations[i].puzzle;
      print('谜题 ${puzzle.levelId}: ${isCorrect ? '正确' : '错误'}');
    }
  },
  onFailure: (exception) => print('批量验证失败: ${exception.message}'),
);
```

### 2. 智能答案验证与提示生成

```dart
// 验证答案并为错误答案生成下一级提示
final requests = [
  (puzzle: puzzle1, userAnswer: 'A', currentHintLevel: 1),
  (puzzle: puzzle2, userAnswer: 'B', currentHintLevel: 2),
  (puzzle: puzzle3, userAnswer: 'C', currentHintLevel: 0),
];

final smartResult = await batchPuzzleFacade.validateAnswersWithNextHints(requests);
smartResult.fold(
  onSuccess: (results) {
    for (int i = 0; i < results.length; i++) {
      final result = results[i];
      final puzzle = requests[i].puzzle;
      
      if (result.isCorrect) {
        print('谜题 ${puzzle.levelId}: 答案正确！');
      } else {
        print('谜题 ${puzzle.levelId}: 答案错误');
        if (result.nextHint != null) {
          print('提示: ${result.nextHint!['content']}');
        }
      }
    }
  },
  onFailure: (exception) => print('智能验证失败: ${exception.message}'),
);
```

### 3. 批量导入谜题

```dart
// 从JSON数据批量导入谜题
final puzzlesToImport = [
  // 假设这些是从文件或API获取的谜题数据
  puzzle1Entity,
  puzzle2Entity,
  puzzle3Entity,
];

final importResult = await batchPuzzleFacade.importAndValidatePuzzles(puzzlesToImport);
importResult.fold(
  onSuccess: (result) {
    print('成功导入 ${result.imported} 个谜题');
    if (result.errors.isNotEmpty) {
      print('错误信息:');
      for (final error in result.errors) {
        print('  - $error');
      }
    }
  },
  onFailure: (exception) => print('导入失败: ${exception.message}'),
);
```

## 性能优化功能

### 1. 缓存装饰器使用

```dart
import 'package:logic_lab/core/utils/performance_optimizer.dart';

// 创建缓存装饰器
final cachedDecorator = CachedResultDecorator<List<PuzzleEntity>>(
  ttl: Duration(minutes: 10),
  maxSize: 50,
);

// 使用缓存执行操作
Future<Result<List<PuzzleEntity>>> getCachedPuzzles(String worldId) {
  return cachedDecorator.execute(
    'puzzles_$worldId',
    () => puzzleRepository.getPuzzlesByThemeWorld(ThemeWorld.forest),
  );
}

// 第一次调用会执行实际操作
final puzzles1 = await getCachedPuzzles('forest');

// 第二次调用会从缓存返回
final puzzles2 = await getCachedPuzzles('forest'); // 缓存命中

// 查看缓存统计
final stats = cachedDecorator.getStats();
print('缓存统计: $stats');
```

### 2. 批量操作优化器

```dart
// 创建批量操作优化器
final batchOptimizer = BatchOperationOptimizer<String, bool>(
  operationName: 'validateAnswers',
  batchProcessor: (puzzleIds) async {
    // 实际的批量处理逻辑
    final puzzles = await puzzleRepository.getPuzzlesByIds(puzzleIds);
    return puzzles.map((puzzle) => validateSingleAnswer(puzzle)).toList();
  },
  batchSize: 20,
  batchInterval: Duration(milliseconds: 100),
);

// 添加单个操作到批次
final result1 = batchOptimizer.add('puzzle_1');
final result2 = batchOptimizer.add('puzzle_2');
final result3 = batchOptimizer.add('puzzle_3');

// 这些操作会被自动批量处理
final isValid1 = await result1;
final isValid2 = await result2;
final isValid3 = await result3;

// 查看优化器统计
final optimizerStats = batchOptimizer.getStats();
print('批量优化器统计: $optimizerStats');
```

### 3. 防抖动和节流

```dart
final performanceOptimizer = PerformanceOptimizer();

// 防抖动 - 用于搜索输入
void onSearchTextChanged(String query) {
  performanceOptimizer.debounce(
    'search',
    Duration(milliseconds: 300),
    () => performSearch(query),
  );
}

// 节流 - 用于按钮点击
void onRefreshButtonPressed() {
  performanceOptimizer.throttle(
    'refresh',
    Duration(seconds: 2),
    () => refreshData(),
  );
}

// 批量队列 - 用于日志记录
void logUserAction(UserAction action) {
  performanceOptimizer.addToBatch<UserAction>(
    'user_actions',
    action,
    batchSize: 10,
    maxWaitTime: Duration(seconds: 5),
    processor: (actions) => sendLogsToServer(actions),
  );
}
```

### 4. 性能监控

```dart
final performanceMonitor = PerformanceMonitor();

// 监控操作性能
Future<List<PuzzleEntity>> getRecommendedPuzzles() {
  return performanceMonitor.monitor(
    'getRecommendedPuzzles',
    () => puzzleRepository.getRecommendedPuzzles(
      userSkillPoints: currentUser.skillPoints,
      completedLevels: currentUser.completedLevels,
    ),
  );
}

// 查看性能统计
final allStats = performanceMonitor.getAllStats();
print('性能统计: $allStats');

// 查看特定操作统计
final recommendStats = performanceMonitor.getOperationStats('getRecommendedPuzzles');
print('推荐谜题操作统计: $recommendStats');
```

### 5. 内存优化

```dart
final memoryOptimizer = MemoryOptimizer();

// 开始内存监控
memoryOptimizer.startMonitoring(interval: Duration(minutes: 5));

// 跟踪重要对象
memoryOptimizer.trackObject(puzzleEngine);
memoryOptimizer.trackObject(userService);

// 查看内存统计
final memoryStats = memoryOptimizer.getMemoryStats();
print('内存统计: $memoryStats');

// 手动清理
memoryOptimizer.forceCleanup();
```

## 集成使用示例

### 完整的游戏会话优化

```dart
class OptimizedGameSession {
  final BatchUserOperationsFacade _userBatch;
  final BatchPuzzleOperationsFacade _puzzleBatch;
  final CachedResultDecorator<List<PuzzleEntity>> _puzzleCache;
  final BatchOperationOptimizer<String, bool> _answerValidator;
  final PerformanceMonitor _monitor;

  OptimizedGameSession({
    required BatchUserOperationsFacade userBatch,
    required BatchPuzzleOperationsFacade puzzleBatch,
  })  : _userBatch = userBatch,
        _puzzleBatch = puzzleBatch,
        _puzzleCache = CachedResultDecorator(ttl: Duration(minutes: 15)),
        _answerValidator = BatchOperationOptimizer(
          operationName: 'validateAnswers',
          batchProcessor: _batchValidateAnswers,
        ),
        _monitor = PerformanceMonitor();

  // 获取推荐谜题（带缓存）
  Future<Result<List<PuzzleEntity>>> getRecommendedPuzzles(String userId) {
    return _monitor.monitor('getRecommendedPuzzles', () {
      return _puzzleCache.execute(
        'recommended_$userId',
        () => _puzzleRepository.getRecommendedPuzzles(
          userSkillPoints: _currentUser.skillPoints,
          completedLevels: _currentUser.completedLevels,
        ),
      );
    });
  }

  // 提交答案（批量优化）
  Future<Result<bool>> submitAnswer(String puzzleId, dynamic answer) {
    return _answerValidator.add('${puzzleId}_$answer');
  }

  // 批量解锁成就
  Future<Result<void>> unlockDailyAchievements(List<String> userIds) {
    final achievements = userIds.map((userId) => (
      userId: userId,
      achievementId: 'daily_login_${DateTime.now().day}',
    )).toList();

    return _userBatch.unlockAchievements(achievements);
  }

  // 获取性能报告
  Map<String, dynamic> getPerformanceReport() {
    return {
      'monitor': _monitor.getAllStats(),
      'cache': _puzzleCache.getStats(),
      'batchValidator': _answerValidator.getStats(),
    };
  }

  static Future<Result<List<bool>>> _batchValidateAnswers(List<String> items) async {
    // 解析谜题ID和答案
    final validations = <({PuzzleEntity puzzle, dynamic userAnswer})>[];
    
    for (final item in items) {
      final parts = item.split('_');
      final puzzleId = parts[0];
      final answer = parts[1];
      
      final puzzle = await _puzzleRepository.getPuzzleById(puzzleId);
      if (puzzle.isSuccess && puzzle.data != null) {
        validations.add((puzzle: puzzle.data!, userAnswer: answer));
      }
    }
    
    return _puzzleBatch.validateAnswers(validations);
  }
}
```

## 最佳实践

### 1. 批量操作使用建议

- **合理控制批次大小**: 用户操作建议10-50个，谜题操作建议20-100个
- **设置合适的超时时间**: 避免长时间等待，影响用户体验
- **错误处理**: 批量操作失败时，提供详细的错误信息
- **进度反馈**: 对于大批量操作，提供进度指示

### 2. 缓存策略建议

- **TTL设置**: 根据数据更新频率设置合适的过期时间
- **缓存大小**: 根据内存限制设置合理的缓存大小
- **缓存键设计**: 使用有意义且唯一的缓存键
- **缓存失效**: 及时清理过期和无效的缓存

### 3. 性能优化建议

- **防抖动**: 用于用户输入、搜索等高频操作
- **节流**: 用于按钮点击、刷新等需要限制频率的操作
- **批量处理**: 用于日志记录、数据同步等可以延迟的操作
- **性能监控**: 持续监控关键操作的性能指标

### 4. 内存管理建议

- **及时释放**: 不再使用的对象要及时释放引用
- **弱引用跟踪**: 对于需要监控的对象使用弱引用
- **定期清理**: 设置合理的清理间隔
- **内存警告**: 监控内存使用情况，及时处理内存警告

## 总结

通过实施这些批量操作和性能优化功能，LogicLab项目现在具备了：

1. **高效的批量处理能力** - 支持用户、谜题的批量操作
2. **智能的缓存机制** - 减少重复计算和网络请求
3. **完善的性能监控** - 实时监控关键操作性能
4. **优化的内存管理** - 防止内存泄漏和过度使用
5. **用户体验优化** - 通过防抖动、节流等技术提升响应性

这些改进显著提升了应用的性能和用户体验，为后续的功能扩展奠定了坚实的基础。 
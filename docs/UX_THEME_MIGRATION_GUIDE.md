# UX 主题迁移指南

本指南帮助开发者从旧的 `AppTheme` 迁移到新的 `UXThemeConfig` 和更新后的 `AppTheme`。

## 迁移概述

### 主要变化

1. **引入 UXThemeConfig**：统一的设计系统基础
2. **重构 AppTheme**：基于 UXThemeConfig 的游戏特定扩展
3. **统一命名规范**：更清晰的常量命名
4. **响应式支持**：内置断点和响应式工具
5. **主题构建器**：自动生成亮色和暗色主题

## 迁移步骤

### 1. 更新导入语句

```dart
// 旧的导入
import 'package:logic_lab/core/constants/app_theme.dart';

// 新的导入
import 'package:logic_lab/core/constants/app_theme.dart';
import 'package:logic_lab/core/theme/ux_theme_config.dart';
```

### 2. 颜色常量迁移

| 旧常量 | 新常量 | 说明 |
|--------|--------|------|
| `AppTheme.primaryColor` | `UXThemeConfig.primaryBlue` | 主色调 |
| `AppTheme.secondaryColor` | `UXThemeConfig.accentGreen` | 辅助色 |
| `AppTheme.errorColor` | `UXThemeConfig.errorRed` | 错误色 |
| `AppTheme.backgroundColor` | `UXThemeConfig.backgroundPrimary` | 背景色 |
| `AppTheme.textPrimary` | `UXThemeConfig.textPrimary` | 主文本色 |
| `AppTheme.textSecondary` | `UXThemeConfig.textSecondary` | 次要文本色 |

#### 迁移示例

```dart
// 旧代码
Container(
  color: AppTheme.primaryColor,
  child: Text(
    '标题',
    style: TextStyle(color: AppTheme.textPrimary),
  ),
)

// 新代码
Container(
  color: UXThemeConfig.primaryBlue,
  child: Text(
    '标题',
    style: TextStyle(color: UXThemeConfig.textPrimary),
  ),
)
```

### 3. 尺寸常量迁移

| 旧常量 | 新常量 | 说明 |
|--------|--------|------|
| `AppSizes.paddingS` | `UXThemeConfig.spacingS` | 小间距 |
| `AppSizes.paddingM` | `UXThemeConfig.spacingL` | 中等间距 |
| `AppSizes.paddingL` | `UXThemeConfig.spacingXL` | 大间距 |
| `AppSizes.radiusM` | `UXThemeConfig.radiusM` | 中等圆角 |
| `AppSizes.buttonHeight` | `UXThemeConfig.buttonHeightMedium` | 按钮高度 |

#### 迁移示例

```dart
// 旧代码
Padding(
  padding: EdgeInsets.all(AppSizes.paddingM),
  child: Container(
    height: AppSizes.buttonHeight,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(AppSizes.radiusM),
    ),
  ),
)

// 新代码
Padding(
  padding: EdgeInsets.all(UXThemeConfig.spacingL),
  child: Container(
    height: UXThemeConfig.buttonHeightMedium,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
    ),
  ),
)
```

### 4. 动画常量迁移

| 旧常量 | 新常量 | 说明 |
|--------|--------|------|
| `AppAnimations.shortDuration` | `UXThemeConfig.durationFast` | 快速动画 |
| `AppAnimations.mediumDuration` | `UXThemeConfig.durationNormal` | 普通动画 |
| `AppAnimations.longDuration` | `UXThemeConfig.durationSlow` | 慢速动画 |
| `AppAnimations.defaultCurve` | `UXThemeConfig.curveDefault` | 默认曲线 |

#### 迁移示例

```dart
// 旧代码
AnimatedContainer(
  duration: AppAnimations.mediumDuration,
  curve: AppAnimations.defaultCurve,
  // ...
)

// 新代码
AnimatedContainer(
  duration: UXThemeConfig.durationNormal,
  curve: UXThemeConfig.curveDefault,
  // ...
)
```

### 5. 主题数据迁移

#### 旧的主题使用方式

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: AppTheme.lightTheme,
      home: HomePage(),
    );
  }
}
```

#### 新的主题使用方式

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: AppTheme.lightTheme, // 现在基于 UXThemeConfig
      darkTheme: AppTheme.darkTheme, // 新增暗色主题支持
      themeMode: ThemeMode.system,
      home: HomePage(),
    );
  }
}
```

### 6. 响应式设计迁移

#### 新增响应式功能

```dart
class ResponsiveWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(context.responsiveSpacing),
      child: Text(
        '响应式文本',
        style: TextStyle(
          fontSize: context.responsiveFontSize(UXThemeConfig.fontSizeBody),
        ),
      ),
    );
  }
}
```

#### 设备类型判断

```dart
Widget _buildContent(BuildContext context) {
  switch (context.deviceType) {
    case DeviceType.mobile:
      return _buildMobileLayout();
    case DeviceType.tablet:
      return _buildTabletLayout();
    case DeviceType.desktop:
    case DeviceType.large:
      return _buildDesktopLayout();
  }
}
```

## 游戏特定功能

### 1. 主题世界支持

```dart
// 获取森林主题
final forestTheme = AppTheme.getThemeWorldTheme('forest');

// 获取海洋主题（暗色）
final oceanDarkTheme = AppTheme.getThemeWorldTheme('ocean', isDark: true);

// 在游戏中应用
MaterialApp(
  theme: forestTheme,
  home: ForestLevelPage(),
)
```

### 2. 游戏特定尺寸

```dart
// 谜题网格
Container(
  width: context.responsivePuzzleGridSize,
  height: context.responsivePuzzleGridSize,
  child: GridView.builder(
    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 3,
      childAspectRatio: 1.0,
      crossAxisSpacing: AppTheme.puzzleGridSpacing,
      mainAxisSpacing: AppTheme.puzzleGridSpacing,
    ),
    itemBuilder: (context, index) {
      return Container(
        width: context.responsivePuzzleCellSize,
        height: context.responsivePuzzleCellSize,
        decoration: BoxDecoration(
          color: AppTheme.correctColor,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        ),
      );
    },
  ),
)
```

### 3. 游戏动画

```dart
// 星星动画
AnimatedContainer(
  duration: GameAnimations.starAnimationDuration,
  curve: GameAnimations.starAnimationCurve,
  transform: Matrix4.identity()..scale(_isAnimating ? 1.2 : 1.0),
  child: Icon(
    Icons.star,
    size: AppTheme.starSize,
    color: AppTheme.starColor,
  ),
)

// 谜题翻转动画
AnimatedSwitcher(
  duration: GameAnimations.puzzleFlipDuration,
  transitionBuilder: (child, animation) {
    return RotationTransition(
      turns: animation,
      child: child,
    );
  },
  child: _showAnswer ? _buildAnswer() : _buildQuestion(),
)
```

## 扩展主题

### 1. 创建自定义扩展

```dart
// 为特定功能扩展主题
extension PuzzleTheme on UXThemeConfig {
  // 谜题特定颜色
  static const Color puzzleCorrect = successGreen;
  static const Color puzzleIncorrect = errorRed;
  static const Color puzzleHint = accentOrange;
  static const Color puzzleNeutral = neutralGray300;
  
  // 谜题特定尺寸
  static const double puzzleCardElevation = 8.0;
  static const double puzzleBorderWidth = 2.0;
  static const double puzzleIconPadding = spacingM;
}

// 使用扩展
Container(
  decoration: BoxDecoration(
    color: PuzzleTheme.puzzleCorrect,
    borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
    border: Border.all(
      color: PuzzleTheme.puzzleCorrect,
      width: PuzzleTheme.puzzleBorderWidth,
    ),
  ),
  child: Padding(
    padding: EdgeInsets.all(PuzzleTheme.puzzleIconPadding),
    child: Icon(Icons.check),
  ),
)
```

### 2. 组件库构建

```dart
class LogicLabComponents {
  // 主要按钮
  static Widget primaryButton({
    required String text,
    required VoidCallback onPressed,
    bool isLoading = false,
  }) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: UXThemeConfig.primaryBlue,
        foregroundColor: UXThemeConfig.textOnPrimary,
        padding: EdgeInsets.symmetric(
          horizontal: UXThemeConfig.paddingButtonHorizontal * 2,
          vertical: UXThemeConfig.paddingButtonVertical,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusButton * 3),
        ),
      ),
      child: isLoading
          ? SizedBox(
              width: UXThemeConfig.iconSizeM,
              height: UXThemeConfig.iconSizeM,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation(UXThemeConfig.textOnPrimary),
              ),
            )
          : Text(text),
    );
  }

  // 游戏卡片
  static Widget gameCard({
    required Widget child,
    VoidCallback? onTap,
    bool isSelected = false,
  }) {
    return Card(
      elevation: isSelected ? 8 : 4,
      color: isSelected 
          ? UXThemeConfig.primaryBlueTint 
          : UXThemeConfig.backgroundPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusCard),
        side: isSelected
            ? BorderSide(color: UXThemeConfig.primaryBlue, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusCard),
        child: Padding(
          padding: EdgeInsets.all(UXThemeConfig.spacingL),
          child: child,
        ),
      ),
    );
  }
}
```

## 调试和验证

### 1. 主题预览页面

```dart
class ThemePreviewPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('主题预览')),
      body: ListView(
        padding: EdgeInsets.all(UXThemeConfig.spacingL),
        children: [
          _buildColorSection(),
          _buildTypographySection(),
          _buildComponentSection(),
          _buildGameElementsSection(),
        ],
      ),
    );
  }

  Widget _buildGameElementsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '游戏元素',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeHeading,
            fontWeight: UXThemeConfig.fontWeightBold,
          ),
        ),
        SizedBox(height: UXThemeConfig.spacingM),
        
        // 星星评级
        Row(
          children: List.generate(5, (index) {
            return Padding(
              padding: EdgeInsets.only(right: UXThemeConfig.spacingXS),
              child: Icon(
                Icons.star,
                size: AppTheme.starSize,
                color: index < 3 ? AppTheme.starColor : UXThemeConfig.neutralGray300,
              ),
            );
          }),
        ),
        
        SizedBox(height: UXThemeConfig.spacingM),
        
        // 游戏按钮
        LogicLabComponents.primaryButton(
          text: '开始游戏',
          onPressed: () {},
        ),
        
        SizedBox(height: UXThemeConfig.spacingM),
        
        // 游戏卡片
        LogicLabComponents.gameCard(
          child: Column(
            children: [
              Icon(
                Icons.puzzle_outlined,
                size: AppTheme.puzzleIconSize,
                color: UXThemeConfig.primaryBlue,
              ),
              SizedBox(height: UXThemeConfig.spacingS),
              Text('数字逻辑'),
            ],
          ),
          onTap: () {},
        ),
      ],
    );
  }
}
```

### 2. 主题切换测试

```dart
class ThemeTestPage extends StatefulWidget {
  @override
  _ThemeTestPageState createState() => _ThemeTestPageState();
}

class _ThemeTestPageState extends State<ThemeTestPage> {
  String _currentWorld = 'default';
  bool _isDark = false;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.getThemeWorldTheme(_currentWorld, isDark: _isDark),
      child: Scaffold(
        appBar: AppBar(
          title: Text('主题测试'),
          actions: [
            Switch(
              value: _isDark,
              onChanged: (value) => setState(() => _isDark = value),
            ),
          ],
        ),
        body: Column(
          children: [
            // 主题世界选择器
            SegmentedButton<String>(
              segments: [
                ButtonSegment(value: 'default', label: Text('默认')),
                ButtonSegment(value: 'forest', label: Text('森林')),
                ButtonSegment(value: 'ocean', label: Text('海洋')),
                ButtonSegment(value: 'space', label: Text('太空')),
              ],
              selected: {_currentWorld},
              onSelectionChanged: (selection) {
                setState(() => _currentWorld = selection.first);
              },
            ),
            
            // 测试内容
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    LogicLabComponents.gameCard(
                      child: Column(
                        children: [
                          Icon(
                            Icons.star,
                            size: AppTheme.starSize,
                            color: AppTheme.starColor,
                          ),
                          SizedBox(height: UXThemeConfig.spacingS),
                          Text('当前主题: $_currentWorld'),
                          Text('模式: ${_isDark ? "暗色" : "亮色"}'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

## 注意事项

1. **渐进式迁移**：可以逐步迁移，新旧系统可以共存
2. **测试充分**：确保所有主题变化都经过测试
3. **文档更新**：及时更新相关文档和注释
4. **性能考虑**：新的主题系统更高效，但要注意响应式计算的性能
5. **向后兼容**：游戏特定的功能仍然保留在 AppTheme 中

## 完成检查清单

- [ ] 更新所有颜色常量引用
- [ ] 迁移尺寸和间距常量
- [ ] 更新动画配置
- [ ] 测试亮色和暗色主题
- [ ] 验证响应式功能
- [ ] 测试主题世界切换
- [ ] 更新组件库
- [ ] 运行完整测试套件
- [ ] 更新文档

完成迁移后，LogicLab 将拥有更统一、更灵活、更易维护的主题系统！ 
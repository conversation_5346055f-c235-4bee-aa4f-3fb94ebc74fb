#!/bin/bash

# 🧪 LogicLab 镜像对称游戏测试脚本
# 更新时间：2024-12-19

echo "🎮 LogicLab 镜像对称游戏测试启动..."
echo "================================================"

# 检查是否在正确的目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 错误：请在LogicLab项目根目录运行此脚本"
    exit 1
fi

# 1. 环境检查
echo "📋 步骤1：检查Flutter环境..."
flutter doctor
if [ $? -ne 0 ]; then
    echo "⚠️ Flutter环境检查发现问题，但继续执行..."
fi

# 2. 项目清理和准备
echo ""
echo "🛠️ 步骤2：清理并准备项目..."
flutter clean
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ 依赖获取失败！"
    exit 1
fi

# 3. 代码生成
echo ""
echo "⚙️ 步骤3：运行代码生成..."
dart run build_runner build

if [ $? -ne 0 ]; then
    echo "⚠️ 代码生成失败，但继续执行..."
fi

# 4. 代码分析
echo ""
echo "🔍 步骤4：代码分析..."
flutter analyze

if [ $? -ne 0 ]; then
    echo "⚠️ 代码分析发现问题，但继续执行..."
fi

# 5. 单元测试
echo ""
echo "🧪 步骤5：运行单元测试..."
flutter test

if [ $? -ne 0 ]; then
    echo "⚠️ 单元测试失败，但继续执行..."
fi

# 6. 检查关键文件
echo ""
echo "📁 步骤6：检查关键文件..."

files_to_check=(
    "assets/puzzles/mirror_symmetry_sample.json"
    "assets/puzzles/mirror_symmetry_with_explanation.json"
    "lib/presentation/widgets/mirror_symmetry_widget.dart"
    "lib/data/models/puzzle.dart"
    "lib/services/puzzle_engine.dart"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file - 存在"
    else
        echo "❌ $file - 缺失"
    fi
done

# 7. 显示测试指导
echo ""
echo "📱 步骤7：准备启动应用..."
echo "================================================"
echo "🎯 测试重点："
echo "1. 应用启动和用户创建流程"
echo "2. 镜像对称游戏核心功能"
echo "3. 答案解析和教学效果"
echo "4. UI/UX响应和动画效果"
echo "5. 数据持久化和性能表现"
echo ""
echo "📋 测试步骤："
echo "1. 创建测试用户"
echo "2. 进入镜像对称游戏"
echo "3. 完成游戏并查看解析"
echo "4. 测试不同谜题类型"
echo "5. 验证数据保存"
echo ""

# 8. 询问启动方式
echo "🚀 选择启动方式："
echo "1. Debug模式（推荐用于功能测试）"
echo "2. Profile模式（用于性能测试）"
echo "3. 仅显示测试指南"
echo "4. 退出"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "🐛 启动Debug模式..."
        flutter run --debug
        ;;
    2)
        echo "⚡ 启动Profile模式..."
        flutter run --profile
        ;;
    3)
        echo "📖 请查看 docs/MIRROR_SYMMETRY_TESTING_GUIDE.md 获取详细测试指南"
        ;;
    4)
        echo "👋 测试准备完成，退出脚本"
        exit 0
        ;;
    *)
        echo "❌ 无效选择，退出脚本"
        exit 1
        ;;
esac

echo ""
echo "✅ 测试脚本执行完成！"
echo "📚 详细测试指南：docs/MIRROR_SYMMETRY_TESTING_GUIDE.md"
echo "🐛 问题反馈：请记录测试过程中发现的任何问题" 
import 'package:flutter/foundation.dart';
import 'lib/core/constants/game_asset_categories.dart';
import 'lib/core/managers/asset_manager.dart';
import 'lib/core/generators/puzzle_generator.dart';
import 'lib/examples/asset_classification_example.dart';

/// 素材分类系统测试
void main() async {
  debugPrint('🎮 LogicLab 素材分类系统测试开始');
  
  // 启用调试模式
  AssetManager.enableDebugMode();
  
  try {
    // 测试1: 基本素材分类枚举
    await testBasicEnums();
    
    // 测试2: 素材管理器功能
    await testAssetManager();
    
    // 测试3: 题目生成器功能
    await testPuzzleGenerator();
    
    // 测试4: 批量生成功能
    await testBatchGeneration();
    
    // 测试5: 运行完整示例
    await testFullExamples();
    
    debugPrint('✅ 所有测试通过！');
    
  } catch (e, stackTrace) {
    debugPrint('❌ 测试失败: $e');
    debugPrint('堆栈跟踪: $stackTrace');
  }
}

/// 测试基本枚举类型
Future<void> testBasicEnums() async {
  debugPrint('\n📋 测试1: 基本素材分类枚举');
  
  // 测试镜像对称分类
  debugPrint('镜像对称分类测试:');
  for (final clothing in ClothingType.values.take(3)) {
    debugPrint('  衣服: ${clothing.displayName} (${clothing.assetKey})');
  }
  
  for (final pattern in PatternType.values.take(3)) {
    debugPrint('  花纹: ${pattern.displayName} (${pattern.assetKey})');
  }
  
  for (final color in ColorType.values.take(3)) {
    debugPrint('  颜色: ${color.displayName} (${color.assetKey})');
  }
  
  // 测试图形推理分类
  debugPrint('图形推理分类测试:');
  for (final shape in ShapeType.values.take(3)) {
    debugPrint('  图形: ${shape.displayName} (${shape.assetKey})');
  }
  
  for (final fill in FillType.values.take(3)) {
    debugPrint('  填充: ${fill.displayName} (${fill.assetKey})');
  }
  
  // 测试空间想象分类
  debugPrint('空间想象分类测试:');
  for (final shape3d in Shape3DType.values.take(3)) {
    debugPrint('  3D形状: ${shape3d.displayName} (${shape3d.assetKey})');
  }
  
  for (final view in ViewType.values.take(3)) {
    debugPrint('  视角: ${view.displayName} (${view.assetKey})');
  }
  
  // 测试编程启蒙分类
  debugPrint('编程启蒙分类测试:');
  for (final character in CharacterType.values.take(3)) {
    debugPrint('  角色: ${character.displayName} (${character.assetKey})');
  }
  
  for (final environment in EnvironmentType.values.take(3)) {
    debugPrint('  环境: ${environment.displayName} (${environment.assetKey})');
  }
  
  debugPrint('✅ 基本枚举测试完成');
}

/// 测试素材管理器
Future<void> testAssetManager() async {
  debugPrint('\n🎨 测试2: 素材管理器功能');
  
  final assetManager = AssetManager.instance;
  
  // 测试镜像对称素材生成
  debugPrint('测试镜像对称素材生成:');
  final mirrorResult = await assetManager.generateRandomMirrorSymmetryAsset(
    difficulty: 1,
    allowedClothingTypes: [ClothingType.shirt, ClothingType.dress],
    allowedPatternTypes: [PatternType.heart, PatternType.star],
    allowedColorTypes: [ColorType.red, ColorType.blue],
  );
  
  if (mirrorResult.isSuccess) {
    final asset = mirrorResult.data!;
    debugPrint('  ✅ 生成成功: ${asset.name}');
    debugPrint('  描述: ${asset.description}');
    debugPrint('  路径: ${asset.generateAssetPath()}');
  } else {
    debugPrint('  ❌ 生成失败: ${mirrorResult.exception}');
  }
  
  // 测试图形推理素材生成
  debugPrint('测试图形推理素材生成:');
  final graphicResult = await assetManager.generateRandomGraphicPatternAsset(
    difficulty: 2,
    allowedShapeTypes: [ShapeType.circle, ShapeType.square],
    allowedColorTypes: [ColorType.red, ColorType.blue, ColorType.green],
  );
  
  if (graphicResult.isSuccess) {
    final asset = graphicResult.data!;
    debugPrint('  ✅ 生成成功: ${asset.name}');
    debugPrint('  描述: ${asset.description}');
    debugPrint('  路径: ${asset.generateAssetPath()}');
  } else {
    debugPrint('  ❌ 生成失败: ${graphicResult.exception}');
  }
  
  // 测试空间想象素材生成
  debugPrint('测试空间想象素材生成:');
  final spatialResult = await assetManager.generateRandomSpatialVisualizationAsset(
    difficulty: 2,
    allowedShapeTypes: [Shape3DType.cube, Shape3DType.pyramid],
    allowedViewTypes: [ViewType.front, ViewType.isometric],
  );
  
  if (spatialResult.isSuccess) {
    final asset = spatialResult.data!;
    debugPrint('  ✅ 生成成功: ${asset.name}');
    debugPrint('  描述: ${asset.description}');
    debugPrint('  路径: ${asset.generateAssetPath()}');
  } else {
    debugPrint('  ❌ 生成失败: ${spatialResult.exception}');
  }
  
  // 测试编程启蒙素材生成
  debugPrint('测试编程启蒙素材生成:');
  final codingResult = await assetManager.generateRandomCodingAsset(
    difficulty: 2,
    allowedCharacterTypes: [CharacterType.robot, CharacterType.knight],
    allowedEnvironmentTypes: [EnvironmentType.forest, EnvironmentType.castle],
  );
  
  if (codingResult.isSuccess) {
    final asset = codingResult.data!;
    debugPrint('  ✅ 生成成功: ${asset.name}');
    debugPrint('  描述: ${asset.description}');
    debugPrint('  角色路径: ${asset.generateCharacterAssetPath()}');
    debugPrint('  环境路径: ${asset.generateEnvironmentAssetPath()}');
  } else {
    debugPrint('  ❌ 生成失败: ${codingResult.exception}');
  }
  
  debugPrint('✅ 素材管理器测试完成');
}

/// 测试题目生成器
Future<void> testPuzzleGenerator() async {
  debugPrint('\n🧩 测试3: 题目生成器功能');
  
  final puzzleGenerator = PuzzleGenerator.instance;
  
  // 测试镜像对称题目生成
  debugPrint('测试镜像对称题目生成:');
  final mirrorPuzzleResult = await puzzleGenerator.generateMirrorSymmetryPuzzle(
    difficulty: 1,
    requiredTags: ['test'],
  );
  
  if (mirrorPuzzleResult.isSuccess) {
    final puzzle = mirrorPuzzleResult.data!;
    debugPrint('  ✅ 题目生成成功: ${puzzle.title}');
    debugPrint('  ID: ${puzzle.id}');
    debugPrint('  难度: ${puzzle.difficulty}');
    debugPrint('  预估时间: ${puzzle.estimatedTime.inSeconds}秒');
    debugPrint('  提示数量: ${puzzle.hints.length}');
    
    // 检查题目数据结构
    final data = puzzle.data;
    if (data.containsKey('options') && data['options'] is List) {
      debugPrint('  选项数量: ${data['options'].length}');
      debugPrint('  正确答案索引: ${data['correctAnswer']}');
    }
  } else {
    debugPrint('  ❌ 题目生成失败: ${mirrorPuzzleResult.exception}');
  }
  
  // 测试图形推理题目生成
  debugPrint('测试图形推理题目生成:');
  final graphicPuzzleResult = await puzzleGenerator.generateGraphicPatternPuzzle(
    difficulty: 2,
    requiredTags: ['test'],
  );
  
  if (graphicPuzzleResult.isSuccess) {
    final puzzle = graphicPuzzleResult.data!;
    debugPrint('  ✅ 题目生成成功: ${puzzle.title}');
    debugPrint('  ID: ${puzzle.id}');
    debugPrint('  难度: ${puzzle.difficulty}');
    debugPrint('  预估时间: ${puzzle.estimatedTime.inSeconds}秒');
    debugPrint('  提示数量: ${puzzle.hints.length}');
    
    // 检查题目数据结构
    final data = puzzle.data;
    if (data.containsKey('gridAssets') && data['gridAssets'] is List) {
      debugPrint('  网格素材数量: ${data['gridAssets'].length}');
      debugPrint('  网格大小: ${data['gridSize']}x${data['gridSize']}');
      debugPrint('  缺失位置: ${data['missingPosition']}');
    }
  } else {
    debugPrint('  ❌ 题目生成失败: ${graphicPuzzleResult.exception}');
  }
  
  debugPrint('✅ 题目生成器测试完成');
}

/// 测试批量生成功能
Future<void> testBatchGeneration() async {
  debugPrint('\n📦 测试4: 批量生成功能');
  
  final assetManager = AssetManager.instance;
  final puzzleGenerator = PuzzleGenerator.instance;
  
  // 测试批量素材生成
  debugPrint('测试批量素材生成:');
  final batchAssetResult = await assetManager.generateMultipleAssets<MirrorSymmetryAssetCombination>(
    () => assetManager.generateRandomMirrorSymmetryAsset(difficulty: 1),
    5,
    allowDuplicates: false,
  );
  
  if (batchAssetResult.isSuccess) {
    final assets = batchAssetResult.data!;
    debugPrint('  ✅ 批量生成成功: ${assets.length}个素材组合');
    for (int i = 0; i < assets.length; i++) {
      debugPrint('    ${i + 1}. ${assets[i].name}');
    }
  } else {
    debugPrint('  ❌ 批量生成失败: ${batchAssetResult.exception}');
  }
  
  // 测试批量题目生成
  debugPrint('测试批量题目生成:');
  final batchPuzzleResult = await puzzleGenerator.generateMultiplePuzzles(
    PuzzleType.mirrorSymmetry,
    3,
    difficulty: 1,
    requiredTags: ['batch_test'],
  );
  
  if (batchPuzzleResult.isSuccess) {
    final puzzles = batchPuzzleResult.data!;
    debugPrint('  ✅ 批量生成成功: ${puzzles.length}个题目');
    for (int i = 0; i < puzzles.length; i++) {
      debugPrint('    ${i + 1}. ${puzzles[i].title} (${puzzles[i].estimatedTime.inSeconds}秒)');
    }
  } else {
    debugPrint('  ❌ 批量生成失败: ${batchPuzzleResult.exception}');
  }
  
  debugPrint('✅ 批量生成测试完成');
}

/// 测试完整示例
Future<void> testFullExamples() async {
  debugPrint('\n🎯 测试5: 完整示例运行');
  
  try {
    // 运行基本用法演示
    await AssetClassificationDemo.basicUsageDemo();
    
    // 运行高级用法演示
    await AssetClassificationDemo.advancedUsageDemo();
    
    debugPrint('✅ 完整示例测试完成');
    
  } catch (e) {
    debugPrint('❌ 完整示例测试失败: $e');
  }
}

/// 性能测试
Future<void> performanceTest() async {
  debugPrint('\n⚡ 性能测试');
  
  final assetManager = AssetManager.instance;
  final stopwatch = Stopwatch()..start();
  
  // 测试大量素材生成的性能
  final results = <MirrorSymmetryAssetCombination>[];
  for (int i = 0; i < 100; i++) {
    final result = await assetManager.generateRandomMirrorSymmetryAsset(difficulty: 2);
    if (result.isSuccess) {
      results.add(result.data!);
    }
  }
  
  stopwatch.stop();
  debugPrint('生成100个素材组合耗时: ${stopwatch.elapsedMilliseconds}ms');
  debugPrint('平均每个素材组合: ${stopwatch.elapsedMilliseconds / 100}ms');
  debugPrint('成功率: ${results.length}/100 (${results.length}%)');
  
  // 检查重复率
  final uniqueNames = results.map((e) => e.name).toSet();
  debugPrint('唯一组合数: ${uniqueNames.length}');
  debugPrint('重复率: ${((results.length - uniqueNames.length) / results.length * 100).toStringAsFixed(1)}%');
}

/// 统计信息
void printStatistics() {
  debugPrint('\n📊 素材分类统计信息');
  
  final stats = {
    '衣服类型': ClothingType.values.length,
    '花纹类型': PatternType.values.length,
    '颜色类型': ColorType.values.length,
    '图形类型': ShapeType.values.length,
    '填充类型': FillType.values.length,
    '大小类型': SizeType.values.length,
    '3D形状类型': Shape3DType.values.length,
    '视角类型': ViewType.values.length,
    '材质类型': MaterialType.values.length,
    '角色类型': CharacterType.values.length,
    '角色状态': CharacterState.values.length,
    '环境类型': EnvironmentType.values.length,
  };
  
  for (final entry in stats.entries) {
    debugPrint('${entry.key}: ${entry.value}种');
  }
  
  // 计算理论组合数
  final mirrorCombinations = ClothingType.values.length * 
                             PatternType.values.length * 
                             ColorType.values.length;
  final graphicCombinations = ShapeType.values.length * 
                              FillType.values.length * 
                              ColorType.values.length * 
                              SizeType.values.length;
  final spatialCombinations = Shape3DType.values.length * 
                              ViewType.values.length * 
                              MaterialType.values.length * 
                              ColorType.values.length;
  final codingCombinations = CharacterType.values.length * 
                             CharacterState.values.length * 
                             EnvironmentType.values.length;
  
  debugPrint('\n理论组合数量:');
  debugPrint('镜像对称: $mirrorCombinations');
  debugPrint('图形推理: $graphicCombinations');
  debugPrint('空间想象: $spatialCombinations');
  debugPrint('编程启蒙: $codingCombinations');
  debugPrint('总计: ${mirrorCombinations + graphicCombinations + spatialCombinations + codingCombinations}');
} 